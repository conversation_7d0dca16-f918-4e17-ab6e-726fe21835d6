from typing import Dict

import streamlit as st

# ─────────────────────────────────────────────────────────────────────────────
# 🔗 Internal imports ---------------------------------------------------------
# ─────────────────────────────────────────────────────────────────────────────
from streamlit_app.constants import INDEXER_BASE_URL, LOADER_BASE_URL, QUERY_BASE_URL
from streamlit_app.utils.api_utils import send_json_request
from streamlit_app.utils.auth_utils import ensure_login

token, headers, roles = ensure_login()

# ─────────────────────────────────────────────────────────────────────────────
# ⚙️  Settings ---------------------------------------------------------------
# ─────────────────────────────────────────────────────────────────────────────
PAGE_TITLE = "RAG Admin | _fbeta"
PAGE_ICON = "📊"

st.set_page_config(
    page_title=PAGE_TITLE,
    page_icon=PAGE_ICON,
    layout="wide",
    initial_sidebar_state="expanded",
)

# ─────────────────────────────────────────────────────────────────────────────
# 🏥 Service‑health helpers --------------------------------------------------
# ─────────────────────────────────────────────────────────────────────────────
# Modified SERVICE_ENDPOINTS to include the URL and the expected status string
SERVICE_ENDPOINTS: Dict[str, Dict[str, Dict[str, str]]] = {
    "Loader Service": {
        "live": {"url": f"{LOADER_BASE_URL}/live", "expected_status": "alive"},
        "ready": {"url": f"{LOADER_BASE_URL}/ready", "expected_status": "ready"},
    },
    "Indexer Service": {
        "live": {"url": f"{INDEXER_BASE_URL}/live", "expected_status": "alive"},
        "ready": {"url": f"{INDEXER_BASE_URL}/ready", "expected_status": "ready"},
    },
    "Query Service": {
        "live": {"url": f"{QUERY_BASE_URL}/live", "expected_status": "alive"},
        "ready": {"url": f"{QUERY_BASE_URL}/ready", "expected_status": "ready"},
    },
}


# Modified check_service_health to accept an expected status value
def check_service_health(url: str, expected_json_status_value: str) -> bool:
    """
    Return True if service responds with 200 OK and the JSON response
    contains `{"status": expected_json_status_value}`.
    """
    try:
        response = send_json_request(url, method="get", headers=headers)
        # For /ready endpoint, a 503 (as per FastAPI example if not ready) will result in status_code != 200
        # and thus correctly return False.
        # If /ready returns 200 but {"status": "not_ready"}, it will also correctly return False.
        return response.status_code == 200 and response.json().get("status") == expected_json_status_value
    except Exception:
        return False


# ─────────────────────────────────────────────────────────────────────────────
# 🖥️  Main layout -----------------------------------------------------------
# ─────────────────────────────────────────────────────────────────────────────
# Header with big logo & title
col_logo, col_title = st.columns([1, 4], gap="large")

with col_logo:
    try:
        st.image("streamlit_app/images/fbeta-logo.png", use_container_width=True)
    except Exception:  # More general exception for image loading
        st.write("_(logo missing – add `fbeta_logo.png`)_")

with col_title:
    st.markdown("# RAG Admin Dashboard")
    st.markdown(
        "Empowering **_fbeta** to manage vector collections, documents and RAG evaluation workflows with ease.",
        unsafe_allow_html=True,
    )

st.markdown("---")

# Quick‑start / What‑can‑I‑do‑here section
st.subheader("🔎 What you can do in this app")

st.markdown(
    """
* **Collection** – create, list, alter or delete document collections and tweak advanced embedding / chunking settings.
* **Document** – upload, preview & sync source documents into a selected collection.
* **URL** – ingest content directly from web pages.
* **Session / Query** – inspect user queries & session history, drill‑down into retrieved chunks.
* **DB viewer** – raw pgvector tables for nerd‑level debugging.
* **RAG evaluation** – run automated tests & view evaluation metrics.
* **Token usage** – monitor LLM token consumption per service.
    """,
    unsafe_allow_html=True,
)

st.markdown("---")

# Live service status cards
st.subheader("📡 Service health")

health_cols = st.columns(len(SERVICE_ENDPOINTS))

for (service_name, checks), col in zip(SERVICE_ENDPOINTS.items(), health_cols):
    live_ok = check_service_health(checks["live"]["url"], checks["live"]["expected_status"])
    ready_ok = check_service_health(checks["ready"]["url"], checks["ready"]["expected_status"])

    live_status = "✅ Alive" if live_ok else "❌ Down"
    ready_status = "✅ Ready" if ready_ok else "❌ Not Ready"

    col.metric(label=service_name, value=live_status, delta=ready_status)


st.markdown(
    "<small>🚩Status is fetched live from each service's respective endpoint every page load.</small>",
    unsafe_allow_html=True,
)
