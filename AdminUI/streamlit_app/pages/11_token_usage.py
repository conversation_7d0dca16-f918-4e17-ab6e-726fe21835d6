from typing import Any, Dict, List, Optional
from urllib.parse import urlencode

import pandas as pd
import plotly.express as px
import streamlit as st
from streamlit_app.constants import LOADER_BASE_URL
from streamlit_app.utils.api_utils import send_json_request
from streamlit_app.utils.auth_utils import ensure_login

# --- Auth and base config ---
token, headers, roles = ensure_login()
TOKEN_API_URL = LOADER_BASE_URL

PAGE_TITLE = "💰 Token Usage Analytics"
st.set_page_config(page_title=PAGE_TITLE, page_icon="💰", layout="wide")
st.markdown(f"# {PAGE_TITLE}")
st.markdown("---")


# --- Helpers ---
def _build_query(params: Dict[str, Any]) -> str:
    """URL-encode non-empty params. Booleans become 'true'/'false'."""
    cleaned: Dict[str, Any] = {}
    for k, v in params.items():
        if v is None:
            continue
        if isinstance(v, bool):
            cleaned[k] = str(v).lower()
        else:
            cleaned[k] = v
    return urlencode(cleaned, doseq=False)


def _api_get_json(url: str) -> Optional[Dict[str, Any]]:
    try:
        resp = send_json_request(url, method="get", headers=headers)
        if resp.status_code == 200:
            return resp.json()
        st.error(f"Request failed: {resp.status_code} - {url}")
    except Exception as e:
        st.error(f"Request error: {e}")
    return None


def fetch_emails() -> List[str]:
    """Fetch distinct user emails from LoaderService."""
    data = _api_get_json(f"{TOKEN_API_URL}/admin/emails")
    if data and data.get("success"):
        return data.get("data", {}).get("emails", [])
    return []


def fetch_usage_total(
    interval_seconds: int = -1, user_email: Optional[str] = None, llm_model: Optional[str] = None, breakdown: bool = False, group_by: Optional[str] = None
) -> Optional[Any]:
    """Fetch token usage from LoaderService.

    Returns either a numeric total or a breakdown list depending on parameters.
    """
    params: Dict[str, Any] = {"interval_seconds": interval_seconds}
    if user_email:
        params["user_emails"] = user_email
    if llm_model:
        params["llm_models"] = llm_model
    if breakdown:
        params["breakdown"] = True
        params["group_by"] = group_by or "day"
    url = f"{TOKEN_API_URL}/admin/token_usage?{_build_query(params)}"
    data = _api_get_json(url)
    return data.get("data", {}).get("result") if data else None


def fetch_cost_total(
    interval_seconds: int = -1, user_email: Optional[str] = None, llm_model: Optional[str] = None, breakdown: bool = False, group_by: Optional[str] = None
) -> Optional[Any]:
    """Fetch token cost from LoaderService.

    Returns either a numeric total or a breakdown list depending on parameters.
    """
    params: Dict[str, Any] = {"interval_seconds": interval_seconds}
    if user_email:
        params["user_emails"] = user_email
    if llm_model:
        params["llm_models"] = llm_model
    if breakdown:
        params["breakdown"] = True
        params["group_by"] = group_by or "day"
    url = f"{TOKEN_API_URL}/admin/token_cost?{_build_query(params)}"
    data = _api_get_json(url)
    return data.get("data", {}).get("result") if data else None


def fetch_users_aggregates() -> Dict[str, Any]:
    """Build per-user aggregates by iterating emails and fetching per-email totals (no breakdown)."""
    emails = fetch_emails()
    users: List[Dict[str, Any]] = []
    for email in emails:
        usage_val = fetch_usage_total(interval_seconds=-1, user_email=email)
        cost_val = fetch_cost_total(interval_seconds=-1, user_email=email)
        users.append(
            {
                "user_email": email,
                "total_token_usage": int(usage_val or 0),
                "total_token_cost": float(cost_val or 0.0),
            }
        )
    users.sort(key=lambda u: u["total_token_usage"], reverse=True)
    return {"users": users, "total_users": len(users)}


# --- Sidebar ---
st.sidebar.header("🔧 Analytics Dashboard")
analysis_type = st.sidebar.radio(
    "📊 Analysis Type",
    options=["System Overview", "User Deep Dive"],
)
if st.sidebar.button("🔄 Refresh Data", use_container_width=True):
    st.rerun()


# --- System Overview ---
if analysis_type == "System Overview":
    st.subheader("🌍 High-level Overview")

    with st.spinner("Loading aggregates..."):
        aggregates = fetch_users_aggregates()
        total_usage = fetch_usage_total()  # all-time
        total_cost = fetch_cost_total()  # all-time

    users = aggregates.get("users", [])
    total_users = aggregates.get("total_users", 0)

    active_users = [u for u in users if u["total_token_usage"] > 0]

    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("👥 Total Users", f"{total_users}", f"{len(active_users)} active")
    with col2:
        st.metric("🔢 Total Tokens", f"{(total_usage or 0):,}")
    with col3:
        st.metric("💰 Total Cost", f"${(total_cost or 0.0):.4f}")
    with col4:
        avg_cpt = (total_cost or 0.0) / (total_usage or 1) if total_usage else 0.0
        st.metric("⚡ Avg Cost/Token", f"${avg_cpt:.6f}")

    st.markdown("---")

    # Top users by usage
    df = pd.DataFrame(users)
    if not df.empty:
        top10 = df.nlargest(10, "total_token_usage")
        fig = px.bar(
            top10,
            y="user_email",
            x="total_token_usage",
            title="🥇 Top 10 Users by Token Usage",
            labels={"total_token_usage": "Total Tokens", "user_email": "User"},
            color="total_token_usage",
            color_continuous_scale="Blues",
            orientation="h",
        )
        fig.update_layout(height=480, showlegend=False)
        fig.update_traces(texttemplate="%{x:,}", textposition="outside")
        st.plotly_chart(fig, use_container_width=True)

    # Summary table
    if users:
        table_df = pd.DataFrame(users)
        table_df["cost_per_token"] = table_df.apply(
            lambda r: (r["total_token_cost"] / r["total_token_usage"]) if r["total_token_usage"] else 0.0,
            axis=1,
        ).round(6)
        table_df.rename(
            columns={
                "user_email": "User Email",
                "total_token_usage": "Total Tokens",
                "total_token_cost": "Total Cost",
                "cost_per_token": "Cost/Token",
            },
            inplace=True,
        )
        st.dataframe(table_df, hide_index=True, use_container_width=True)


# --- User Deep Dive ---
elif analysis_type == "User Deep Dive":
    st.subheader("🔍 Advanced per-user analytics with filters")

    # Sidebar filters
    with st.spinner("Loading emails..."):
        emails = fetch_emails()
        if not emails:
            aggregates = fetch_users_aggregates()
            emails = [u["user_email"] for u in aggregates.get("users", [])]

    if not emails:
        st.warning("No users found.")
    else:
        selected_email = st.sidebar.selectbox("👤 Select User", options=sorted(emails))

        time_options = {
            "All Time": -1,
            "Last 24 Hours": 86400,
            "Last 7 Days": 604800,
            "Last 30 Days": 2592000,
            "Last Hour": 3600,
        }
        selected_time_label = st.sidebar.selectbox("⏱️ Time Period", options=list(time_options.keys()))
        interval_seconds = time_options[selected_time_label]

        use_model_filter = st.sidebar.checkbox("Filter by Model", value=False)
        selected_model = None
        if use_model_filter:
            selected_model = st.sidebar.selectbox(
                "🤖 Model",
                options=["gpt-4", "gpt-4.1", "gpt-4o", "gpt-4o-2", "gpt-4o-mini"],
            )

        show_breakdown = st.sidebar.checkbox("Show Time Breakdown", value=False)
        group_by = st.sidebar.radio("Group By", options=["day", "hour"], horizontal=True) if show_breakdown else None

        # Fetch data
        with st.spinner("Loading user analytics..."):
            usage_result = fetch_usage_total(
                interval_seconds=interval_seconds,
                user_email=selected_email,
                llm_model=selected_model,
                breakdown=show_breakdown,
                group_by=group_by,
            )
            cost_result = fetch_cost_total(
                interval_seconds=interval_seconds,
                user_email=selected_email,
                llm_model=selected_model,
                breakdown=show_breakdown,
                group_by=group_by,
            )

        st.markdown(f"### 📊 Analysis for: `{selected_email}`")
        filt_desc = f"Period: {selected_time_label}"
        if selected_model:
            filt_desc += f" | Model: {selected_model}"
        st.caption(filt_desc)

        # Render totals or breakdown
        if show_breakdown:
            try:
                # Normalize usage
                if isinstance(usage_result, list) and usage_result:
                    if isinstance(usage_result[0], list):
                        usage_df = pd.DataFrame(usage_result, columns=["bucket", "usage"])  # type: ignore[arg-type]
                    else:
                        usage_df = pd.DataFrame(usage_result)
                        if not usage_df.empty:
                            usage_df.rename(columns={"value": "usage"}, inplace=True)
                    if "bucket" not in usage_df.columns:
                        usage_df = pd.DataFrame(columns=["bucket", "usage"])
                else:
                    usage_df = pd.DataFrame(columns=["bucket", "usage"])

                # Normalize cost
                if isinstance(cost_result, list) and cost_result:
                    if isinstance(cost_result[0], list):
                        cost_df = pd.DataFrame(cost_result, columns=["bucket", "cost"])  # type: ignore[arg-type]
                    else:
                        cost_df = pd.DataFrame(cost_result)
                        if not cost_df.empty:
                            cost_df.rename(columns={"value": "cost"}, inplace=True)
                    if "bucket" not in cost_df.columns:
                        cost_df = pd.DataFrame(columns=["bucket", "cost"])
                else:
                    cost_df = pd.DataFrame(columns=["bucket", "cost"])

                if usage_df.empty and cost_df.empty:
                    st.info("There is no data for the selected filters.")
                else:
                    df = pd.merge(usage_df, cost_df, on="bucket", how="outer").fillna(0)
                    if df.empty or "bucket" not in df.columns:
                        st.info("There is no data for the selected filters.")
                    else:
                        t1, t2 = st.tabs(["📊 Usage", "💰 Cost"])
                        with t1:
                            fig_u = px.bar(df, x="bucket", y="usage", title="Token Usage by Time", labels={"bucket": group_by or "time"})
                            fig_u.update_layout(height=400)
                            st.plotly_chart(fig_u, use_container_width=True)
                            st.dataframe(df[["bucket", "usage"]], hide_index=True, use_container_width=True)
                        with t2:
                            fig_c = px.bar(df, x="bucket", y="cost", title="Token Cost by Time", labels={"bucket": group_by or "time"})
                            fig_c.update_layout(height=400)
                            st.plotly_chart(fig_c, use_container_width=True)
                            st.dataframe(df[["bucket", "cost"]], hide_index=True, use_container_width=True)
            except Exception:
                st.info("There is no data for the selected filters.")
        else:
            total_usage = int(usage_result or 0)
            total_cost = float(cost_result or 0.0)
            col1, col2 = st.columns(2)
            with col1:
                st.metric("🔢 Filtered Tokens", f"{total_usage:,}")
            with col2:
                st.metric("💰 Filtered Cost", f"${total_cost:.4f}")


# --- Footer ---
st.markdown("---")
st.caption("Real-time data from LoaderService")
