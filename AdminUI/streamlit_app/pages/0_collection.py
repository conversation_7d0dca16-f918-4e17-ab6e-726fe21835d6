from streamlit_app.utils.auth_utils import ensure_login

token, headers, roles = ensure_login()
from enum import Enum

import pandas as pd
import requests
import streamlit as st

# Available search methods (only those implemented in VectorDB)
AVAILABLE_SEARCH_METHODS = [
    "similarity",  # Similarity Search (default)
    "mmr",  # Maximal Marginal Relevance
    "keyword",  # Keyword
    "hybrid",  # Hybrid
    "intelligent_hybrid",  # Intelligent Hybrid
    "query_expansion",  # Query Expansion
    "elasticsearch",  # Elasticsearch
    "elasticsearch_hybrid",  # Elasticsearch Hybrid
    "azure_ai_web",  # Azure AI Web
]
DEFAULT_SEARCH_METHOD = "similarity"

from streamlit_app.constants import GRAPHRAG_BASE_URL, INDEXER_BASE_URL, LOADER_BASE_URL
from streamlit_app.enums.llm import ReRankWebLLMModels
from streamlit_app.utils.api_utils import send_json_request
from streamlit_app.utils.db_utils import is_valid_uuid

# API endpoint
LOADER_URL = f"{LOADER_BASE_URL}/collection"
INDEXER_URL = f"{INDEXER_BASE_URL}/collection"

# custom system prompt character limit
custom_system_prompt_min_char_limit = 10
custom_system_prompt_max_char_limit = (
    400000  # as most collections use gpt-4o or gpt-4o-mini the max length is set to ~80% of the approximated max context length: 128.000 * 4 * 0.8
)

PAGE_TITLE = "Collection"
st.set_page_config(page_title=PAGE_TITLE, page_icon="📈")

st.markdown(f"# {PAGE_TITLE}")

st.write("""Send request to collection endpoint.""")
st.title("Test Collection Service")

tab_list, tab_add, tab_delete, tab_alter = st.tabs(["list collections", "add collection", "delete collection", "Alter collection"])

# ------------------------------------------------------------------#
# Visibility role (who may see this collection?)
# ------------------------------------------------------------------#
VISIBILITY_OPTIONS = {
    "basic": "basic – visible to basic, expert & admin users",
    "expert": "expert – visible to expert & admin users",
    "admin": "admin – visible ONLY to admin users",
}

# Highest role of the creator decides the default
if "admin" in roles:
    default_role_key = "admin"
elif "expert" in roles:
    default_role_key = "expert"
else:
    default_role_key = "basic"


def get_collection_options():
    """Fetch collection options from the API."""
    try:
        response = send_json_request(f"{LOADER_URL}s", method="get", headers=headers)
        if response.status_code == 200:
            collections = response.json().get("collections", [])
            return [(f"{col['name']} - {col['id']}", col["id"]) for col in collections]
    except Exception as e:
        st.warning(f"Failed to fetch collections: {e}")
    return []


def get_collection_details(collection_id: str):
    """Fetch detailed data for a single collection."""
    try:
        response = send_json_request(f"{LOADER_URL}?collection_id={collection_id}", method="get", headers=headers)
        if response.status_code == 200:
            return response.json()
    except Exception as e:
        st.warning(f"Failed to fetch collection details: {e}")
    return {}


def get_collection_token_usage(collection_id: str) -> dict:
    """Call the token usage API endpoint for a specific collection."""
    try:
        response = send_json_request(f"{LOADER_URL}/{collection_id}/token-usage", method="get", headers=headers)
        if response.status_code == 200:
            return response.json()
        else:
            st.warning(f"Token usage fetch failed for {collection_id}: {response.status_code}")
    except Exception as e:
        st.warning(f"Token usage error for {collection_id}: {e}")
    return {}


def get_graphrag_collections():
    """Fetch GraphRAG collections from the GraphRAG service."""
    try:
        response = requests.get(f"{GRAPHRAG_BASE_URL}/collections", headers=headers)
        if response.status_code == 200:
            collections_data = response.json().get("collections", [])
            # Convert GraphRAG collections to the same format as regular collections
            graphrag_collections = []
            for collection_name in collections_data:
                # Remove "collection_" prefix if present for display
                display_name = collection_name.replace("collection_", "") if collection_name.startswith("collection_") else collection_name
                graphrag_collections.append(
                    {
                        "id": f"graphrag_{collection_name}",  # Add prefix to avoid ID conflicts
                        "name": f"{display_name} (GraphRAG)",
                        "description": "GraphRAG Knowledge Graph Collection",
                        "type": "GraphRAG",
                        "sessions_count": "N/A",
                        "documents_count": "N/A",
                        "created_at": "N/A",
                        "updated_at": "N/A",
                        "embedding_model": "N/A",
                        "llm_model": "N/A",
                        "query_name": display_name,  # For consistency with query page
                        "custom_config": {"query_name": display_name},
                        "sessions": [],
                        "documents": [],
                        "urls": [],
                    }
                )
            return graphrag_collections
    except Exception as e:
        st.warning(f"Failed to fetch GraphRAG collections: {e}")
    return []


with tab_list:
    # ── Place buttons side by side ───────────────────────────────
    col1, col2 = st.columns(2)
    send_clicked = col1.button("Send Request", key="button_list")
    see_clicked = col2.button("See Token Availability", key="button_token")

    # ── 1) Fetch collections (Send Request) ─────────────────────
    if send_clicked:
        try:
            with st.spinner("Fetching collections…"):
                # Fetch regular collections
                response = send_json_request(f"{LOADER_URL}s", method="get", headers=headers)
                regular_collections = []
                if response.status_code == 200:
                    regular_collections = response.json().get("collections", [])

                # Fetch GraphRAG collections
                graphrag_collections = get_graphrag_collections()

                # Combine both types of collections
                all_collections = regular_collections + graphrag_collections

            if response.status_code == 200:
                st.success(f"Collections fetched successfully! ({len(regular_collections)} regular + {len(graphrag_collections)} GraphRAG)")
                st.session_state["collections_raw"] = all_collections
            else:
                st.error("Failed to fetch collections from the API.")
        except Exception as e:
            st.error(f"Unexpected error while fetching collections: {e}")

    # ── 2) Show collection table (if available) ─────────────────
    if "collections_raw" in st.session_state:
        data = st.session_state["collections_raw"]
        custom_config_keys = set()

        for entry in data:
            entry["sessions_count"] = len(entry.get("sessions", []))
            entry["documents_count"] = len(entry.get("documents", []))
            # Set type if not already set (for regular collections)
            if "type" not in entry:
                entry["type"] = "Regular"
            custom_config_keys.update(entry.get("custom_config", {}).keys())

        df = pd.DataFrame(data)
        for key in custom_config_keys:
            df[key] = df["custom_config"].apply(lambda x, k=key: x.get(k))
        df.drop(columns=["custom_config", "sessions", "documents", "urls"], errors="ignore", inplace=True)

        # Reorder columns to show type early
        if "type" in df.columns:
            cols = df.columns.tolist()
            cols.remove("type")
            # Insert type after name
            name_idx = cols.index("name") if "name" in cols else 0
            cols.insert(name_idx + 1, "type")
            df = df[cols]

        st.markdown("### 📚 Collections")
        if not df.empty:
            st.dataframe(df)
        else:
            st.info("No collections available.")

    # ── 3) Show token-usage table (only if "See Token Availability" clicked) ─
    if see_clicked:
        if "collections_raw" not in st.session_state:
            st.warning("First click **Send Request** to load the collections.")
        else:
            token_info_rows = []
            for entry in st.session_state["collections_raw"]:
                # Skip GraphRAG collections for token usage (they don't support this API)
                if entry.get("type") == "GraphRAG":
                    continue

                token_usage = get_collection_token_usage(entry["id"])
                if token_usage:
                    token_info_rows.append(
                        {
                            "Name": entry["name"],
                            "Model": token_usage["llm_model"],
                            "Context Limit": token_usage["context_limit"],
                            "System Prompt Tokens": token_usage["system_prompt_tokens"],
                            "Top-K": token_usage["top_k"],
                            "Chunk Size (tokens)": token_usage["chunk_size"],
                            "Chunk Tokens Total": token_usage["chunk_tokens_total"],
                            "Total Used": token_usage["total_used"],
                            "Available for Input": token_usage["available_for_input"],
                        }
                    )

            if token_info_rows:
                st.markdown("### 🧠 Context Usage per Collection (Prompt + Top-K Chunks)")
                st.info("💡 Note: GraphRAG collections are excluded from token usage analysis")
                token_df = pd.DataFrame(token_info_rows)
                st.dataframe(token_df)
            else:
                st.info("No token-usage data returned for the current collections.")


EMBEDDING_MODELS = ["text-embedding-ada-002", "bge-m3"]
LLM_MODELS = ["gpt-4", "gpt-4o", "gpt-4o-mini", "gpt-4o-2", "Phi-4", "Meta-Llama-3.1-70B-Instruct", "Mistral-8B"]

# ---------------- Search methods ----------------------


class SearchMethod(str, Enum):
    """Enum of search strategies supported by backend."""

    SIMILARITY = "similarity"
    MMR = "mmr"
    KEYWORD = "keyword"
    HYBRID = "hybrid"
    BM25 = "bm25"
    FUSED = "fused"
    RERANK = "rerank"
    ELASTICSEARCH = "elastic search"


AVAILABLE_SEARCH_METHODS = [m.value for m in SearchMethod]

CHAT_HISTORY_TTL_OPTIONS = {
    "1 day": 60 * 60 * 24,
    "1 week": 60 * 60 * 24 * 7,
    "2 weeks": 60 * 60 * 24 * 14,
    "3 weeks": 60 * 60 * 24 * 21,
    "1 month": 60 * 60 * 24 * 30,
    "6 months": 60 * 60 * 24 * 180,
    "1 year": 60 * 60 * 24 * 365,
}


class ChunkingStrategy(str, Enum):
    """Supported chunking strategies."""

    RECURSIVE = "recursive"
    TOKEN = "token"
    HEADLINE = "headline"
    MARKDOWN = "markdown"


with tab_add:
    is_valid = True
    name = st.text_input("collection name")
    description = st.text_input("collection description", value="")
    embedding_model_selection = st.selectbox("Embedding model", EMBEDDING_MODELS, index=0, key="emb_model")
    llm_model_selection = st.selectbox("LLM", LLM_MODELS, index=0, key="llm_model")
    method_selection = st.selectbox("Search Method", AVAILABLE_SEARCH_METHODS, index=AVAILABLE_SEARCH_METHODS.index(DEFAULT_SEARCH_METHOD), key="method_selection")
    custom_system_prompt = st.text_area("Custom System Prompt (optional)", value="", height=150, help="Define custom instructions for the AI. Supports markdown formatting.")
    custom_prompt_length_limit = st.number_input(
        "System Prompt Max Length (chars)",
        min_value=custom_system_prompt_min_char_limit,
        max_value=custom_system_prompt_max_char_limit,
        value=custom_system_prompt_max_char_limit,
        key="custom_prompt_limit_add",
        help="Maximum character length allowed for the system prompt",
    )

    reranking_on = st.radio(
        "RERANK",
        options=["ON", "OFF"],
        index=1,
        horizontal=True,
        key="reranking_on_add",
        help="Reranking improves search results by scoring and reordering retrieved documents based on relevance",
    )
    if reranking_on == "ON":
        reranker_llm_model = st.selectbox("Reranking LLM Model", [m.value for m in ReRankWebLLMModels], index=0, key="reranker_llm_model_add")

    is_memory_on = st.radio(
        "Query History (Memory)",
        options=["ON", "OFF"],
        index=1,
        horizontal=True,
        key="memory_on",
        help="Enables the system to remember previous queries and use them for context in new responses",
    )
    if is_memory_on == "ON":
        ttl_label = st.selectbox("Chat History TTL", list(CHAT_HISTORY_TTL_OPTIONS.keys()), index=4, key="ttl_add")  # default: 1 month
        query_history_ttl = CHAT_HISTORY_TTL_OPTIONS[ttl_label]

    is_web_search_on = st.radio(
        "Web Search",
        options=["ON", "OFF"],
        index=1,
        horizontal=True,
        key="web_search_add",
        help="Allows the AI to search the web for up-to-date information when answering queries",
    )
    if is_web_search_on == "ON":
        web_search_llm_model = st.selectbox("Web Search LLM Model", [m.value for m in ReRankWebLLMModels], index=0, key="web_search_llm_model_add")

    is_slash_commands_on = st.radio(
        "Slash Commands",
        options=["ON", "OFF"],
        index=0,
        horizontal=True,
        key="slash_commands_add",
        help="Enables special commands starting with '/' that trigger specific actions or behaviors",
    )
    visibility_role = st.selectbox(
        "Visibility role",
        options=list(VISIBILITY_OPTIONS.keys()),
        format_func=lambda k: VISIBILITY_OPTIONS[k],
        index=list(VISIBILITY_OPTIONS.keys()).index(default_role_key),
        key="visibility_role_add",
    )

    # Advanced config
    st.markdown("#### Advanced (Optional):")
    col1, col2, col3 = st.columns(3)
    with col1:
        chunk_size = st.number_input(
            "Chunk Size", min_value=0, value=1000, key="chunk_size_add", help="Size of text chunks for processing (chars for recursive/headline, tokens for token strategy)"
        )
    with col2:
        chunk_overlap = st.number_input(
            "Chunk Overlap",
            min_value=0,
            value=50,
            key="chunk_overlap_add",
            help="Overlap between consecutive chunks to maintain context (chars for recursive/headline, tokens for token strategy)",
        )
    with col3:
        chunker_strategy = st.selectbox(
            "Chunking Strategy",
            options=[s.value for s in ChunkingStrategy],
            index=0,
            key="chunker_strategy_add",
            help="Method used to split documents into chunks: recursive (default), token-based, headline-based, or markdown-based",
        )

    custom_config = {}
    if embedding_model_selection:
        custom_config["embedding_model"] = embedding_model_selection
    if llm_model_selection:
        custom_config["llm_model"] = llm_model_selection
    if method_selection:
        custom_config["collection_search_method"] = method_selection
    if chunk_size > 0:
        custom_config["chunk_size"] = chunk_size
    if chunk_overlap > 0:
        custom_config["chunk_overlap"] = chunk_overlap
    custom_config["chunking_strategy"] = chunker_strategy
    custom_config["is_memory_on"] = is_memory_on == "ON"
    custom_config["reranking_on"] = reranking_on == "ON"
    custom_config["is_web_search_on"] = is_web_search_on == "ON"
    custom_config["is_slash_commands_on"] = is_slash_commands_on == "ON"
    custom_config["role"] = visibility_role
    if custom_config["is_memory_on"]:
        custom_config["query_history_ttl"] = query_history_ttl
    if custom_config["reranking_on"]:
        custom_config["reranker_small_llm_model"] = reranker_llm_model
    if custom_config["is_web_search_on"]:
        custom_config["web_search_llm_model"] = web_search_llm_model

    if custom_system_prompt != "":
        max_len = custom_prompt_length_limit or custom_system_prompt_max_char_limit
        if len(custom_system_prompt) < custom_system_prompt_min_char_limit:
            st.warning(f"System prompt is too short. Min: {custom_system_prompt_min_char_limit}")
            is_valid = False
        elif len(custom_system_prompt) > max_len:
            st.warning(f"System prompt is too long. Max: {max_len}")
            is_valid = False
        else:
            custom_config["system_prompt"] = custom_system_prompt
            custom_config["system_prompt_length_limit"] = custom_prompt_length_limit

    if st.button("Send Request", key="button_add"):
        if not name.strip():
            st.warning("Unable to create! Reason: Collection name can not be empty.")
        elif " " in name.strip():
            st.warning("Unable to create! Reason: Collection name can not contain space character.")
        elif not is_valid:
            st.warning("Fix validation errors before proceeding.")
        else:
            try:
                json_data = {
                    "name": name,
                    "description": description,
                    "custom_config": custom_config,
                }
                with st.spinner("API call.."):
                    response = send_json_request(f"{LOADER_URL}", json_data, headers=headers)
                if isinstance(response, requests.Response) and response.status_code == 200:
                    st.success("Request Sent Successfully!")
                    st.json(response.json())
                else:
                    st.error(response.text)
            except SyntaxError:
                st.error("Invalid JSON format. Please enter a valid JSON.")


with tab_delete:
    collection_options = get_collection_options()
    selected_option = st.selectbox("Select collection to delete", collection_options, format_func=lambda x: x[0])
    collection_id = selected_option[1] if selected_option else ""

    if st.button("Send Request", key="button_delete"):
        if not collection_id:
            st.warning("Unable to delete! Please select a collection.")
        elif not is_valid_uuid(collection_id):
            st.warning("Invalid UUID format.")
        else:
            try:
                with st.spinner("API call.."):
                    response = send_json_request(
                        f"{INDEXER_URL}/{collection_id}",
                        method="delete",
                        headers=headers,
                    )
                if isinstance(response, requests.Response):
                    st.success("Request Sent Successfully!")
                    st.json(response.json())
                else:
                    st.error(response)
            except SyntaxError:
                st.error("Invalid JSON format.")

with tab_alter:
    is_valid = True
    collection_options = get_collection_options()
    selected_option = st.selectbox("Select collection to alter", collection_options, format_func=lambda x: x[0])
    collection_id = selected_option[1] if selected_option else ""

    # Fetch current collection details when selected
    current_collection = None
    current_custom_config = {}
    current_role = "basic"

    if collection_id:
        current_collection = get_collection_details(collection_id)
        if current_collection:
            current_custom_config = current_collection.get("custom_config", {})
            current_role = current_custom_config.get("role", "basic")

    alter_name = st.text_input("New collection name (if altering)", value=current_collection.get("name", "") if current_collection else "")
    # Get current LLM model and find its index in LLM_MODELS
    current_llm_model = current_custom_config.get("llm_model", LLM_MODELS[0]) if current_collection else LLM_MODELS[0]
    llm_model_index = LLM_MODELS.index(current_llm_model) if current_llm_model in LLM_MODELS else 0
    llm_model_selection = st.selectbox("LLM", LLM_MODELS, index=llm_model_index, key="llm_model_alter", help="Select the Large Language Model to use for generating responses")
    # Search method (pre-select current)
    current_method = current_custom_config.get("collection_search_method", DEFAULT_SEARCH_METHOD) if current_collection else DEFAULT_SEARCH_METHOD
    method_selection_alter = st.selectbox(
        "Search Method",
        AVAILABLE_SEARCH_METHODS,
        index=AVAILABLE_SEARCH_METHODS.index(current_method),
        key="method_selection_alter",
    )
    custom_system_prompt = st.text_area(
        "Custom system prompt (if altering)",
        value=current_custom_config.get("system_prompt", "") if current_collection else "",
        height=150,
        help="Define custom instructions for the AI. Supports markdown formatting.",
    )
    current_prompt_length_limit = (
        current_custom_config.get("system_prompt_length_limit", custom_system_prompt_max_char_limit) if current_collection else custom_system_prompt_max_char_limit
    )
    custom_prompt_length_limit_alter = st.number_input(
        "System Prompt Max Length (chars)",
        min_value=custom_system_prompt_min_char_limit,
        max_value=custom_system_prompt_max_char_limit,
        value=current_prompt_length_limit,
        key="custom_prompt_limit_alter",
        help="Maximum character length allowed for the system prompt",
    )

    # Pre-populate current values
    current_reranking = current_custom_config.get("reranking_on", False) if current_collection else False
    reranking_index = 0 if current_reranking else 1
    reranking_on_alter = st.radio(
        "RERANK",
        options=["ON", "OFF"],
        index=reranking_index,
        horizontal=True,
        key="reranking_on_alter",
        help="Reranking improves search results by scoring and reordering retrieved documents based on relevance",
    )
    if reranking_on_alter == "ON":
        current_reranker_model = (
            current_custom_config.get("reranker_small_llm_model", ReRankWebLLMModels.GPT4OMINI.value) if current_collection else ReRankWebLLMModels.GPT4OMINI.value
        )
        reranker_llm_model_alter = st.selectbox(
            "Reranking LLM Model", [m.value for m in ReRankWebLLMModels], index=[m.value for m in ReRankWebLLMModels].index(current_reranker_model), key="reranker_llm_model_alter"
        )

    current_memory = current_custom_config.get("is_memory_on", False) if current_collection else False
    memory_index = 0 if current_memory else 1
    is_memory_on_alter = st.radio(
        "Query History (Memory)",
        options=["ON", "OFF"],
        index=memory_index,
        horizontal=True,
        key="memory_on_alter",
        help="Enables the system to remember previous queries and use them for context in new responses",
    )
    if is_memory_on_alter == "ON":
        current_ttl = current_custom_config.get("query_history_ttl", CHAT_HISTORY_TTL_OPTIONS["1 month"]) if current_collection else CHAT_HISTORY_TTL_OPTIONS["1 month"]
        current_ttl_label = [k for k, v in CHAT_HISTORY_TTL_OPTIONS.items() if v == current_ttl][0] if current_ttl in CHAT_HISTORY_TTL_OPTIONS.values() else "1 month"
        ttl_index = list(CHAT_HISTORY_TTL_OPTIONS.keys()).index(current_ttl_label)
        ttl_label_alter = st.selectbox(
            "Chat History TTL",
            list(CHAT_HISTORY_TTL_OPTIONS.keys()),
            index=ttl_index,
            key="ttl_alter",
            help="Time-to-live: How long chat history will be stored before automatic deletion",
        )
        query_history_ttl_alter = CHAT_HISTORY_TTL_OPTIONS[ttl_label_alter]

    current_web_search = current_custom_config.get("is_web_search_on", False) if current_collection else False
    web_search_index = 0 if current_web_search else 1
    is_web_search_on_alter = st.radio(
        "Web Search",
        options=["ON", "OFF"],
        index=web_search_index,
        horizontal=True,
        key="web_search_alter",
        help="Allows the AI to search the web for up-to-date information when answering queries",
    )
    if is_web_search_on_alter == "ON":
        current_web_search_model = (
            current_custom_config.get("web_search_llm_model", ReRankWebLLMModels.GPT4OMINI.value) if current_collection else ReRankWebLLMModels.GPT4OMINI.value
        )
        web_search_llm_model_alter = st.selectbox(
            "Web Search LLM Model",
            [m.value for m in ReRankWebLLMModels],
            index=[m.value for m in ReRankWebLLMModels].index(current_web_search_model),
            key="web_search_llm_model_alter",
        )

    current_slash_commands = current_custom_config.get("is_slash_commands_on", True) if current_collection else True
    slash_commands_index = 0 if current_slash_commands else 1
    is_slash_commands_on_alter = st.radio(
        "Slash Commands",
        options=["ON", "OFF"],
        index=slash_commands_index,
        horizontal=True,
        key="slash_commands_alter",
        help="Enables special commands starting with '/' that trigger specific actions or behaviors",
    )
    current_role = current_custom_config.get("role", "basic")

    visibility_role_alter = st.selectbox(
        "Visibility role",
        options=list(VISIBILITY_OPTIONS.keys()),
        format_func=lambda k: VISIBILITY_OPTIONS[k],
        index=list(VISIBILITY_OPTIONS.keys()).index(current_role),
        key="visibility_role_alter",
    )

    # Advanced config
    st.markdown("#### Advanced (Optional):")
    col1, col2, col3 = st.columns(3)
    with col1:
        current_chunk_size = current_custom_config.get("chunk_size", 0) if current_collection else 0
        chunk_size = st.number_input(
            "Chunk Size",
            min_value=0,
            value=current_chunk_size,
            key="chunk_size_alter",
            help="Size of text chunks for processing (chars for recursive/headline, tokens for token strategy)",
        )
    with col2:
        current_chunk_overlap = current_custom_config.get("chunk_overlap", 0) if current_collection else 0
        chunk_overlap = st.number_input(
            "Chunk Overlap",
            min_value=0,
            value=current_chunk_overlap,
            key="chunk_overlap_alter",
            help="Overlap between consecutive chunks to maintain context (chars for recursive/headline, tokens for token strategy)",
        )
    with col3:
        current_strategy = current_custom_config.get("chunking_strategy", ChunkingStrategy.RECURSIVE.value) if current_collection else ChunkingStrategy.RECURSIVE.value
        strategy_index = [s.value for s in ChunkingStrategy].index(current_strategy) if current_strategy in [s.value for s in ChunkingStrategy] else 0
        chunker_strategy_alter = st.selectbox(
            "Chunking Strategy",
            options=[s.value for s in ChunkingStrategy],
            index=strategy_index,
            key="chunker_strategy_alter",
            help="Method used to split documents into chunks: recursive (default), token-based, headline-based, or markdown-based",
        )

    if st.button("Send Request", key="button_alter"):
        if not collection_id:
            st.warning("Collection ID cannot be empty.")
        else:
            custom_config = {}
            if alter_name.strip():
                custom_config["name"] = alter_name
            if llm_model_selection:
                custom_config["llm_model"] = llm_model_selection
            if method_selection_alter:
                custom_config["collection_search_method"] = method_selection_alter
            if chunk_size > 0:
                custom_config["chunk_size"] = chunk_size
            if chunk_overlap > 0:
                custom_config["chunk_overlap"] = chunk_overlap
            custom_config["chunking_strategy"] = chunker_strategy_alter

            custom_config["is_memory_on"] = is_memory_on_alter == "ON"
            custom_config["reranking_on"] = reranking_on_alter == "ON"
            custom_config["is_web_search_on"] = is_web_search_on_alter == "ON"
            custom_config["is_slash_commands_on"] = is_slash_commands_on_alter == "ON"
            custom_config["system_prompt_length_limit"] = custom_prompt_length_limit_alter
            custom_config["role"] = visibility_role_alter

            if custom_config["is_memory_on"]:
                custom_config["query_history_ttl"] = query_history_ttl_alter
            if custom_config["reranking_on"]:
                custom_config["reranker_small_llm_model"] = reranker_llm_model_alter
            if custom_config["is_web_search_on"]:
                custom_config["web_search_llm_model"] = web_search_llm_model_alter

            if custom_system_prompt != "":
                max_len = custom_prompt_length_limit_alter or custom_system_prompt_max_char_limit
                if len(custom_system_prompt) < custom_system_prompt_min_char_limit:
                    st.warning(f"System prompt too short. Min: {custom_system_prompt_min_char_limit}")
                    is_valid = False
                elif len(custom_system_prompt) > max_len:
                    st.warning(f"System prompt too long. Max: {max_len}. Your input: {len(custom_system_prompt)}")
                    is_valid = False
                else:
                    custom_config["system_prompt"] = custom_system_prompt
                    custom_config["system_prompt_length_limit"] = custom_prompt_length_limit_alter

            if not custom_config:
                st.warning("No changes detected.")
            elif not is_valid:
                st.warning("Fix validation errors before proceeding.")
            else:
                # Prepare the update request object
                update_request = {
                    "collection_id": collection_id,
                    "name": custom_config.get("name", None),
                    "llm_model": custom_config.get("llm_model", None),
                    "system_prompt": custom_config.get("system_prompt", None),
                    "chunk_size": custom_config.get("chunk_size", None),
                    "chunk_overlap": custom_config.get("chunk_overlap", None),
                    "is_memory_on": custom_config.get("is_memory_on", None),
                    "query_history_ttl": custom_config.get("query_history_ttl", None),
                    "reranking_on": custom_config.get("reranking_on", None),
                    "reranker_small_llm_model": custom_config.get("reranker_small_llm_model", None),
                    "is_web_search_on": custom_config.get("is_web_search_on", None),
                    "web_search_llm_model": custom_config.get("web_search_llm_model", None),
                    "is_slash_commands_on": custom_config.get("is_slash_commands_on", None),
                    "chunking_strategy": custom_config.get("chunking_strategy", None),
                    "system_prompt_length_limit": custom_config.get("system_prompt_length_limit", None),
                    "collection_search_method": custom_config.get("collection_search_method", None),
                    "role": custom_config.get("role", None),
                }

            try:
                with st.spinner("API call.."):
                    response = send_json_request(
                        f"{LOADER_URL}/{collection_id}",
                        json_data=update_request,
                        method="patch",
                        headers=headers,
                    )
                if isinstance(response, requests.Response) and response.status_code == 200:
                    st.success("Request Sent Successfully!")
                    st.json(response.json())
                else:
                    st.error(response.text)
            except Exception as e:
                st.error(f"An error occurred: {e}")
