from streamlit_app.utils.auth_utils import ensure_login

token, headers, roles = ensure_login()
import pandas as pd
import streamlit as st
from streamlit_app.utils.api_utils import (
    delete_feedbacks_from_query,
    send_feedback_for_query,
)
from streamlit_app.utils.db_utils import (
    delete_query,
    get_all_collections,
    get_all_queries,
    get_or_set_selected_collection,
    word_wrap,
)


def dataframe_with_selections(df):
    """To display dataframe in selectable view."""
    df_with_selections = df.copy()
    df_with_selections.insert(0, "Select", False)
    edited_df = st.data_editor(
        df_with_selections,
        hide_index=True,
        column_config={"Select": st.column_config.CheckboxColumn(required=True)},
        disabled=df.columns,
    )
    return df[edited_df.Select]["id"].tolist()


FEEDBACK_TYPE_RATE3 = "rate-3"
FEEDBACK_TYPE_RATE10 = "rate-10"

PAGE_TITLE = "Query History"
st.set_page_config(page_title=PAGE_TITLE, page_icon="📈")

st.markdown(f"# {PAGE_TITLE}")

refresh = st.button("Refresh", key="button_refresh")

selected_collection_id = get_or_set_selected_collection()

all_collections = get_all_collections()
collection_dict = {str(c["id"]): c for c in all_collections}

if not selected_collection_id or selected_collection_id not in collection_dict:
    st.warning("Please create a collection first!")
else:
    selected_collection = collection_dict[selected_collection_id]
    collection_id = selected_collection["id"]
    custom_config = selected_collection.get("custom_config", {})

    st.sidebar.markdown("**Custom Config:**")
    st.sidebar.markdown(f":dash: **embedding_model:** {custom_config.get('embedding_model', 'default')}")
    st.sidebar.markdown(f":dash: **llm_model:** {custom_config.get('llm_model', 'default')}")
    st.sidebar.markdown(f":dash: **method:** {custom_config.get('method', 'default')}")
    st.sidebar.markdown(f":speech_balloon: **system_prompt:** {custom_config.get('system_prompt', 'default')}")

    queries = get_all_queries(collection_id=collection_id)
    if not queries:
        st.write("No data available")
    else:
        st.markdown(f"__🔗 Collection: {selected_collection.get('name', 'Unnamed Collection')}__")

        search_term = st.text_input("Search term", "")
        matched_queries = [q for q in queries if search_term.lower() in q.get("content").lower()]

        if not matched_queries:
            st.write("No matching queries found.")
        else:
            matched_queries = sorted(matched_queries, key=lambda x: x.get("created_at", ""), reverse=True)

        for ind, _query in enumerate(matched_queries):
            created_at = _query.get("created_at")
            query_id = _query.get("id")
            request_id = _query.get("request_id")
            question = _query.get("content")
            collection_name = _query.get("session", {}).get("collection", {}).get("name", "")
            answer = _query.get("answer")
            raw_request = _query.get("raw_request")
            raw_response = _query.get("raw_response")
            feedbacks = _query.get("feedbacks")
            custom_config = _query.get("custom_config", {})
            retrieved_docs = _query.get("retrieved_docs", {})
            retrieved_docs_mmr = _query.get("retrieved_docs_mmr", {})

            st.markdown(f"__🕒 Timestamp: {created_at}__")
            st.markdown("## 👤 User")
            st.markdown(f"{question}")

            if raw_response and isinstance(raw_response, dict):
                custom_config = raw_response.get("custom_config", {})
                if custom_config:
                    with st.expander("❗ Custom config found ❗"):
                        st.json(custom_config, expanded=True)
            else:
                st.write("No raw response available for this query.")

            st.markdown("## 🤖 Answer")
            st.markdown(f"{answer}")
            st.markdown(f"__query_id = {str(query_id)}__")

            with st.expander("Source documents"):
                if raw_response and isinstance(raw_response, dict):
                    source_documents = raw_response.get("source_documents", [])
                    if source_documents:
                        _tabs = st.tabs([str(i) for i in range(len(source_documents))])
                        for _tab, _doc in zip(_tabs, source_documents):
                            with _tab:
                                _page_content = _doc["page_content"]
                                _metadata = _doc["metadata"]
                                _type = _doc["type"]
                                st.json(
                                    {
                                        "page_content": word_wrap(_page_content),
                                        "metadata": _metadata,
                                        "type": _type,
                                    }
                                )

            with st.expander("Retrieved Docs", expanded=False):
                if _query.get("retrieved_docs"):
                    st.json(_query.get("retrieved_docs"))
                else:
                    st.write("No retrieved docs available.")

            st.text("💬 Feedback")
            if feedbacks:
                _columns = ("id", "feedback_type", "feedback_value", "feedback_text")
                df = pd.DataFrame(columns=_columns)
                for feedback in feedbacks:
                    df.loc[len(df)] = {
                        "id": str(feedback["id"]),
                        "feedback_type": str(feedback["feedback_type"]),
                        "feedback_value": str(feedback["feedback_value"]),
                        "feedback_text": str(feedback["feedback_text"]),
                    }

                selection = dataframe_with_selections(df)
                if selection:
                    if st.button(
                        "🗑️ Delete selected feedbacks",
                        key=f"button_delete_feedbacks_{ind}",
                    ):
                        delete_feedbacks_from_query(selection, headers)
                        st.rerun()

            feedback_text = st.text_input(
                "add your extra feedback here.. (optional and only submitted via rate(1-10) buttons)",
                key=f"feedback_text_{ind}",
            )
            cols = st.columns(10)
            rates = range(1, 11)
            for _col, _rate in zip(cols, rates):
                text = str(_rate)
                if _rate == 1:
                    text = f"👎{text}"
                elif _rate == 10:
                    text = f"👍{text}"
                with _col:
                    _button = st.button(text, key=f"button_rate10_{ind}_{_rate}")
                    if _button:
                        send_feedback_for_query(
                            query_id=str(query_id),
                            feedback_type=FEEDBACK_TYPE_RATE10,
                            feedback_value=str(_rate),
                            feedback_text=feedback_text,
                            headers=headers,
                        )
                        st.rerun()

        # Query deletion section
        st.markdown("### 🗑️ Delete Queries")
        df = pd.DataFrame(
            [
                {
                    "id": _query["id"],
                    "question": _query["content"],
                    "created_at": _query["created_at"],
                }
                for _query in matched_queries
            ]
        )
        query_selection = dataframe_with_selections(df)

        if query_selection:
            if st.button("🗑️ Delete selected queries", key="button_delete_queries"):
                for query_id in query_selection:
                    delete_query(query_id)
                st.success("Selected queries deleted successfully.")
                st.rerun()

        st.divider()
