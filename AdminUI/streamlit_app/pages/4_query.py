from streamlit_app.utils.auth_utils import ensure_login

token, headers, roles = ensure_login()
from typing import Dict

import requests
import streamlit as st
from streamlit_app.constants import GRAPHRAG_BASE_URL, PROMPT_BASE_URL, QUERY_BASE_URL
from streamlit_app.utils.api_utils import send_json_request
from streamlit_app.utils.db_utils import (
    get_all_collections,
    get_or_set_selected_collection,
    word_wrap,
)
from streamlit_app.utils.prompt_utils import (
    PromptOrchestrationClient,
    build_final_prompt,
    fetch_prompts,
    remove_prompt,
    render_prompt_table,
)

PAGE_TITLE = "Query"
st.set_page_config(page_title=PAGE_TITLE, page_icon="📈")
st.markdown(f"# {PAGE_TITLE}")
st.write("Send request to embedding services.")

# Collection handling
selected_collection_id = get_or_set_selected_collection()
all_collections = get_all_collections()
collection_dict: Dict[str, Dict] = {str(c["id"]): c for c in all_collections}

# Prompt Orchestration client
prompt_service_client = PromptOrchestrationClient(base_url=PROMPT_BASE_URL)

if selected_collection_id and selected_collection_id in collection_dict:
    selected_collection = collection_dict[selected_collection_id]
    custom_config = selected_collection.get("custom_config", {})
    collection_type = selected_collection.get("type", "Regular")

    st.sidebar.markdown(f"**Collection Type:** {collection_type}")

    if collection_type == "GraphRAG":
        st.sidebar.markdown("**GraphRAG Config:**")
        st.sidebar.markdown(":robot_face: **Type:** Knowledge Graph RAG")
        st.sidebar.markdown(f":file_folder: **Collection Name:** {custom_config.get('query_name', 'N/A')}")
        st.sidebar.markdown(":mag: **Query Methods:** global, local, drift")
        st.sidebar.info("💡 This collection uses GraphRAG API with knowledge graph-based retrieval")
    else:
        st.sidebar.markdown("**Custom Config:**")
        st.sidebar.markdown(f":dash: **embedding_model:** {custom_config.get('embedding_model', 'default')}")
        st.sidebar.markdown(f":dash: **llm_model:** {custom_config.get('llm_model', 'default')}")
        st.sidebar.markdown(f":dash: **method:** {custom_config.get('method', 'default')}")
        st.sidebar.markdown(f":speech_balloon: **system_prompt:** {custom_config.get('system_prompt', 'default')}")

if not selected_collection_id or selected_collection_id not in collection_dict:
    st.warning("Please create a collection first!")
    st.stop()

collection_id = collection_dict[selected_collection_id]["id"]
selected_collection = collection_dict[selected_collection_id]
collection_type = selected_collection.get("type", "Regular")
is_graphrag = collection_type == "GraphRAG"

if is_graphrag:
    st_qa, st_doc_search = st.tabs(["GraphRAG Query", "document_search"])
else:
    st_qa, st_doc_search = st.tabs(["q&a", "document_search"])


with st_qa:
    if is_graphrag:
        st.info("🕸️ **GraphRAG Query Interface** - Querying knowledge graph-based collections")
        st.markdown("---")

    use_prompt_service = st.radio("* Select Prompt from Prompt Service", ["False", "True"])

    # If user toggles off the prompt service, clear selection
    if use_prompt_service == "False" and st.session_state.get("selected_prompts"):
        st.session_state.pop("selected_prompts", None)

    # If user toggles on prompt service, clear template selection
    if use_prompt_service == "True" and st.session_state.get("selected_template"):
        st.session_state.pop("selected_template", None)
        st.session_state.pop("rendered_template_content", None)

    if use_prompt_service == "True":
        user = st.selectbox("Select User", ["All", "System"] + [u["email"] for u in prompt_service_client.list_users()])
        # Trim any whitespace from the user selection
        user = user.strip() if user else user

        if user == "All":
            selected_user = "All"
        elif user == "System":
            selected_user = "System"
        else:
            selected_user = user

        search_term = st.text_input("🔍 Search Prompts", key="prompt_search")

        prompts_to_display = fetch_prompts(selected_user, search_term)
        render_prompt_table(prompts_to_display, key_prefix="prompt_row")

    # Selected prompts with ability to deselect
    if st.session_state.get("selected_prompts"):
        with st.expander("✅ Selected Prompts"):
            for idx, sp in enumerate(st.session_state.selected_prompts):
                col_text, col_btn = st.columns([8, 1])
                col_text.markdown(f"• **{sp.get('title', 'No Title')}**")
                if col_btn.button("🗑️", key=f"sel_del_{idx}"):
                    remove_prompt(idx)

    st.divider()

    # Template selection section
    use_template_service = st.radio("* Select Template from Template Service", ["False", "True"])

    # If user toggles off the template service, clear selection
    if use_template_service == "False" and st.session_state.get("selected_template"):
        st.session_state.pop("selected_template", None)
        st.session_state.pop("rendered_template_content", None)

    # If user toggles on template service, clear prompt selection
    if use_template_service == "True" and st.session_state.get("selected_prompts"):
        st.session_state.pop("selected_prompts", None)

    if use_template_service == "True":
        # Initialize session state for templates
        if "templates" not in st.session_state:
            st.session_state.templates = []

        # Load templates if not already loaded
        if not st.session_state.templates:
            try:
                response = prompt_service_client.get_all_templates()
                st.session_state.templates = response.get("data", [])
            except Exception as e:
                st.error(f"Error loading templates: {e}")

        # Template selection
        template_options = ["None"] + [f"{t['title']} ({t['use_case']})" for t in st.session_state.templates]
        selected_template_option = st.selectbox("Select Template", template_options, key="template_selection")

        if selected_template_option != "None":
            # Find the selected template
            selected_template = None
            for template in st.session_state.templates:
                if f"{template['title']} ({template['use_case']})" == selected_template_option:
                    selected_template = template
                    break

            if selected_template:
                st.session_state.selected_template = selected_template

                # Show template details
                with st.expander(f"📝 Template: {selected_template['title']}"):
                    st.markdown(f"**Description:** {selected_template.get('description', 'No description')}")
                    st.markdown(f"**Content:** {selected_template['template_content']}")

                    # Template placeholders
                    if selected_template.get("placeholders"):
                        st.markdown("**Placeholders:**")
                        placeholder_values = {}
                        for placeholder in selected_template["placeholders"]:
                            placeholder_type = placeholder["placeholder_type"]
                            description = placeholder["description"]
                            default_value = placeholder["default_value"]
                            is_required = placeholder["is_required"]

                            if is_required:
                                value = st.text_input(f"{placeholder_type} *", value=default_value, help=f"Required: {description}")
                            else:
                                value = st.text_input(placeholder_type, value=default_value, help=f"Optional: {description}")

                            if value:  # Only add non-empty values
                                placeholder_values[placeholder_type] = value

                        # Render template button
                        if st.button("Render Template"):
                            if placeholder_values:
                                try:
                                    # Local rendering: replace placeholders in template_content (case-insensitive)
                                    import re

                                    rendered_content = selected_template["template_content"]
                                    for key, value in placeholder_values.items():
                                        # Use regex to replace placeholders case-insensitively
                                        pattern = re.compile(f"\\{{{key}\\}}", re.IGNORECASE)
                                        rendered_content = pattern.sub(value, rendered_content)
                                    if rendered_content:
                                        st.markdown("**Rendered Template:**")
                                        st.text_area("Result", rendered_content, height=150, disabled=True, key="rendered_template")
                                        # Store the rendered content for use in the final prompt
                                        st.session_state.rendered_template_content = rendered_content
                                        st.success("Template rendered and will be included in the query!")
                                    else:
                                        st.error("Failed to render template")
                                except Exception as e:
                                    st.error(f"Error rendering template: {e}")
                            else:
                                st.warning("Please fill in at least one placeholder.")

    # Selected template with ability to deselect
    if st.session_state.get("selected_template"):
        with st.expander("✅ Selected Template"):
            template = st.session_state.selected_template
            col_text, col_btn = st.columns([8, 1])
            col_text.markdown(f"• **{template.get('title', 'No Title')}** ({template.get('use_case', 'Unknown')})")
            if col_btn.button("🗑️", key="template_del"):
                st.session_state.pop("selected_template", None)
                st.rerun()

    st.divider()

    # Question & options
    question = st.text_input("Question")

    # ✅ Optional session ID input
    manual_session_id = st.text_input("Session ID (optional)", placeholder="Leave empty for auto-generation")

    # Determine available search methods based on collection type
    if is_graphrag:
        available_methods = ["global", "local", "drift"]
        default_method = "global"
        method_help = "Global: Comprehensive analysis across all documents\nLocal: Focused search on specific entities\nDrift: Temporal analysis"
        search_method = st.selectbox("GraphRAG Query Method", options=available_methods, index=0, help=method_help)
    else:
        available_methods = [
            "similarity",
            "mmr",
            "keyword",
            "hybrid",
            "bm25",
            "fused",
            "rerank",
            "elastic search",
        ]
        default_method = custom_config.get("collection_search_method", "similarity")
        default_index = available_methods.index(default_method) if default_method in available_methods else 0

        search_method = st.selectbox(
            "Search Method",
            options=available_methods,
            index=default_index,
        )
    custom_prompt_input = st.text_area("Custom Prompt (additional, optional)")

    final_prompt = build_final_prompt(custom_prompt_input)

    json_payload = {
        "content": question,
        "custom_prompt": final_prompt,
        "search_method": search_method,
    }
    if manual_session_id.strip():
        json_payload["session_id"] = manual_session_id.strip()  # ➕ add if provided

    if st.button("Send Request", key="button_single"):
        if not question:
            st.error("Please enter a question before sending the request.")
            st.stop()

        try:
            if is_graphrag:
                # GraphRAG API call - use query_name (without collection_ prefix)
                query_name = custom_config.get("query_name", "")
                graphrag_payload = {"root_name": query_name, "method": search_method, "query": question}

                with st.spinner("Sending GraphRAG Query Request …"):
                    response = requests.post(f"{GRAPHRAG_BASE_URL}/query", json=graphrag_payload, headers=headers, timeout=300)
            else:
                # Regular query service call
                with st.spinner("Sending Query Request …"):
                    response = send_json_request(
                        f"{QUERY_BASE_URL}/collection/{collection_id}/query",
                        json_payload,
                        headers=headers,
                    )

            if isinstance(response, requests.Response):
                if response.status_code != 200:
                    st.error(response.text)
                    st.stop()
                st.success("Request sent successfully!")
            else:
                st.error(response)
                st.stop()

            data = response.json()

            # ✅ Show response content
            if is_graphrag:
                st.markdown("## 🕸️ GraphRAG Answer")
                st.markdown(data.get("answer", ""))
                if "answer" not in data or not data["answer"]:
                    st.warning("⚠️ " + data.get("error", "No answer found in the response."))
            else:
                st.markdown("## 🤖 Answer")
                st.markdown(data.get("content", ""))
                st.markdown("#### 📌 Session ID")
                st.code(data.get("session_id", "N/A"), language="text")

            # Show retrieved docs only for regular collections
            if not is_graphrag:
                retrieved_docs = data.get("retrieved_docs", {})

                # Show docs (similarity/keyword/hybrid)
                docs = retrieved_docs.get("docs", [])
                if docs:
                    with st.expander(f"Retrieved docs ({search_method})"):
                        for i, doc in enumerate(docs):
                            with st.tabs([str(i)])[0]:
                                doc_data = doc[0] if isinstance(doc, (list, tuple)) else doc
                                st.json(
                                    {
                                        "page_content": word_wrap(doc_data.get("page_content", "")),
                                        "metadata": doc_data.get("metadata", {}),
                                        "type": doc_data.get("type", ""),
                                    }
                                )

                # Show docs (MMR)
                docs_mmr = retrieved_docs.get("docs_mmr", [])
                if docs_mmr:
                    with st.expander("Retrieved docs (mmr)"):
                        for i, doc in enumerate(docs_mmr):
                            with st.tabs([str(i)])[0]:
                                doc_data = doc[0] if isinstance(doc, (list, tuple)) else doc
                                st.json(
                                    {
                                        "page_content": word_wrap(doc_data.get("page_content", "")),
                                        "metadata": doc_data.get("metadata", {}),
                                        "type": doc_data.get("type", ""),
                                    }
                                )

        except Exception as exc:
            st.error(f"Unexpected error: {exc}")

with st_doc_search:
    if is_graphrag:
        st.info("🚫 Document search is not available for GraphRAG collections.")
        st.markdown("GraphRAG uses knowledge graph-based retrieval instead of traditional document search.")
        st.markdown("Please use the **GraphRAG Query** tab for querying this collection.")
    else:
        search_question = st.text_input("Question", key="question2")
        if st.button("Send Request", key="button_doc_search"):
            try:
                with st.spinner("Sending document search request …"):
                    response = send_json_request(
                        f"{QUERY_BASE_URL}/collection/{collection_id}/query/retrieved_docs",
                        {"content": search_question},
                        headers=headers,
                    )

                if isinstance(response, requests.Response):
                    if response.status_code != 200:
                        st.error(response.text)
                    else:
                        st.success("Request sent successfully!")
                        st.json(response.json())
                else:
                    st.error(response)
            except Exception as exc:
                st.error(f"Unexpected error: {exc}")
