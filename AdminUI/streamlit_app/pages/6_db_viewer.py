from streamlit_app.utils.auth_utils import ensure_login

token, headers, roles = ensure_login()
import pandas as pd
import streamlit as st
from streamlit_app.constants import INDEXER_BASE_URL, LOADER_BASE_URL, QUERY_BASE_URL
from streamlit_app.utils.api_utils import send_json_request
from streamlit_app.utils.db_utils import (
    DOCUMENT_STATUS_VALUES,
    get_all_collections,
    get_all_documents,
    get_all_feedbacks,
    get_all_queries,
    get_all_sessions,
    get_all_urls,
    get_or_set_selected_collection,
)

PAGE_TITLE = "DB Viewer"
st.set_page_config(page_title=PAGE_TITLE, page_icon="📈")
st.markdown(f"# {PAGE_TITLE}")

if "failed_docs" not in st.session_state:
    st.session_state.failed_docs = []

selected_collection_id = get_or_set_selected_collection()

all_collections = get_all_collections()
collection_dict = {str(c["id"]): c for c in all_collections}

if not selected_collection_id or selected_collection_id not in collection_dict:
    st.warning("Please create a collection first!")
else:
    selected_collection = collection_dict[selected_collection_id]
    collection_id = selected_collection["id"]
    st.text(collection_id)


def display_queries(collection_id):
    """Display all queries for a given collection."""
    st.title("Queries Table")
    queries = get_all_queries(collection_id)
    if queries:
        df_queries = pd.DataFrame(
            [
                (
                    q.get("created_at"),
                    str(q.get("id")),
                    str(q.get("request_id")),
                    str(q.get("session", {}).get("collection.name", "")),
                    q.get("content"),
                    q.get("answer"),
                    q.get("raw_request"),
                    q.get("raw_response"),
                )
                for q in queries
            ],
            columns=[
                "Created At",
                "ID",
                "Request Id",
                "Collection Name",
                "Content",
                "Answer",
                "Raw Request",
                "Raw Response",
            ],
        )
        st.dataframe(df_queries)
    else:
        st.write("No data available")


def display_documents(collection_id):
    """Display all documents for a given collection, with options to filter by status and delete selected documents."""
    st.title("Documents Table")
    documents = get_all_documents(collection_id)
    if documents:
        # Filter documents based on filtered_status
        if filtered_status and filtered_status != "*":
            documents = [d for d in documents if d.get("status") == filtered_status]

        # Create a DataFrame from the filtered documents
        df_documents = pd.DataFrame(
            [
                (
                    d["created_at"],
                    str(d.get("id")),
                    str(d.get("collection_name")),
                    d.get("file_path"),
                    d.get("metadata"),
                    d.get("status"),
                )
                for d in documents
            ],
            columns=[
                "Created At",
                "ID",
                "Collection Name",
                "File Path",
                "Metadata",
                "Status",
            ],
        )

        # Create a DataFrame with a selection column
        df_documents = pd.DataFrame(
            [
                (
                    d["created_at"],
                    str(d.get("id")),
                    str(d.get("collection_name")),
                    d.get("file_path"),
                    d.get("metadata"),
                    d.get("status"),
                    False,  # Default selection state
                )
                for d in documents
            ],
            columns=[
                "Created At",
                "ID",
                "Collection Name",
                "File Path",
                "Metadata",
                "Status",
                "Select",
            ],
        )

        # Use Streamlit's data editor to make the table interactive
        edited_df = st.data_editor(
            df_documents,
            column_config={
                "Select": st.column_config.CheckboxColumn(
                    "Select",
                    help="Select documents to delete",
                    default=False,
                ),
                "ID": st.column_config.TextColumn(
                    "ID",
                    help="Document ID",
                ),
                "File Path": st.column_config.TextColumn(
                    "File Path",
                    help="Document file path",
                    width="large",
                ),
            },
            use_container_width=True,
            hide_index=True,
        )

        # Store selected document IDs
        selected_doc_ids = edited_df.loc[edited_df["Select"], "ID"].tolist()

        # Button to delete selected documents
        if st.button("Delete Selected Documents"):
            if selected_doc_ids:
                with st.spinner(f"Deleting {len(selected_doc_ids)} selected documents..."):
                    success_count = 0
                    fail_count = 0
                    for doc_id in selected_doc_ids:
                        # Get the file path for this document ID for better feedback
                        doc_file_path = edited_df.loc[edited_df["ID"] == doc_id, "File Path"].iloc[0]

                        # Delete Document with embeddings
                        response = send_json_request(
                            f"{INDEXER_BASE_URL}/collection/{collection_id}/document/{doc_id}",
                            method="delete",
                            headers=headers,
                        )
                        if response:  # Check response for success/failure if necessary
                            success_count += 1
                        else:
                            fail_count += 1
                            st.error(f"Failed to delete document: {doc_file_path} (ID: {doc_id})")

                    if success_count > 0:
                        st.success(f"Successfully deleted {success_count} documents")
                    if fail_count > 0:
                        st.error(f"Failed to delete {fail_count} documents")

                    # Refresh the page to show updated document list
                    st.rerun()
            else:
                st.warning("No documents selected for deletion.")

        # Store failed documents in session state
        st.session_state.failed_docs = [str(str(d.get("id"))) for d in documents if d.get("status") == "failed"]
    else:
        st.write("No data available")

    if len(st.session_state.failed_docs) > 0:
        if st.button(
            f"Reload {len(st.session_state.failed_docs)} failed documents",
            key="button_process",
        ):
            with st.spinner("reloading documents.."):
                for document_id in st.session_state.failed_docs:
                    send_json_request(
                        f"{LOADER_BASE_URL}/{collection_id}/document/{document_id}/reload",
                        headers=headers,
                    )


def display_urls(collection_id):
    """Display all URLs for a given collection, with options to delete or reload failed URLs."""
    st.title("URLs Table")
    urls = get_all_urls(collection_id)
    if urls:
        # Create a DataFrame from the URLs with selection column
        df_urls = pd.DataFrame(
            [
                (
                    u.get("created_at"),
                    str(u.get("id")),
                    str(u.get("collection", {}).get("name")),
                    u.get("url"),
                    u.get("content"),
                    u.get("status"),
                    False,  # Default selection state
                )
                for u in urls
            ],
            columns=["Created At", "ID", "Collection Name", "URL", "Content", "Status", "Select"],
        )

        # Use Streamlit's data editor to make the table interactive
        edited_df_urls = st.data_editor(
            df_urls,
            column_config={
                "Select": st.column_config.CheckboxColumn(
                    "Select",
                    help="Select URLs to delete",
                    default=False,
                ),
                "URL": st.column_config.TextColumn(
                    "URL",
                    help="URL address",
                    width="large",
                ),
            },
            use_container_width=True,
            hide_index=True,
        )

        # Process selected URLs for deletion
        selected_url_ids = edited_df_urls.loc[edited_df_urls["Select"], "ID"].tolist()
        if st.button("Delete Selected URLs"):
            if selected_url_ids:
                with st.spinner(f"Deleting {len(selected_url_ids)} selected URLs..."):
                    success_count = 0
                    fail_count = 0
                    for url_id in selected_url_ids:
                        # Get the URL for this ID for better feedback
                        url_address = edited_df_urls.loc[edited_df_urls["ID"] == url_id, "URL"].iloc[0]

                        response = send_json_request(
                            f"{INDEXER_BASE_URL}/collection/{collection_id}/url/{url_id}",
                            method="delete",
                            headers=headers,
                        )
                        if response:
                            success_count += 1
                        else:
                            fail_count += 1
                            st.error(f"Failed to delete URL: {url_address} (ID: {url_id})")

                    if success_count > 0:
                        st.success(f"Successfully deleted {success_count} URLs")
                    if fail_count > 0:
                        st.error(f"Failed to delete {fail_count} URLs")

                    # Refresh the page to show updated URL list
                    st.rerun()
            else:
                st.warning("No URLs selected for deletion.")

        # # Reload failed URLs
        # failed_urls = [u for u in urls if u.get("status") == "failed"]
        # if len(failed_urls) > 0:
        #     if st.button(
        #         f"Reload {len(failed_urls)} Failed URLs",
        #         key="button_reload_urls",
        #     ):
        #         with st.spinner("Reloading failed URLs..."):
        #             for url in failed_urls:
        #                 send_json_request(
        #                     f"{LOADER_BASE_URL}/{collection_id}/url/{url['id']}/reload",
        #                     headers=headers,
        #                 )
    else:
        st.write("No data available")


def display_feedbacks(collection_id):
    """Display all feedbacks for a given collection."""
    st.title("Feedbacks Table")

    feedbacks = get_all_feedbacks(collection_id)
    if feedbacks:
        df_feedbacks = pd.DataFrame(
            [
                (
                    f.get("created_at"),
                    str(f.get("id")),
                    str(f.get("request_id")),
                    f.get("feedback_type"),
                    f.get("feedback_value"),
                    f.get("status"),
                )
                for f in feedbacks
            ],
            columns=["Created At", "ID", "Request ID", "Type", "Value", "Status"],
        )
        st.dataframe(df_feedbacks)
    else:
        st.write("No data available")


def display_collections(collection_id):
    """Display all collections in a table format."""
    st.title("Collections Table")
    collections = get_all_collections()
    if collections:
        df_collections = pd.DataFrame(
            [
                (
                    c.get("created_at", "-"),
                    str(c["id"]),
                    c.get("name", "-"),
                    c.get("description", "-"),
                    c.get("custom_config", {}),
                )
                for c in collections
            ],
            columns=["Created At", "ID", "Name", "Description", "Custom config"],
        )

        st.dataframe(df_collections)
    else:
        st.write("No data available")


def display_user_sessions(collection_id: str):
    """Display user sessions for a given collection, allowing management of sessions, folders, and individual session actions."""
    try:
        st.title("📁 User Sessions")

        folders_dict = get_all_sessions(collection_id)

        if not folders_dict:
            st.info("No sessions found for this collection.")
            return

        def render_session_table(title, sessions, folder_name=None):
            if not sessions:
                st.info(f"No sessions in {title}.")
                return

            # Delete folder button (only for non-default folders)
            if folder_name and folder_name not in ["favorites", "unassigned"]:
                if st.button(
                    f"🗑️ Delete Folder: {folder_name}",
                    key=f"delete_folder_{folder_name}",
                ):
                    url = f"{QUERY_BASE_URL}/delete_folder/{collection_id}/{folder_name}"
                    resp = send_json_request(url, method="delete", headers=headers)
                    if resp and resp.status_code == 200:
                        st.success(f"Folder '{folder_name}' deleted successfully")
                        st.rerun()
                    else:
                        st.error(f"Failed to delete folder '{folder_name}'")

            df = pd.DataFrame(sessions)
            df["created_at"] = pd.to_datetime(df["created_at"])
            df["Select"] = [st.checkbox(f"Select {row['id']}", key=f"select_{row['id']}") for _, row in df.iterrows()]
            st.subheader(f"📂 {title}")
            st.dataframe(
                df[["Select", "title", "user_email", "created_at", "goal", "id"]],
                use_container_width=True,
            )

            selected_ids = df[df["Select"]]["id"].tolist()
            if selected_ids and st.button(
                f"🗑️ Delete Selected Sessions from {title}",
                key=f"delete_sessions_{folder_name}",
            ):
                for session_id in selected_ids:
                    url = f"{QUERY_BASE_URL}/delete_session/{session_id}"
                    resp = send_json_request(url, method="delete", headers=headers)
                    if resp and resp.status_code == 200:
                        st.success(f"Session {session_id} deleted successfully")
                    else:
                        st.error(f"Failed to delete session {session_id}")
                st.rerun()

        # Favorites
        if "favorites" in folders_dict:
            render_session_table("⭐ Favorites", folders_dict["favorites"], "favorites")

        # Unassigned
        if "unassigned" in folders_dict:
            render_session_table("🗂️ Unassigned", folders_dict["unassigned"], "unassigned")

        # Other folders
        other_folders = {k: v for k, v in folders_dict.items() if k not in ["favorites", "unassigned"]}
        for folder_name, sessions in other_folders.items():
            render_session_table(folder_name, sessions, folder_name)

        # Update Session Title
        st.subheader("✏️ Update Session Title")
        with st.form("update_form"):
            session_id = st.text_input("Session ID to update")
            new_title = st.text_input("New Title")
            submitted = st.form_submit_button("Update Title")
            if submitted:
                payload = {"title": new_title}
                url = f"{QUERY_BASE_URL}/update_session/{session_id}"
                resp = send_json_request(url, method="put", json_data=payload, headers=headers)
                if resp and resp.status_code == 200:
                    st.success(f"Session {session_id} updated successfully")
                    st.rerun()
                else:
                    st.error(f"Failed to update session {session_id}")

        # Delete Session by ID
        st.subheader("🗑️ Delete Session Manually")
        with st.form("delete_form"):
            session_id_to_delete = st.text_input("Session ID to delete")
            delete_submitted = st.form_submit_button("Delete Session")
            if delete_submitted:
                url = f"{QUERY_BASE_URL}/delete_session/{session_id_to_delete}"
                resp = send_json_request(url, method="delete", headers=headers)
                if resp and resp.status_code == 200:
                    st.success(f"Session {session_id_to_delete} deleted successfully")
                    st.rerun()
                else:
                    st.error(f"Failed to delete session {session_id_to_delete}")

    except Exception as e:
        st.error(f"An error occurred: {e}")


tabs = {
    "Collections": display_collections,
    "Queries": display_queries,
    "Documents": display_documents,
    "Feedbacks": display_feedbacks,
    "URLs": display_urls,
    "User Sessions": display_user_sessions,
}

refresh = st.button("Refresh", key="button_refresh2")

filtered_status = None
selection = st.sidebar.radio("Select a table", list(tabs.keys()))
if selection == "Documents" or selection == "URLs":
    filtered_status = st.selectbox("filter_status", DOCUMENT_STATUS_VALUES)

if not selected_collection_id or selected_collection_id not in collection_dict:
    st.warning("Please create a collection first!")
    collection_id = None
else:
    selected_collection = collection_dict[selected_collection_id]
    collection_id = selected_collection["id"]
    st.text(collection_id)

if collection_id:
    tabs[selection](collection_id)
else:
    st.error("No collection selected. Please create or select a collection.")
