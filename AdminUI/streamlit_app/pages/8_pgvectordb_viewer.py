from streamlit_app.utils.auth_utils import ensure_login
from streamlit_app.utils.http_utils import safe_request

# roles may be used if needed
_, _, roles = ensure_login()
import json

import pandas as pd
import streamlit as st
from streamlit_app.constants import INDEXER_BASE_URL, LOADER_BASE_URL

LOADER_URL = f"{LOADER_BASE_URL}/collection"
PAGE_TITLE = "VectorDB Management"
st.set_page_config(page_title=PAGE_TITLE, page_icon="📊")
st.markdown(f"# {PAGE_TITLE}")

selected_label = None
collection_id = None


def _get_headers():
    _token, hdrs, _ = ensure_login()
    return hdrs


def get_collection_options():
    """Fetch available collections from the vector database loader service."""
    try:
        response = safe_request("get", f"{LOADER_URL}s", headers=_get_headers(), timeout=15)
        if response.status_code == 200:
            collections = response.json().get("collections", [])
            return [(f"{col['name']} - {col['id']}", col["id"]) for col in collections]
    except Exception as e:
        st.warning(f"Failed to fetch collections: {e}")
    return []


collection_options = get_collection_options()
if collection_options:
    st.subheader("📁 Select Collection")
    selected_label = st.selectbox("Choose a collection", [label for label, _ in collection_options])
    collection_id = dict(collection_options)[selected_label]


if collection_id:
    # Show total chunk count
    st.subheader("🔣 Total Chunk Count For All Collections")
    total_resp = safe_request(
        "get",
        f"{INDEXER_BASE_URL}/embedding/item_count",
        params={"collection_id": collection_id},
        headers=_get_headers(),
    )
    total_chunks = total_resp.json().get("total_items", 0)
    st.metric("Total Chunks", total_chunks)

    # Collection Info
    info_resp = safe_request("get", f"{INDEXER_BASE_URL}/embedding/collection/{collection_id}", headers=_get_headers())
    info = info_resp.json()
    info_df = pd.DataFrame([info])
    st.subheader("📦 Collection Info")
    st.table(info_df)

    # List chunks (with embeddings)
    st.subheader("🧠 Chunks (with embeddings)")
    embed_resp = safe_request(
        "get",
        f"{INDEXER_BASE_URL}/embedding/collection/{collection_id}/documents/embed",
        headers=_get_headers(),
    )
    embed_chunks = embed_resp.json()
    # Normalize to a DataFrame-safe structure
    try:
        if isinstance(embed_chunks, list):
            df = pd.DataFrame(embed_chunks)
        elif isinstance(embed_chunks, dict):
            # If dict of scalars → wrap in list; else try records
            if all(not isinstance(v, (list, dict)) for v in embed_chunks.values()):
                df = pd.DataFrame([embed_chunks])
            else:
                df = pd.json_normalize(embed_chunks)
        else:
            df = pd.DataFrame([{"value": embed_chunks}])
    except ValueError:
        # Fallback for scalar-only dict without index
        df = pd.DataFrame([embed_chunks]) if isinstance(embed_chunks, dict) else pd.DataFrame([{"value": embed_chunks}])
    st.dataframe(df, use_container_width=True)

    # Search (similarity/MMR/keyword/hybrid)
    st.subheader("🔍 Search in Collection")
    query = st.text_input("Query Text")
    method = st.selectbox("Search Method", ["similarity", "mmr", "keyword", "hybrid"])
    k = st.slider("Top K", 1, 20, 4)
    if st.button("Search"):
        search_resp = safe_request(
            "get",
            f"{INDEXER_BASE_URL}/embedding/collection/{collection_id}/search",
            params={"query": query, "method": method, "k": k},
            headers=_get_headers(),
        )
        st.json(search_resp.json())

    # Relevant Documents (Retriever)
    if st.button("Get Relevant Documents"):
        rel_resp = safe_request(
            "get",
            f"{INDEXER_BASE_URL}/embedding/collection/{collection_id}/relevant",
            params={"query": query, "k": k},
            headers=_get_headers(),
        )
        st.json(rel_resp.json())

    # Add Document (single)
    st.subheader("➕ Insert New Chunk")
    with st.form("insert_form"):
        doc_id = st.text_input("Document ID")
        content = st.text_area("Content")
        metadata = st.text_area("Metadata (as JSON)")
        submit = st.form_submit_button("Insert")
        if submit:
            try:
                payload = {
                    "document_id": doc_id,
                    "content": content,
                    "metadata": json.loads(metadata),
                }
                resp = safe_request(
                    "post",
                    f"{INDEXER_BASE_URL}/embedding/collection/{collection_id}/document",
                    json=payload,
                    headers=_get_headers(),
                )
                (st.success("Inserted") if resp.status_code == 201 else st.error("Failed"))
            except Exception as e:
                st.error(f"Error: {e}")

    # Update Chunk
    st.subheader("✏️ Update Chunk")
    with st.form("update_form"):
        upd_id = st.text_input("Document ID to update")
        new_content = st.text_area("New Content")
        new_metadata = st.text_area("New Metadata (as JSON)")
        if st.form_submit_button("Update"):
            try:
                payload = {}
                if new_content:
                    payload["content"] = new_content
                if new_metadata:
                    payload["metadata"] = json.loads(new_metadata)
                resp = safe_request(
                    "patch",
                    f"{INDEXER_BASE_URL}/embedding/collection/{collection_id}/document/{upd_id}",
                    json=payload,
                    headers=_get_headers(),
                )
                st.success("Updated") if resp.status_code == 200 else st.error("Failed")
            except Exception as e:
                st.error(f"Error: {e}")

    # Delete Chunk
    st.subheader("❌ Delete Chunk")
    del_id = st.text_input("Document ID to delete")
    if st.button("Delete"):
        resp = safe_request(
            "delete",
            f"{INDEXER_BASE_URL}/embedding/collection/{collection_id}/document/{del_id}",
            headers=_get_headers(),
        )
        st.success("Deleted") if resp.status_code == 200 else st.error("Failed")
