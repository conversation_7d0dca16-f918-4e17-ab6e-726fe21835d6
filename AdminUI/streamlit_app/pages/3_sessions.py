from streamlit_app.utils.auth_utils import ensure_login
from streamlit_app.utils.http_utils import safe_request

# roles may be used for UI decisions
_, _, roles = ensure_login()
from datetime import datetime
from typing import Any, Dict, List

import streamlit as st
from streamlit_app.constants import LOADER_BASE_URL, QUERY_BASE_URL
from streamlit_app.utils.api_utils import send_json_request

LOADER_URL = f"{LOADER_BASE_URL}/collection"
PAGE_TITLE = "My Sessions"
st.set_page_config(page_title=PAGE_TITLE, page_icon="🗂️", layout="wide")

st.markdown(f"# {PAGE_TITLE}")

# Lightweight styles for nicer cards and badges
st.markdown(
    """
    <style>
    .session-card {
        padding: 1rem 1.25rem; border: 1px solid #eaeaea; border-radius: 12px;
        background: #fff; box-shadow: 0 1px 2px rgba(0,0,0,0.04);
        margin-bottom: 12px;
    }
    .meta {
        color: #6b7280; font-size: 0.9rem; margin-bottom: 0.25rem;
    }
    .badge {
        display: inline-block; padding: 2px 8px; border-radius: 999px;
        font-size: 0.75rem; border: 1px solid #e5e7eb; color: #374151; background: #f9fafb;
        margin-right: 6px;
    }
    .folder-title { font-weight: 600; }
    </style>
    """,
    unsafe_allow_html=True,
)


# ─────────────────────────────────────────────────────────────────────────────
# Helpers
# ─────────────────────────────────────────────────────────────────────────────


def _get_headers():
    _token, hdrs, _ = ensure_login()
    return hdrs


def get_collection_options():
    """Fetch available collections from the vector database loader service."""
    try:
        response = send_json_request(f"{LOADER_URL}s", method="get", headers=_get_headers())
        if response.status_code == 200:
            collections = response.json().get("collections", [])
            return [(f"{col['name']} - {col['id']}", col["id"]) for col in collections]
    except Exception as e:
        st.warning(f"Failed to fetch collections: {e}")
    return []


def _to_dt(val):
    try:
        return datetime.fromisoformat(val.replace("Z", "+00:00")) if isinstance(val, str) else None
    except Exception:
        return None


# ─────────────────────────────────────────────────────────────────────────────
# Sidebar: collection + filters
# ─────────────────────────────────────────────────────────────────────────────
collection_options = get_collection_options()
if not collection_options:
    st.warning("No collections found.")
    st.stop()

with st.sidebar:
    st.header("Filters")
    selected_label, selected_id = st.selectbox("Choose a collection", options=collection_options, format_func=lambda x: x[0])
    search_text = st.text_input("Search in titles/goals", placeholder="Type to filter sessions...")
    show_only_favorites = st.checkbox("Show only favorites", value=False)
    sort_by = st.selectbox("Sort by", options=["Created (newest)", "Created (oldest)", "Title (A→Z)", "Title (Z→A)"])
    if st.button("Refresh", use_container_width=True):
        st.rerun()

# ─────────────────────────────────────────────────────────────────────────────
# Load sessions
# ─────────────────────────────────────────────────────────────────────────────
response = safe_request("get", f"{QUERY_BASE_URL}/get_sessions_by_collection_id/{selected_id}", headers=_get_headers())
if response.status_code != 200:
    st.error("Failed to fetch sessions.")
    st.stop()

data = response.json()
sessions_by_folder = data.get("folders", {}) or {}

# Flatten for metrics and filtering
flat_sessions = []
for folder, sessions in sessions_by_folder.items():
    for s in sessions:
        s_copy = dict(s)
        s_copy["__folder"] = folder
        flat_sessions.append(s_copy)

# Apply filters
if search_text:
    needle = search_text.lower()

    def _matches(session: dict, term: str) -> bool:
        title = str(session.get("title") or "").lower()
        goal = str(session.get("goal") or "").lower()
        email = str(session.get("user_email") or "").lower()
        return (title.find(term) != -1) or (goal.find(term) != -1) or (email.find(term) != -1)

    flat_sessions = [s for s in flat_sessions if _matches(s, needle)]

if show_only_favorites:
    flat_sessions = [s for s in flat_sessions if s.get("is_favorite")]

# Sorting
if sort_by == "Created (newest)":
    flat_sessions.sort(key=lambda s: _to_dt(s.get("created_at")) or datetime.min, reverse=True)
elif sort_by == "Created (oldest)":
    flat_sessions.sort(key=lambda s: _to_dt(s.get("created_at")) or datetime.max)
elif sort_by == "Title (A→Z)":
    flat_sessions.sort(key=lambda s: (s.get("title") or "").lower())
elif sort_by == "Title (Z→A)":
    flat_sessions.sort(key=lambda s: (s.get("title") or "").lower(), reverse=True)

# Summary metrics
total_sessions = len(flat_sessions)
fav_count = sum(1 for s in flat_sessions if s.get("is_favorite"))
unique_folders = len({s.get("__folder") for s in flat_sessions})

m1, m2, m3 = st.columns(3)
with m1:
    st.metric("Total Sessions", f"{total_sessions}")
with m2:
    st.metric("Favorites", f"{fav_count}")
with m3:
    st.metric("Folders", f"{unique_folders}")

st.markdown("---")

# Group again by folder for presentation
grouped: Dict[str, List[Dict[str, Any]]] = {}
for s in flat_sessions:
    grouped.setdefault(s.get("__folder") or "Uncategorized", []).append(s)

# Render folder sections
for folder, sessions in grouped.items():
    with st.expander(f"📁 {folder} ({len(sessions)})", expanded=True):
        for session in sessions:
            with st.container():
                st.markdown("<div class='session-card'>", unsafe_allow_html=True)
                c1, c2 = st.columns([6, 2])
                with c1:
                    title = session.get("title") or "Untitled Session"
                    created = session.get("created_at") or "-"
                    goal = session.get("goal") or "-"
                    email = session.get("user_email") or "-"
                    uploaded = session.get("uploaded_pdfs")
                    uploaded_str = ", ".join(uploaded) if isinstance(uploaded, list) else str(uploaded)
                    fav_badge = "<span class='badge'>⭐ Favorite</span>" if session.get("is_favorite") else ""
                    st.markdown(f"### {title} {fav_badge}")
                    st.markdown(f"<div class='meta'>Created: {created}</div>", unsafe_allow_html=True)
                    st.markdown(f"<div class='meta'>Goal: {goal}</div>", unsafe_allow_html=True)
                    st.markdown(f"<div class='meta'>User: {email}</div>", unsafe_allow_html=True)
                    st.markdown(f"<div class='meta'>Uploaded PDFs: {uploaded_str}</div>", unsafe_allow_html=True)
                with c2:
                    d1, d2 = st.columns(2)
                    with d1:
                        if st.button("🗑️ Delete", key=f"delete_{session['id']}"):
                            delete_res = safe_request(
                                "delete",
                                f"{QUERY_BASE_URL}/delete_session/{session['id']}",
                                headers=_get_headers(),
                            )
                            if delete_res.status_code == 200:
                                st.success("Session deleted. Reloading...")
                                st.rerun()
                            else:
                                st.error("Failed to delete session.")
                    with d2:
                        is_fav = session.get("is_favorite")
                        fav_label = "⭐ Unfavorite" if is_fav else "☆ Favorite"
                        if st.button(fav_label, key=f"fav_{session['id']}"):
                            toggle_fav = not is_fav
                            fav_res = safe_request(
                                "put",
                                f"{QUERY_BASE_URL}/update_favorite_status/{session['id']}?is_favorite={str(toggle_fav).lower()}",
                                headers=_get_headers(),
                            )
                            if fav_res.status_code == 200:
                                st.success("Favorite status updated. Reloading...")
                                st.rerun()
                            else:
                                st.error("Failed to update favorite status.")
                st.markdown("</div>", unsafe_allow_html=True)
