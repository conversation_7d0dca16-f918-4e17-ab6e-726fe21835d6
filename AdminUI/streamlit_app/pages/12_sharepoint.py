from typing import Dict

import streamlit as st
from streamlit_app.constants import LOADER_BASE_URL
from streamlit_app.utils.api_utils import send_json_request
from streamlit_app.utils.auth_utils import ensure_login
from streamlit_app.utils.db_utils import (
    get_all_collections,
    get_or_set_selected_collection,
)

# Ensure user is logged in
token, headers, roles = ensure_login()

PAGE_TITLE = "SharePoint Upload"
st.set_page_config(page_title=PAGE_TITLE, page_icon="📤")
st.markdown(f"# {PAGE_TITLE}")

st.write("Hier können Dokumente von einem SharePoint-Standort in eine Sammlung hochgeladen werden.")

# API Endpoints
LOADER_URL = f"{LOADER_BASE_URL}"


def get_sharepoint_folders():
    """Fetch SharePoint folder names from the API."""
    try:
        response = send_json_request(f"{LOADER_URL}/sharepoint/folder/names", method="get", headers=headers)
        if response.status_code == 200:
            sites = response.json().get("folder_names", [])
            # Return a list of site names
            return sites
    except Exception as e:
        st.warning(f"Failed to fetch SharePoint sites: {e}")
    return []


# --- Collection Handling ---
selected_collection_id = get_or_set_selected_collection()
all_collections = get_all_collections()
collection_dict: Dict[str, Dict] = {str(c["id"]): c for c in all_collections}

# --- UI Elements ---
sharepoint_folders = get_sharepoint_folders()

if not selected_collection_id or selected_collection_id not in collection_dict:
    st.warning("Bitte wählen Sie eine Collection aus oder erstellen Sie zuerst eine Collection.")
    st.stop()

if not sharepoint_folders:
    st.warning("Keine SharePoint-Ordner gefunden oder Fehler beim Abrufen.")
    st.stop()

selected_collection = collection_dict[selected_collection_id]
st.info(f"Ausgewählte Sammlung: **{selected_collection['name']}**")

# --- Form for selection ---
with st.form("sharepoint_upload_form"):
    selected_folder_name = st.selectbox(
        "Wählen Sie einen SharePoint-Ordner aus:", options=sharepoint_folders, help="Der SharePoint-Ordner, von dem die Dokumente importiert werden sollen."
    )

    submitted = st.form_submit_button("Upload starten")

if submitted:
    if selected_collection_id and selected_folder_name:
        collection_id = selected_collection_id
        collection_name = selected_collection["name"]

        st.info(f"Starte den Upload von SharePoint-Dokumenten vom Ordner '{selected_folder_name}' in die Sammlung '{collection_name}'...")

        with st.spinner("Verarbeitung läuft... Dieser Vorgang kann einige Minuten dauern."):
            try:
                upload_url = f"{LOADER_URL}/collection/{collection_id}/sharepoint/folder/{selected_folder_name}"
                response = send_json_request(upload_url, method="post", headers=headers)

                if response.status_code == 200:
                    st.success("SharePoint-Ordner erfolgreich verarbeitet!")
                    result_data = response.json()
                    st.json(result_data)
                elif response.status_code == 404:
                    st.error(f"Fehler: {response.json().get('message')}")
                else:
                    st.error(f"Ein unerwarteter Fehler ist aufgetreten: {response.status_code}")
                    st.json(response.json())

            except Exception as e:
                st.error(f"Ein Fehler ist während des API-Aufrufs aufgetreten: {e}")
    else:
        st.warning("Bitte wählen Sie einen SharePoint-Standort aus.")
