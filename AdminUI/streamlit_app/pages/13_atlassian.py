"""Atlassian Integration Management Page."""

from streamlit_app.utils.auth_utils import ensure_login

token, headers, roles = ensure_login()
from datetime import datetime, timedelta

import pandas as pd
import requests
import streamlit as st
from streamlit_app.constants import LOADER_BASE_URL
from streamlit_app.utils.api_utils import send_json_request
from streamlit_app.utils.db_utils import (
    get_all_collections,
    get_or_set_selected_collection,
)

PAGE_TITLE = "Atlassian Integration"
st.set_page_config(page_title=PAGE_TITLE, page_icon="🔧")

st.markdown(f"# {PAGE_TITLE}")
st.write("Connect to Atlassian Jira and Confluence to view projects, issues, and pages.")

# Initialize session state for Atlassian connection
if "atlassian_connected" not in st.session_state:
    st.session_state.atlassian_connected = False
if "atlassian_base_url" not in st.session_state:
    st.session_state.atlassian_base_url = ""
if "atlassian_username" not in st.session_state:
    st.session_state.atlassian_username = ""

# Collection selection (via sidebar)
selected_collection_id = get_or_set_selected_collection()
all_collections = get_all_collections()
collection_dict = {str(c["id"]): str(c["name"]) for c in all_collections}

# Main tabs
tab_auth, tab_jira, tab_confluence = st.tabs(["🔐 Authentication", "📋 Jira Issues", "📄 Confluence Spaces"])

# ─────────────────────────────────────────────────────────────────────
# Authentication Tab
# ─────────────────────────────────────────────────────────────────────
with tab_auth:
    st.subheader("Atlassian Authentication")

    if st.session_state.atlassian_connected:
        st.success("✅ Connected to Atlassian")
        st.write(f"**Base URL:** {st.session_state.atlassian_base_url}")
        st.write(f"**Username:** {st.session_state.atlassian_username}")

        col1, col2 = st.columns(2)
        with col1:
            if st.button("🔌 Disconnect", key="disconnect"):
                try:
                    response = send_json_request(
                        f"{LOADER_BASE_URL}/atlassian/cleanup",
                        method="post",
                        headers=headers,
                    )
                    if isinstance(response, requests.Response) and response.status_code == 200:
                        st.session_state.atlassian_connected = False
                        st.session_state.atlassian_base_url = ""
                        st.session_state.atlassian_username = ""
                        st.success("Disconnected successfully!")
                        st.rerun()
                    else:
                        st.error("Failed to disconnect.")
                except Exception as e:
                    st.error(f"Error disconnecting: {e}")

        with col2:
            if st.button("🔄 Test Connection", key="test_connection"):
                try:
                    with st.spinner("Testing connection..."):
                        response = send_json_request(
                            f"{LOADER_BASE_URL}/atlassian/jira/projects/simple",
                            method="get",
                            headers=headers,
                        )
                    if isinstance(response, requests.Response) and response.status_code == 200:
                        projects = response.json().get("projects", [])
                        st.success(f"✅ Connection successful! Found {len(projects)} Jira projects.")
                    else:
                        st.error("❌ Connection test failed. Please re-authenticate.")
                    st.session_state.atlassian_connected = False
                except Exception as e:
                    st.error(f"Error testing connection: {e}")
                    st.session_state.atlassian_connected = False

    else:
        st.info("🔐 Please authenticate with Atlassian to access your Jira projects and Confluence spaces.")

        # API Token Authentication
        st.markdown("### API Token Authentication")
        st.markdown(
            """
        **Steps to get your API token:**
        1. Go to your [Atlassian Account Settings](https://id.atlassian.com/manage-profile/security/api-tokens)
        2. Create an API token in the Security section
        3. Use your email and the token below
        """
        )

        with st.form("atlassian_auth_form"):
            base_url = st.text_input(
                "Atlassian Base URL", value=st.session_state.atlassian_base_url, placeholder="https://yourcompany.atlassian.net", help="Your Atlassian Cloud instance URL"
            )
            username = st.text_input("Username (Email)", value=st.session_state.atlassian_username, placeholder="<EMAIL>", help="Your Atlassian account email")
            api_token = st.text_input("API Token", type="password", placeholder="Your Atlassian API token", help="Generate this in your Atlassian Account Settings")

            submitted = st.form_submit_button("🔐 Connect to Atlassian", type="primary")

        if submitted:
            if not base_url or not username or not api_token:
                st.error("❌ Please fill in all fields.")
            else:
                try:
                    with st.spinner("Authenticating with Atlassian..."):
                        auth_data = {"base_url": base_url.strip(), "username": username.strip(), "api_token": api_token.strip()}

                        response = send_json_request(
                            f"{LOADER_BASE_URL}/atlassian/auth",
                            method="post",
                            headers=headers,
                            json_data=auth_data,
                        )

                    if isinstance(response, requests.Response) and response.status_code == 200:
                        result = response.json()
                        if result.get("success"):
                            st.session_state.atlassian_connected = True
                            st.session_state.atlassian_base_url = base_url.strip()
                            st.session_state.atlassian_username = username.strip()
                            st.success("✅ Successfully authenticated with Atlassian!")
                            st.rerun()
                        else:
                            st.error(f"❌ Authentication failed: {result.get('message', 'Unknown error')}")
                    else:
                        st.error(f"❌ Authentication failed: {response.text if response else 'Network error'}")
                except Exception as e:
                    st.error(f"❌ Error authenticating: {e}")

# ─────────────────────────────────────────────────────────────────────
# Jira Tab
# ─────────────────────────────────────────────────────────────────────
with tab_jira:
    st.subheader("Jira Issues")

    if not st.session_state.atlassian_connected:
        st.warning("Please connect to Atlassian first in the Authentication tab.")
    else:
        # Auto-load projects on page load
        if "jira_projects" not in st.session_state:
            try:
                with st.spinner("Loading Jira projects..."):
                    response = send_json_request(
                        f"{LOADER_BASE_URL}/atlassian/jira/projects/simple",
                        method="get",
                        headers=headers,
                    )

                if isinstance(response, requests.Response) and response.status_code == 200:
                    data = response.json()
                    projects = data.get("projects", [])
                    st.session_state.jira_projects = projects
                    if projects:
                        st.success(f"✅ Loaded {len(projects)} Jira projects")
                else:
                    st.error("Failed to load Jira projects")
                    st.session_state.jira_projects = []
            except Exception as e:
                st.error(f"Error loading Jira projects: {e}")
                st.session_state.jira_projects = []

        # Project selection
        if st.session_state.get("jira_projects"):
            projects = st.session_state.jira_projects
            project_options = {f"{p['key']} - {p['name']}": p["key"] for p in projects}

            selected_project = st.selectbox("Select a project to view issues:", options=[""] + list(project_options.keys()), key="jira_project_select")

            if selected_project:
                project_key = project_options[selected_project]

                # Auto-load issues when project is selected
                if "jira_issues" not in st.session_state or st.session_state.get("current_project_key") != project_key:
                    try:
                        with st.spinner(f"Loading all issues from {project_key}..."):
                            response = send_json_request(
                                f"{LOADER_BASE_URL}/atlassian/jira/projects/{project_key}/issues?max_results=10000",
                                method="get",
                                headers=headers,
                            )

                        if isinstance(response, requests.Response) and response.status_code == 200:
                            data = response.json()
                            issues = data.get("issues", [])
                            st.session_state.jira_issues = issues
                            st.session_state.current_project_key = project_key
                            st.session_state.selected_jira_project = project_key

                            if issues:
                                st.success(f"🎯 Loaded {len(issues)} total issues (tasks + subtasks)")
                            else:
                                st.info("No issues found in this project")
                        else:
                            st.error("Failed to load issues")
                            st.session_state.jira_issues = []
                    except Exception as e:
                        st.error(f"Error loading issues: {e}")
                        st.session_state.jira_issues = []

        # Show issues table with filtering and selection if data is available
        if "jira_issues" in st.session_state and st.session_state.jira_issues:
            st.markdown("---")
            st.subheader("🔍 Filter & Select Issues")

            issues = st.session_state.jira_issues

            # Create filter columns
            filter_col1, filter_col2, filter_col3, filter_col4 = st.columns(4)

            with filter_col1:
                # Get unique statuses
                all_statuses = sorted({issue.get("status", "") for issue in issues if issue.get("status")})
                selected_statuses = st.multiselect("Status", all_statuses, key="status_filter")

            with filter_col2:
                # Get unique types
                all_types = sorted({issue.get("issue_type", "") for issue in issues if issue.get("issue_type")})
                selected_types = st.multiselect("Type", all_types, key="type_filter")

            with filter_col3:
                # Get unique assignees
                all_assignees = sorted({issue.get("assignee", "") for issue in issues if issue.get("assignee")})
                selected_assignees = st.multiselect("Assignee", all_assignees, key="assignee_filter")

            with filter_col4:
                # Date range filter for created date
                date_filter = st.selectbox("Created Date", ["All", "Last 7 days", "Last 30 days", "Last 90 days"], key="date_filter")

            # Apply filters
            filtered_issues = issues.copy()

            if selected_statuses:
                filtered_issues = [issue for issue in filtered_issues if issue.get("status", "") in selected_statuses]

            if selected_types:
                filtered_issues = [issue for issue in filtered_issues if issue.get("issue_type", "") in selected_types]

            if selected_assignees:
                filtered_issues = [issue for issue in filtered_issues if issue.get("assignee", "") in selected_assignees]

            if date_filter != "All":
                now = datetime.now()
                if date_filter == "Last 7 days":
                    cutoff = now - timedelta(days=7)
                elif date_filter == "Last 30 days":
                    cutoff = now - timedelta(days=30)
                elif date_filter == "Last 90 days":
                    cutoff = now - timedelta(days=90)

                date_filtered = []
                for issue in filtered_issues:
                    created_str = issue.get("created", "")
                    if created_str:
                        try:
                            # Parse ISO date format from Jira
                            created_date = datetime.fromisoformat(created_str.replace("Z", "+00:00"))
                            if created_date >= cutoff:
                                date_filtered.append(issue)
                        except (ValueError, TypeError):
                            date_filtered.append(issue)  # Include if date parsing fails
                    else:
                        date_filtered.append(issue)
                filtered_issues = date_filtered

            st.info(f"📊 Showing {len(filtered_issues)} of {len(issues)} issues")

            if filtered_issues:
                # Selection controls
                select_col1, select_col2, select_col3 = st.columns([2, 2, 4])

                with select_col1:
                    select_all = st.checkbox("Select All", key="select_all_issues")

                with select_col2:
                    if st.button("Clear Selection", key="clear_selection"):
                        st.session_state.select_all_issues = False
                        st.rerun()

                # Create DataFrame for display with selection checkboxes
                df_data = []

                for issue in filtered_issues:
                    # Build a compact activity preview (first 3 items)
                    act_list = issue.get("activities", []) or []
                    activity_preview = " | ".join(act_list[:3])

                    description_text = issue.get("description", "") or ""

                    df_data.append(
                        {
                            "Select": select_all,  # Use select_all state
                            "Key": issue.get("key", ""),
                            "Parent": issue.get("parent_task", ""),
                            "Summary": issue.get("summary", ""),
                            "Description": description_text,
                            "Activity": activity_preview,
                            "Type": issue.get("issue_type", ""),
                            "Status": issue.get("status", ""),
                            "Priority": issue.get("priority", ""),
                            "Assignee": issue.get("assignee", ""),
                            "Created": issue.get("created", ""),
                        }
                    )

                df = pd.DataFrame(df_data)

                # Use st.data_editor for interactive selection
                edited_df = st.data_editor(
                    df,
                    use_container_width=True,
                    hide_index=True,
                    column_config={
                        "Select": st.column_config.CheckboxColumn(
                            "Select",
                            help="Select issues for viewing",
                            default=False,
                        ),
                        "Description": st.column_config.TextColumn(
                            "Description",
                            help="Issue description",
                            max_chars=200,
                        ),
                        "Activity": st.column_config.TextColumn(
                            "Activity",
                            help="Comments, worklogs, and history",
                            max_chars=300,
                        ),
                    },
                    disabled=["Key", "Parent", "Summary", "Description", "Activity", "Type", "Status", "Priority", "Assignee", "Created"],
                    key="issues_editor",
                )

                # Get selected issue keys
                selected_issue_keys = []
                for _, row in edited_df.iterrows():
                    if row["Select"]:
                        selected_issue_keys.append(row["Key"])

                # Show selection summary and indexing controls
                if selected_issue_keys:
                    st.success(
                        f"🎯 Selected {len(selected_issue_keys)} issues: {', '.join(selected_issue_keys[:5])}"
                        + (f" and {len(selected_issue_keys) - 5} more..." if len(selected_issue_keys) > 5 else "")
                    )

                    # Collection selection and indexing
                    st.markdown("---")
                    st.subheader("🚀 Index Selected Issues")

                    index_col1, index_col2, index_col3 = st.columns([2, 2, 1])

                    with index_col1:
                        # Collection selection for indexing
                        if collection_dict:
                            selected_collection_for_index = st.selectbox(
                                "Select Collection for Indexing",
                                options=list(collection_dict.keys()),
                                format_func=lambda x: f"{collection_dict[x]} ({x})",
                                key="jira_collection_select",
                                help="Choose which collection to index the selected issues into",
                            )
                        else:
                            st.warning("⚠️ No collections available. Please create a collection first.")
                            selected_collection_for_index = None

                    with index_col2:
                        st.metric("Issues to Index", len(selected_issue_keys))
                        if selected_collection_for_index:
                            st.info(f"📁 Target: {collection_dict[selected_collection_for_index]}")

                    with index_col3:
                        if selected_collection_for_index and selected_issue_keys:
                            if st.button("🚀 Index Issues", type="primary", key="index_selected_issues", help="Start indexing selected issues for embedding search"):

                                with st.spinner(f"Indexing {len(selected_issue_keys)} issues..."):
                                    try:
                                        project_key = st.session_state.get("selected_jira_project", "")
                                        if not project_key:
                                            st.error("❌ No project selected")
                                        else:
                                            # Prepare request payload
                                            payload = {"project_key": project_key, "issue_keys": selected_issue_keys, "collection_id": selected_collection_for_index}

                                            # Send indexing request
                                            response = send_json_request(
                                                f"{LOADER_BASE_URL}/atlassian/jira/projects/{project_key}/index",
                                                method="post",
                                                headers=headers,
                                                json_data=payload,
                                            )

                                            if isinstance(response, requests.Response) and response.status_code == 200:
                                                result = response.json()
                                                indexed_count = result.get("indexed_count", 0)
                                                failed_items = result.get("failed_items", [])

                                                if indexed_count > 0:
                                                    st.success(f"✅ Successfully queued {indexed_count} issues for indexing!")
                                                    if failed_items:
                                                        st.warning(f"⚠️ {len(failed_items)} issues failed:")
                                                        for item in failed_items[:5]:  # Show first 5 failures
                                                            st.text(f"  • {item}")
                                                        if len(failed_items) > 5:
                                                            st.text(f"  ... and {len(failed_items) - 5} more")
                                                else:
                                                    st.error("❌ No issues were successfully queued for indexing")
                                                    if failed_items:
                                                        st.error("Failures:")
                                                        for item in failed_items:
                                                            st.text(f"  • {item}")
                                            else:
                                                error_msg = response.text if response else "Network error"
                                                st.error(f"❌ Indexing failed: {error_msg}")

                                    except Exception as e:
                                        st.error(f"❌ Error during indexing: {e}")
                else:
                    st.info("ℹ️ No issues selected")

# ─────────────────────────────────────────────────────────────────────
# Confluence Tab
# ─────────────────────────────────────────────────────────────────────
with tab_confluence:
    st.subheader("📄 Confluence Spaces and Pages")

    if not st.session_state.atlassian_connected:
        st.warning("⚠️ Please authenticate with Atlassian first in the Authentication tab.")
    else:
        # Auto-load all spaces and their pages on page load
        if "confluence_pages" not in st.session_state:
            try:
                with st.spinner("Loading all Confluence spaces and pages..."):
                    # First, get all spaces
                    spaces_response = send_json_request(
                        f"{LOADER_BASE_URL}/atlassian/confluence/spaces/simple",
                        method="get",
                        headers=headers,
                    )

                    if isinstance(spaces_response, requests.Response) and spaces_response.status_code == 200:
                        spaces_data = spaces_response.json()
                        spaces = spaces_data.get("spaces", [])

                        all_pages = []
                        if spaces:
                            st.info(f"🔍 Found {len(spaces)} spaces. Loading all pages...")

                            # Fetch pages from all spaces
                            for space in spaces:
                                space_key = space.get("key", "")
                                space_name = space.get("name", "")

                                try:
                                    pages_response = send_json_request(
                                        f"{LOADER_BASE_URL}/atlassian/confluence/spaces/{space_key}/pages", method="get", headers=headers, params={"max_results": 10000}
                                    )

                                    if isinstance(pages_response, requests.Response) and pages_response.status_code == 200:
                                        pages_data = pages_response.json()
                                        pages = pages_data.get("pages", [])

                                        # Add space information to each page
                                        for page in pages:
                                            page["space_key"] = space_key
                                            page["space_name"] = space_name
                                            all_pages.append(page)

                                except Exception as e:
                                    st.warning(f"Could not load pages from space {space_key}: {e}")
                                    continue

                            st.session_state.confluence_pages = all_pages
                            if all_pages:
                                st.success(f"✅ Loaded {len(all_pages)} total pages from {len(spaces)} spaces")
                            else:
                                st.info("No pages found in any space")
                        else:
                            st.info("ℹ️ No Confluence spaces found.")
                            st.session_state.confluence_pages = []
                    else:
                        st.error("Failed to load Confluence spaces")
                        st.session_state.confluence_pages = []
            except Exception as e:
                st.error(f"Error loading Confluence data: {e}")
                st.session_state.confluence_pages = []

        # Show pages table with filtering and selection if data is available
        if "confluence_pages" in st.session_state and st.session_state.confluence_pages:
            st.markdown("---")
            st.subheader("🔍 Filter & Select Pages")

            pages = st.session_state.confluence_pages

            # Create filter columns
            filter_col1, filter_col2, filter_col3, filter_col4, filter_col5 = st.columns(5)

            with filter_col1:
                # Get unique spaces - only show space names, not keys
                all_spaces = sorted({page.get("space_name", "") for page in pages if page.get("space_name")})
                selected_spaces = st.multiselect("Space", all_spaces, key="confluence_space_filter")

            with filter_col2:
                # Get unique creators
                all_creators = sorted({page.get("creator", "") for page in pages if page.get("creator")})
                selected_creators = st.multiselect("Creator", all_creators, key="confluence_creator_filter")

            with filter_col3:
                # Date range filter for created date
                date_filter = st.selectbox("Created Date", ["All", "Last 7 days", "Last 30 days", "Last 90 days"], key="confluence_date_filter")

            with filter_col4:
                # Get unique statuses
                all_statuses = sorted({page.get("status", "") for page in pages if page.get("status")})
                selected_statuses = st.multiselect("Status", all_statuses, key="confluence_status_filter")

            with filter_col5:
                # Title search
                title_search = st.text_input("Title Search", key="confluence_title_search", placeholder="Search in titles...")

            # Apply filters
            filtered_pages = pages.copy()

            if selected_spaces:
                filtered_pages = [page for page in filtered_pages if page.get("space_name", "") in selected_spaces]

            if selected_creators:
                filtered_pages = [page for page in filtered_pages if page.get("creator", "") in selected_creators]

            if selected_statuses:
                filtered_pages = [page for page in filtered_pages if page.get("status", "") in selected_statuses]

            if title_search:
                filtered_pages = [page for page in filtered_pages if title_search.lower() in page.get("title", "").lower()]

            if date_filter != "All":
                now = datetime.now()
                if date_filter == "Last 7 days":
                    cutoff = now - timedelta(days=7)
                elif date_filter == "Last 30 days":
                    cutoff = now - timedelta(days=30)
                elif date_filter == "Last 90 days":
                    cutoff = now - timedelta(days=90)

                date_filtered = []
                for page in filtered_pages:
                    created_str = page.get("created", "")
                    if created_str:
                        try:
                            # Parse ISO date format from Confluence
                            created_date = datetime.fromisoformat(created_str.replace("Z", "+00:00"))
                            if created_date >= cutoff:
                                date_filtered.append(page)
                        except (ValueError, TypeError):
                            date_filtered.append(page)  # Include if date parsing fails
                    else:
                        date_filtered.append(page)
                filtered_pages = date_filtered

            st.info(f"📊 Showing {len(filtered_pages)} of {len(pages)} pages")

            if filtered_pages:
                # Selection controls
                select_col1, select_col2, select_col3 = st.columns([2, 2, 4])

                with select_col1:
                    select_all = st.checkbox("Select All", key="select_all_pages")

                with select_col2:
                    if st.button("Clear Selection", key="clear_page_selection"):
                        st.session_state.select_all_pages = False
                        st.rerun()

                # Create DataFrame for display with selection checkboxes
                df_data = []

                for page in filtered_pages:
                    # Get content preview
                    content_preview = (page.get("content", "")[:100] + "...") if page.get("content") else "No content"

                    # Format created date
                    created_date = page.get("created", "")
                    if created_date:
                        try:
                            # Ensure created_date is string
                            created_date_str = str(created_date) if created_date else ""
                            created_dt = datetime.fromisoformat(created_date_str.replace("Z", "+00:00"))
                            created_formatted = created_dt.strftime("%Y-%m-%d")
                        except (ValueError, TypeError):
                            created_date_str = str(created_date) if created_date else ""
                            created_formatted = created_date_str[:10] if len(created_date_str) >= 10 else created_date_str
                    else:
                        created_formatted = ""

                    df_data.append(
                        {
                            "Select": select_all,  # Use select_all state
                            "ID": page.get("id", ""),
                            "Space": page.get("space_name", ""),
                            "Space_Key": page.get("space_key", ""),  # Add space key for mapping
                            "Title": page.get("title", ""),
                            "Creator": page.get("creator", ""),
                            "Created": created_formatted,
                            "Status": page.get("status", ""),
                            "Content Preview": content_preview,
                            "Labels": ", ".join(page.get("labels", [])) if page.get("labels") else "",
                            "Parent": page.get("parent", ""),
                        }
                    )

                df = pd.DataFrame(df_data)

                # Use st.data_editor for interactive selection
                edited_df = st.data_editor(
                    df,
                    use_container_width=True,
                    hide_index=True,
                    column_config={
                        "Select": st.column_config.CheckboxColumn(
                            "Select",
                            help="Select pages for processing",
                            default=False,
                        ),
                        "Space_Key": None,  # Hide Space_Key column
                        "Content Preview": st.column_config.TextColumn(
                            "Content Preview",
                            help="Page content preview",
                            max_chars=200,
                        ),
                        "Title": st.column_config.TextColumn(
                            "Title",
                            help="Page title",
                            max_chars=100,
                        ),
                    },
                    disabled=["ID", "Space", "Space_Key", "Title", "Creator", "Created", "Status", "Content Preview", "Labels", "Parent"],
                    key="pages_editor",
                )

                # Get selected page IDs
                selected_page_ids = []
                for _, row in edited_df.iterrows():
                    if row["Select"]:
                        selected_page_ids.append(row["ID"])

                # Show selection summary and indexing controls
                if selected_page_ids:
                    st.success(
                        f"🎯 Selected {len(selected_page_ids)} pages"
                        + (
                            f": {', '.join([edited_df[edited_df['ID'] == pid]['Title'].iloc[0][:30] + '...' for pid in selected_page_ids[:3]])}"
                            if len(selected_page_ids) <= 3
                            else " from various spaces"
                        )
                    )

                    # Collection selection and indexing for Confluence pages
                    st.markdown("---")
                    st.subheader("🚀 Index Selected Pages")

                    conf_index_col1, conf_index_col2, conf_index_col3 = st.columns([2, 2, 1])

                    with conf_index_col1:
                        # Collection selection for indexing
                        if collection_dict:
                            selected_collection_for_confluence = st.selectbox(
                                "Select Collection for Indexing",
                                options=list(collection_dict.keys()),
                                format_func=lambda x: f"{collection_dict[x]} ({x})",
                                key="confluence_collection_select",
                                help="Choose which collection to index the selected pages into",
                            )
                        else:
                            st.warning("⚠️ No collections available. Please create a collection first.")
                            selected_collection_for_confluence = None

                    with conf_index_col2:
                        st.metric("Pages to Index", len(selected_page_ids))
                        if selected_collection_for_confluence:
                            st.info(f"📁 Target: {collection_dict[selected_collection_for_confluence]}")

                    with conf_index_col3:
                        if selected_collection_for_confluence and selected_page_ids:
                            if st.button("🚀 Index Pages", type="primary", key="index_selected_pages", help="Start indexing selected pages for embedding search"):

                                with st.spinner(f"Indexing {len(selected_page_ids)} pages..."):
                                    try:
                                        # Get unique space keys from selected pages
                                        selected_spaces = set()
                                        for page_id in selected_page_ids:
                                            page_row = edited_df[edited_df["ID"] == page_id]
                                            if not page_row.empty:
                                                space_key = page_row["Space_Key"].iloc[0]
                                                if space_key:
                                                    selected_spaces.add(space_key)

                                        if not selected_spaces:
                                            st.error("❌ No valid space keys found for selected pages")
                                            # Debug info
                                            st.error("Debug: Checking page space keys...")
                                            for page_id in selected_page_ids:
                                                page_row = edited_df[edited_df["ID"] == page_id]
                                                if not page_row.empty:
                                                    space_name = page_row["Space"].iloc[0]
                                                    space_key = page_row["Space_Key"].iloc[0]
                                                    st.error(f"Page {page_id}: Space='{space_name}', Space_Key='{space_key}'")
                                        else:
                                            success_count = 0
                                            total_failed = 0
                                            all_failed_items = []

                                            # Index pages for each space separately
                                            for space_key in selected_spaces:
                                                # Get page IDs for this space
                                                space_page_ids = []
                                                for page_id in selected_page_ids:
                                                    page_row = edited_df[edited_df["ID"] == page_id]
                                                    if not page_row.empty and page_row["Space_Key"].iloc[0] == space_key:
                                                        space_page_ids.append(page_id)

                                                if space_page_ids:
                                                    # Prepare request payload for this space
                                                    payload = {"space_key": space_key, "page_ids": space_page_ids, "collection_id": selected_collection_for_confluence}

                                                    # Send indexing request
                                                    response = send_json_request(
                                                        f"{LOADER_BASE_URL}/atlassian/confluence/spaces/{space_key}/index",
                                                        method="post",
                                                        headers=headers,
                                                        json_data=payload,
                                                    )

                                                    if isinstance(response, requests.Response) and response.status_code == 200:
                                                        result = response.json()
                                                        indexed_count = result.get("indexed_count", 0)
                                                        failed_items = result.get("failed_items", [])

                                                        success_count += indexed_count
                                                        total_failed += len(failed_items)
                                                        all_failed_items.extend(failed_items)
                                                    else:
                                                        error_msg = response.text if response else "Network error"
                                                        st.error(f"❌ Indexing failed for space {space_key}: {error_msg}")
                                                        total_failed += len(space_page_ids)

                                            # Show final results
                                            if success_count > 0:
                                                st.success(f"✅ Successfully queued {success_count} pages for indexing!")
                                                if all_failed_items:
                                                    st.warning(f"⚠️ {len(all_failed_items)} pages failed:")
                                                    for item in all_failed_items[:5]:  # Show first 5 failures
                                                        st.text(f"  • {item}")
                                                    if len(all_failed_items) > 5:
                                                        st.text(f"  ... and {len(all_failed_items) - 5} more")
                                            else:
                                                st.error("❌ No pages were successfully queued for indexing")
                                                if all_failed_items:
                                                    st.error("Failures:")
                                                    for item in all_failed_items:
                                                        st.text(f"  • {item}")

                                    except Exception as e:
                                        st.error(f"❌ Error during indexing: {e}")
                else:
                    st.info("ℹ️ No pages selected")
