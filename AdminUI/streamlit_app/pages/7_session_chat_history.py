"""Chat history management page for Redis in Streamlit app."""

from streamlit_app.utils.auth_utils import ensure_login
from streamlit_app.utils.http_utils import safe_request

# obtain roles lazily when needed
_, _, roles = ensure_login()
import json
import os

import redis
import streamlit as st
from streamlit_app.constants import LOADER_BASE_URL

REDIS_URL = os.getenv("REDIS_QUERY_HISTORY_URL", "redis://redis:6379/2")
r = redis.Redis.from_url(REDIS_URL, decode_responses=True)

st.set_page_config(page_title="Redis Chat History Manager", layout="wide")
st.title("🧠 Redis Chat History Manager")

# Lightweight styles for nicer cards and badges
st.markdown(
    """
    <style>
    .key-card { padding: 12px 16px; border: 1px solid #eaeaea; border-radius: 10px; background: #fff; box-shadow: 0 1px 2px rgba(0,0,0,0.04); margin-bottom: 10px; }
    .meta { color: #6b7280; font-size: 0.9rem; }
    .badge { display: inline-block; padding: 2px 10px; border-radius: 999px; font-size: 0.75rem; border: 1px solid #e5e7eb; color: #374151; background: #f9fafb; margin-left: 6px; }
    .danger-text { color: #b91c1c; }
    </style>
    """,
    unsafe_allow_html=True,
)


def get_all_collections_redis():
    """Retrieve all unique chat history collections from Redis."""
    keys = r.keys("chat_history:*")
    collections = set()
    for key in keys:
        parts = key.split(":")
        if len(parts) >= 3:
            collections.add(parts[1])
    return sorted(collections)


def get_all_collections():
    """Loader Service – /collections"""
    _token, headers, _ = ensure_login()
    url = f"{LOADER_BASE_URL}/collections"
    resp = safe_request("get", url, headers=headers, timeout=30)
    resp.raise_for_status()
    return resp.json()["collections"]


def remove_db_collections_by_redis_names(redis_collection_names, db_collections):
    """Remove collections from the database that are not in Redis."""
    return [col for col in db_collections if col["name"] not in redis_collection_names]


# Gather collection info and guidance messages
collections = get_all_collections_redis()
redis_collection_names = [name.split("_semantic_search")[0] for name in collections]
db_collection_names = get_all_collections()
collections_without_memory = remove_db_collections_by_redis_names(redis_collection_names, db_collection_names)
collection_messages = []
for col in collections_without_memory:
    is_memory_on = col["custom_config"].get("is_memory_on")
    if is_memory_on is None:
        msg = f"{col['name']} — No memory configuration found."
    elif not is_memory_on:
        msg = f"{col['name']} — Chat history disabled. Use 'alter collection' to enable memory."
    else:
        msg = f"{col['name']} — Memory enabled but no history entries yet."
    collection_messages.append(msg)

# Sidebar filters & actions
with st.sidebar:
    st.header("Filters & Actions")
    if collection_messages:
        st.selectbox("Why there is no chat history?", collection_messages)

    selected_collection = st.selectbox(
        "Select Collection",
        options=redis_collection_names,
        help="Only collections with chat history namespace are shown",
    )

    search_text = st.text_input("Search in keys/values", placeholder="Type to filter...")
    type_filter = st.selectbox("Type filter", options=["All", "string", "list"], index=0)
    page_size = st.slider("Items per page", min_value=10, max_value=100, value=25, step=5)
    refresh = st.button("🔄 Refresh")

    st.markdown("---")
    st.markdown("**Danger zone**")
    delete_all_confirm = st.checkbox("I'm sure to delete ALL keys in this collection")
    delete_all = st.button("🗑️ Delete ALL keys in collection", disabled=not delete_all_confirm, type="secondary")

if refresh:
    st.rerun()

# Selected collection redis pattern
if not selected_collection:
    st.warning("No collection selected.")
    st.stop()

redis_namespace = selected_collection + "_semantic_search"
pattern = f"chat_history:{redis_namespace}:*"
keys = r.keys(pattern)

# Bulk read keys
items = []
for key in keys:
    ktype = r.type(key)
    parsed_value = None
    try:
        if ktype == "string":
            value = r.get(key)
            if value and (value.startswith("[") or value.startswith("{")):
                parsed_value = json.loads(value)
            else:
                parsed_value = value
        elif ktype == "list":
            list_items = r.lrange(key, 0, -1)
            parsed = []
            for item in list_items:
                try:
                    if item and (item.startswith("{") or item.startswith("[")):
                        parsed.append(json.loads(item))
                    else:
                        parsed.append(item)
                except Exception:
                    parsed.append(item)
            parsed_value = parsed
        else:
            parsed_value = f"⚠️ Unsupported type: {ktype}"
    except Exception as e:
        parsed_value = f"❌ Error reading: {e}"

    items.append({"key": key, "type": ktype, "value": parsed_value})

# Filtering
if search_text:
    needle = search_text.lower()
    filtered = []
    for it in items:
        val_text = ""
        try:
            if isinstance(it["value"], (dict, list)):
                val_text = json.dumps(it["value"])[:2000]
            elif it["value"] is not None:
                val_text = str(it["value"])[:2000]
        except Exception:
            val_text = str(it["value"])[:2000]
        if needle in it["key"].lower() or needle in val_text.lower():
            filtered.append(it)
    items = filtered

if type_filter != "All":
    items = [it for it in items if it["type"] == type_filter]

# Metrics
strings_count = sum(1 for it in items if it["type"] == "string")
lists_count = sum(1 for it in items if it["type"] == "list")

m1, m2, m3 = st.columns(3)
with m1:
    st.metric("Total Keys", f"{len(items)}")
with m2:
    st.metric("Strings", f"{strings_count}")
with m3:
    st.metric("Lists", f"{lists_count}")

st.markdown("---")

# Danger: Delete all in this collection
if delete_all:
    if keys:
        deleted = r.delete(*keys)
        st.success(f"✅ Deleted {deleted} keys for collection `{redis_namespace}`.")
        st.rerun()
    else:
        st.info("No keys to delete in this collection.")

# Pagination
total = len(items)
num_pages = max(1, (total + page_size - 1) // page_size)
page = st.number_input("Page", min_value=1, max_value=num_pages, value=1, step=1)
start = (page - 1) * page_size
end = start + page_size
page_items = items[start:end]

if not page_items:
    st.info("No keys match your filters.")
    st.stop()

# Render cards
for row in page_items:
    st.markdown("<div class='key-card'>", unsafe_allow_html=True)
    c1, c2 = st.columns([6, 1])
    with c1:
        st.markdown(f"**Key:** `{row['key']}`  <span class='badge'>{row['type']}</span>", unsafe_allow_html=True)
        # Value preview
        if isinstance(row["value"], (dict, list)):
            with st.expander("View JSON", expanded=False):
                st.json(row["value"])
        else:
            with st.expander("View Value", expanded=False):
                st.code(str(row["value"]))
    with c2:
        if st.button("❌ Delete", key=f"del_{row['key']}"):
            r.delete(row["key"])
            st.success("Deleted.")
            st.rerun()
    st.markdown("</div>", unsafe_allow_html=True)
