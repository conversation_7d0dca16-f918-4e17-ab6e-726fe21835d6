"""Document management page for the admin UI of the embedding service. """

from streamlit_app.utils.auth_utils import ensure_login

token, headers, roles = ensure_login()
import os
import time
import zipfile

import pandas as pd
import requests
import streamlit as st
from streamlit_app.constants import LOADER_BASE_URL
from streamlit_app.utils.api_utils import send_file_to_service, send_json_request
from streamlit_app.utils.db_utils import (
    DOCUMENT_STATUS_VALUES,
    get_all_collections,
    get_or_set_selected_collection,
    hash_func,
)

DEBUG_FILE_DIR = "/data/debug/"
PAGE_TITLE = "Document"
st.set_page_config(page_title=PAGE_TITLE, page_icon="📈")

st.markdown(f"# {PAGE_TITLE}")

SUPPORTED_FILE_EXTENSIONS = [
    "pdf",
    "docx",
    "md",
    "xlsx",
    "txt",
    "pptx",
    "zip",
    "jpg",
    "jpeg",
    "png",
    "json",
]

st.write("Send request to embedding service.")

if "existing_file_hashes" not in st.session_state:
    st.session_state.existing_file_hashes = []
if "existing_documents" not in st.session_state:
    st.session_state.existing_documents = []

os.makedirs(DEBUG_FILE_DIR, exist_ok=True)
success_list = []  # List to collect success info (filename -> document ID)
failed_list = []  # List to collect failed files (filename -> error)

st.title("Test Embedding Service")


def _refresh_documents(collection_id: str):
    try:
        with st.spinner("fetching existing documents.."):
            response = send_json_request(
                f"{LOADER_BASE_URL}/collection/{collection_id}/documents",
                method="get",
                headers=headers,
            )
        if isinstance(response, requests.Response):
            st.session_state.existing_documents = response.json().get("documents")
        else:
            st.error(response)
    except SyntaxError:
        st.error("Invalid JSON format. Please enter a valid JSON.")


selected_collection_id = get_or_set_selected_collection()

all_collections = get_all_collections()
collection_dict = {str(c["id"]): str(c["name"]) for c in all_collections}

if not selected_collection_id or selected_collection_id not in collection_dict:
    st.warning("Please create a collection first!")
else:
    selected_collection = next((c for c in all_collections if str(c["id"]) == selected_collection_id), None)
    if not selected_collection:
        st.error("Selected collection not found.")
        st.stop()

    assert selected_collection is not None

    collection_id = selected_collection["id"]
    existing_file_names = [str(doc["file_path"].split("/")[-1]) for doc in selected_collection.get("documents", [])]
    existing_file_hashes = [hash_func(f"{collection_id}{file_name}") for file_name in existing_file_names]
    st.session_state.existing_file_hashes = existing_file_hashes
    _refresh_documents(collection_id)

    st.sidebar.markdown("**custom config:**")
    st.sidebar.markdown(f":dash: **embedding_model:** {selected_collection['custom_config'].get('embedding_model', 'default')}")
    st.sidebar.markdown(f":dash: **llm_model:** {selected_collection['custom_config'].get('llm_model', 'default')}")
    st.sidebar.markdown(f":dash: **method:** {selected_collection['custom_config'].get('method', 'default')}")
    st.sidebar.markdown(f":speech_balloon: **system_prompt:** {selected_collection['custom_config'].get('system_prompt', 'default')}")

    # Let user choose processing method
    st.sidebar.markdown("### Select processing method:")
    method_selection = st.sidebar.radio(
        "Choose how to process the document:",
        ("Langchain", "Tesseract", "Langchain and Tesseract", "GPT Vision"),
        index=0,
    )

    # Set the default method if no selection is made
    use_langchain = method_selection == "Langchain"
    use_tesseract = method_selection == "Tesseract"
    use_langchain_and_tesseract = method_selection == "Langchain and Tesseract"
    use_gpt_vision = method_selection == "GPT Vision"

    if use_langchain:
        load_method = "use_langchain"
    elif use_tesseract:
        load_method = "use_tesseract"
    elif use_langchain_and_tesseract:
        load_method = "use_langchain_and_tesseract"
    elif use_gpt_vision:
        load_method = "use_gpt_vision"
    else:
        load_method = "use_langchain"

    uploaded_files = st.file_uploader("Upload a file.", type=SUPPORTED_FILE_EXTENSIONS, accept_multiple_files=True)
    if uploaded_files:
        if st.button("Send Request", key="button_single"):

            for uploaded_file in uploaded_files:
                if uploaded_file is not None:
                    file_extension = uploaded_file.name.split(".")[-1]
                    if file_extension == "zip":
                        with zipfile.ZipFile(uploaded_file, "r") as zip_ref:
                            extracted_files = zip_ref.namelist()

                            is_valid = True
                            for file_name in extracted_files:
                                _extension = file_name.split(".")[-1]
                                if _extension == "zip" or _extension not in SUPPORTED_FILE_EXTENSIONS:
                                    st.error(f"zip file contains an incompatible file: {file_name}")
                                    is_valid = False
                            if is_valid:
                                for file_name in extracted_files:
                                    extracted_file_content = zip_ref.read(file_name)
                                    save_path = os.path.join(DEBUG_FILE_DIR, file_name)
                                    with open(save_path, "wb") as file:
                                        file.write(extracted_file_content)

                                    try:
                                        with st.spinner("API call.."):
                                            with open(save_path, "rb") as f_in:
                                                _file = (
                                                    save_path,
                                                    f_in,
                                                    "application/octet-stream",
                                                )
                                                response = send_file_to_service(
                                                    f"{LOADER_BASE_URL}/collection/{collection_id}/document",
                                                    f_in,
                                                    headers=headers,
                                                    params={
                                                        "load_method": load_method,
                                                    },
                                                )
                                        if isinstance(response, requests.Response):
                                            json_response = response.json()
                                            document_id = json_response.get("document_id")
                                            success_list.append(f"{file_name} -> {document_id} -> Success")
                                        else:
                                            failed_list.append(f"{file_name} -> Failed (API Error)")
                                    except Exception as e:
                                        failed_list.append(f"{file_name} -> Failed (Error: {e})")
                    else:
                        file_content = uploaded_file.getvalue()
                        file_id = f"{collection_id}{uploaded_file.name}"
                        file_hash = hash_func(file_id)
                        if file_hash in st.session_state.existing_file_hashes:
                            st.error("This file has already been uploaded!")
                        else:
                            # save to local (for debugging purposes)
                            save_path = os.path.join(DEBUG_FILE_DIR, uploaded_file.name)
                            with open(save_path, "wb") as file:
                                file.write(file_content)

                            try:
                                with st.spinner("API call.."):
                                    response = send_file_to_service(
                                        f"{LOADER_BASE_URL}/collection/{collection_id}/document",
                                        uploaded_file,
                                        headers=headers,
                                        params={
                                            "load_method": load_method,
                                        },
                                    )
                                if isinstance(response, requests.Response):
                                    json_response = response.json()
                                    document_id = json_response.get("document_id")
                                    success_list.append(f"{uploaded_file.name} -> {document_id} -> Success")
                                else:
                                    failed_list.append(f"{uploaded_file.name} -> Failed (API Error)")
                            except Exception:
                                failed_list.append(f"{uploaded_file.name} -> Failed (API Error)")
            # After processing all files, display summary of successes and failures
            st.success("Processing Completed!")

            if success_list:
                st.write("### Successful Uploads:")
                for success in success_list:
                    st.write(success)

            if failed_list:
                st.write("### Failed Uploads:")
                for failure in failed_list:
                    st.write(failure)

    # get all documents
    if st.button("List existing documents", key="button_list"):
        _refresh_documents(collection_id)

    if st.session_state.existing_documents is not None and len(st.session_state.existing_documents) > 0:
        filtered_status = st.selectbox("filter_status", DOCUMENT_STATUS_VALUES)
        collection_name = selected_collection["name"]

        if filtered_status and filtered_status != "*":
            df_documents = pd.DataFrame(
                [
                    (
                        d["created_at"],
                        d["status"],
                        d["file_path"],
                        str(d["id"]),
                        collection_name,
                        d["document_metadata"],
                    )
                    for d in st.session_state.existing_documents
                    if d["status"] == filtered_status
                ],
                columns=[
                    "Created At",
                    "Status",
                    "File Path",
                    "ID",
                    "Collection Name",
                    "Metadata",
                ],
            )
        else:
            df_documents = pd.DataFrame(
                [
                    (
                        d["created_at"],
                        d["status"],
                        d["file_path"],
                        str(d["id"]),
                        collection_name,
                        d["document_metadata"],
                    )
                    for d in st.session_state.existing_documents
                ],
                columns=[
                    "Created At",
                    "Status",
                    "File Path",
                    "ID",
                    "Collection Name",
                    "Metadata",
                ],
            )

        st.dataframe(df_documents)

    else:
        st.text("No documents found!")

    # retry failed documents
    # retry failed documents with selected load method
    if st.session_state.existing_documents is not None:
        _failed_docs = [(str(d["id"]), str(d["file_path"])) for d in st.session_state.existing_documents if d["status"] == "failed"]
    else:
        _failed_docs = []

    if len(_failed_docs) > 0:
        st.warning(f"{len(_failed_docs)} failed documents found!")
        for _doc in _failed_docs:
            _id = _doc[0]
            _file_path = _doc[1]
            if st.button(f"🔄 Reload {_file_path}", key=f"button_reload_{_id}"):
                with st.spinner(f"Reloading document.. {_file_path}"):
                    try:
                        send_json_request(
                            f"{LOADER_BASE_URL}/collection/{collection_id}/document/{_id}/reload",
                            headers=headers,
                            params={
                                "load_method": load_method,
                            },
                        )
                    except Exception as e:
                        st.error(f"Unable to reload {e}.")
                    time.sleep(5)
                with st.spinner("Refreshing document list.."):
                    _refresh_documents(collection_id)
                st.rerun()


if not failed_list and os.path.exists(DEBUG_FILE_DIR):
    leftover_files = os.listdir(DEBUG_FILE_DIR)
    if leftover_files:
        st.warning(f"Cleaning up {len(leftover_files)} leftover debug files..")
        for f in leftover_files:
            f_path = os.path.join(DEBUG_FILE_DIR, f)
            try:
                os.remove(f_path)
            except Exception as e:
                st.warning(f"Couldn't delete leftover file {f}: {e}")
