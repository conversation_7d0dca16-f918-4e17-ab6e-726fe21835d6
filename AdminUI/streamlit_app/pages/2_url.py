from streamlit_app.utils.auth_utils import ensure_login

token, headers, roles = ensure_login()
import pandas as pd
import requests
import streamlit as st
from streamlit_app.constants import LOADER_BASE_URL
from streamlit_app.utils.api_utils import send_json_request
from streamlit_app.utils.db_utils import (
    get_all_collections,
    get_or_set_selected_collection,
)

PAGE_TITLE = "URL Management"
st.set_page_config(page_title=PAGE_TITLE, page_icon="🔗")

st.markdown(f"# {PAGE_TITLE}")
st.write("Manage URLs associated with collections.")

# Collection selection (via sidebar)
selected_collection_id = get_or_set_selected_collection()
all_collections = get_all_collections()
collection_dict = {str(c["id"]): str(c["name"]) for c in all_collections}

if not selected_collection_id or selected_collection_id not in collection_dict:
    st.warning("Please select a collection from the sidebar.")
else:
    collection_id = selected_collection_id
    collection_name = collection_dict[collection_id]
    st.info(f"Selected collection: **{collection_name}**")

    tab_list, tab_add, tab_add_batch = st.tabs(["List URLs", "Add URL", "Add Multiple URLs"])

    # ─────────────────────────────────────────────────────────────────────
    with tab_list:
        if st.button("List URLs", key="button_list"):
            try:
                with st.spinner("Fetching URLs..."):
                    response = send_json_request(
                        f"{LOADER_BASE_URL}/collection/{collection_id}/urls",
                        method="get",
                        headers=headers,
                    )
                if isinstance(response, requests.Response) and response.status_code == 200:
                    st.success("Request Sent Successfully!")
                    data = response.json().get("urls", [])
                    df = pd.DataFrame(data)
                    if not df.empty:
                        st.dataframe(df)
                    else:
                        st.info("No URLs found for the selected collection.")
                else:
                    st.error("Error fetching data.")
            except Exception as e:
                st.error(f"An error occurred: {e}")

    # ─────────────────────────────────────────────────────────────────────
    with tab_add:
        url = st.text_input("URL")

        if st.button("Add URL", key="button_add"):
            if not url.strip():
                st.warning("URL cannot be empty.")
            else:
                try:
                    json_data = {"collection_id": collection_id, "url": url}
                    with st.spinner("Sending request..."):
                        response = send_json_request(
                            f"{LOADER_BASE_URL}/collection/{collection_id}/url",
                            json_data,
                            headers=headers,
                        )
                    if isinstance(response, requests.Response) and response.status_code == 200:
                        st.success("URL added successfully!")
                        st.json(response.json())
                    else:
                        st.error(response.text)
                except Exception as e:
                    st.error(f"An error occurred: {e}")

    # ─────────────────────────────────────────────────────────────────────
    with tab_add_batch:
        urls = st.text_area("URLs (one per line)")

        if st.button("Add Multiple URLs", key="button_add_batch"):
            if not urls.strip():
                st.warning("URLs cannot be empty.")
            else:
                try:
                    url_list = [url.strip() for url in urls.split("\n") if url.strip()]
                    json_data = {"urls": url_list}
                    with st.spinner("Sending request..."):
                        response = send_json_request(
                            f"{LOADER_BASE_URL}/collection/{collection_id}/url/batch",
                            json_data,
                            headers=headers,
                        )
                    if isinstance(response, requests.Response) and response.status_code == 200:
                        st.success("Batch URL request sent successfully!")
                        st.json(response.json())
                    else:
                        st.error(response.text)
                except Exception as e:
                    st.error(f"An error occurred: {e}")
