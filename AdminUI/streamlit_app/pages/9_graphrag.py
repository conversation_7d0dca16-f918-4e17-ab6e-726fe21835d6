from streamlit_app.constants import GRAPHRAG_BASE_URL
from streamlit_app.utils.auth_utils import ensure_login

token, headers, roles = ensure_login()

import os
import tempfile

import networkx as nx
import pandas as pd
import requests
import streamlit as st
import streamlit.components.v1 as components
from pyvis.network import Network

PAGE_TITLE = "GraphRAG Service"
st.set_page_config(page_title=PAGE_TITLE, layout="wide")

st.markdown(f"# {PAGE_TITLE}")
st.write("Knowledge Graph-based Retrieval Augmented Generation service for document analysis and querying.")


# Helper function to get collections
def get_collections():
    """Get collections <UNK> /collections (for query page)"""
    try:
        res = requests.get(f"{GRAPHRAG_BASE_URL}/collections", headers=headers)
        if res.status_code == 200:
            collections_data = res.json().get("collections", [])
            # Remove "collection_" prefix from collection names
            return [c.replace("collection_", "") if c.startswith("collection_") else c for c in collections_data]
        else:
            st.error(f"Failed to fetch collections: {res.status_code} - {res.text}")
    except Exception as e:
        st.error(f"Failed to fetch collections: {e}")
    return []


# Tabs
upload_tab, query_tab, delete_tab, graph_tab, embeddings_tab = st.tabs(["📤 Upload", "🔍 Query", "🗑️ Delete", "🕸️ Knowledge Graph", "📊 Embeddings"])

# --------------------------------------------------------------------------------
# UPLOAD TAB - Multiple files upload
# --------------------------------------------------------------------------------
with upload_tab:
    st.header("📤 Upload Files to GraphRAG")
    collections = get_collections()

    with st.form("upload_form"):
        root_name = st.text_input("Collection Name", help="Enter a unique name for your collection")
        uploaded_files = st.file_uploader(
            "Select files",
            type=["pdf", "docx", "md", "xlsx", "txt", "pptx", "jpg", "jpeg", "png"],
            accept_multiple_files=True,
            help="Upload documents to be processed and indexed by GraphRAG",
        )
        submitted = st.form_submit_button("Upload Files")

    if submitted:
        if uploaded_files and root_name:
            if root_name in collections:
                st.warning("⚠️ Collection already exists. Adding new files to the existing collection.")

            # Build a list of (key, (filename, file_content)) to send multiple files
            multiple_files_payload = []
            for f in uploaded_files:
                multiple_files_payload.append(("files", (f.name, f.read())))

            data = {"root_name": root_name}

            try:
                with st.spinner("Uploading and processing files..."):
                    # POST the data to "/upload" endpoint
                    res = requests.post(f"{GRAPHRAG_BASE_URL}/upload", data=data, files=multiple_files_payload, headers=headers, timeout=120)
                if res.status_code == 200:
                    st.success("✅ " + res.json().get("message", "Files uploaded successfully"))
                    st.info("📝 Files are being processed in the background. Indexing will be triggered automatically after conversion.")
                else:
                    st.error(f"❌ Upload failed: {res.status_code} - {res.text}")
            except Exception as e:
                st.error(f"❌ Upload failed: {e}")
        else:
            if not root_name:
                st.warning("⚠️ Please enter a collection name.")
            if not uploaded_files:
                st.warning("⚠️ Please select at least one file.")

# --------------------------------------------------------------------------------
# QUERY TAB
# --------------------------------------------------------------------------------
with query_tab:
    st.header("🔍 Query GraphRAG Collection")
    collections = get_collections()

    if not collections:
        st.info("📝 No collections available. Please upload some documents first.")
    else:
        with st.form("query_form"):
            q_root_name = st.selectbox("Select Collection", collections)
            q_method = st.selectbox(
                "Query Method",
                ["global", "local", "drift"],
                help="Global: Comprehensive analysis across all documents\nLocal: Focused search on specific entities\nDrift: Temporal analysis",
            )
            q_text = st.text_area("Your Question", value="What is this about?", help="Ask questions about the content in your collection")
            q_submit = st.form_submit_button("🔍 Submit Query")

        if q_submit:
            payload = {"root_name": q_root_name, "method": q_method, "query": q_text}
            try:
                with st.spinner("Processing your query..."):
                    res = requests.post(f"{GRAPHRAG_BASE_URL}/query", json=payload, headers=headers, timeout=300)
                if res.status_code == 200:
                    result = res.json()
                    if "answer" in result and result["answer"]:
                        st.success("✅ Query completed successfully!")
                        st.markdown("### 📋 Answer:")
                        st.markdown(result["answer"])
                    else:
                        st.warning("⚠️ " + result.get("error", "No answer found in the response."))
                else:
                    st.error(f"❌ Query failed: {res.status_code} - {res.text}")
            except Exception as e:
                st.error(f"❌ Query failed: {e}")

# --------------------------------------------------------------------------------
# DELETE TAB
# --------------------------------------------------------------------------------
with delete_tab:
    st.header("🗑️ Delete Collection")
    collections = get_collections()

    if not collections:
        st.info("📝 No collections available to delete.")
    else:
        with st.form("delete_form"):
            to_delete = st.selectbox("Choose collection to delete", collections)
            st.warning("⚠️ This action cannot be undone!")
            del_submit = st.form_submit_button("🗑️ Delete Collection", type="secondary")

        if del_submit:
            try:
                with st.spinner(f"Deleting collection '{to_delete}'..."):
                    res = requests.delete(f"{GRAPHRAG_BASE_URL}/collections/{to_delete}", headers=headers)
                if res.status_code == 200:
                    st.success(f"✅ Collection '{to_delete}' deleted successfully.")
                    st.rerun()  # Refresh the page to update the collections list
                else:
                    st.error(f"❌ Delete failed: {res.status_code} - {res.text}")
            except Exception as e:
                st.error(f"❌ Delete failed: {e}")

# --------------------------------------------------------------------------------
# GRAPH TAB
# --------------------------------------------------------------------------------
with graph_tab:
    st.header("🕸️ View Knowledge Graph")
    collections = get_collections()

    if not collections:
        st.info("📝 No collections available. Please upload and process some documents first.")
    else:
        selected = st.selectbox("Select Collection", collections, key="graph")

        col1, col2 = st.columns([1, 4])
        with col1:
            show_graph = st.button("🕸️ Generate Graph", use_container_width=True)

        if show_graph:
            try:
                with st.spinner("Loading knowledge graph..."):
                    res = requests.get(f"{GRAPHRAG_BASE_URL}/graph/{selected}", headers=headers)
                if res.status_code == 200:
                    try:
                        # Use the same approach as your working GraphRAG app
                        with tempfile.NamedTemporaryFile(suffix=".graphml", delete=False) as tmp:
                            tmp.write(res.content)
                            tmp_path = tmp.name

                        G = nx.read_graphml(tmp_path)
                        st.success(f"✅ Graph loaded successfully! Nodes: {G.number_of_nodes()}, Edges: {G.number_of_edges()}")

                        net = Network(height="600px", width="100%", notebook=False, directed=False)
                        net.from_nx(G)
                        net.set_options(
                            """
                        {
                          "physics": {
                            "enabled": true,
                            "stabilization": {"iterations": 100}
                          }
                        }
                        """
                        )

                        html_path = tempfile.NamedTemporaryFile(suffix=".html", delete=False)
                        net.save_graph(html_path.name)

                        with open(html_path.name, "r") as f:
                            html_content = f.read()
                        components.html(html_content, height=600, scrolling=True)

                        # Clean up temp files
                        try:
                            os.unlink(tmp_path)
                            os.unlink(html_path.name)
                        except OSError:
                            pass  # Ignore cleanup errors

                    except Exception as graph_error:
                        st.error(f"❌ Error rendering graph: {graph_error}")
                        st.info("💡 Tip: If you continue getting permission errors, try restarting the AdminUI container")
                else:
                    st.warning("⚠️ Graph not found. Make sure the collection has been processed and indexed.")
            except Exception as e:
                st.error(f"❌ Graph loading error: {e}")

# --------------------------------------------------------------------------------
# EMBEDDINGS TAB
# --------------------------------------------------------------------------------
with embeddings_tab:
    st.header("📊 View Embeddings")
    collections = get_collections()

    if not collections:
        st.info("📝 No collections available. Please upload and process some documents first.")
    else:
        selected = st.selectbox("Select Collection", collections, key="embed")
        embed_files = ["embeddings.community.full_content.parquet", "embeddings.entity.description.parquet", "embeddings.text_unit.text.parquet"]
        file_to_view = st.selectbox("Select embedding file", embed_files)

        col1, col2 = st.columns([1, 4])
        with col1:
            load_embeddings = st.button("📊 Load Embeddings", use_container_width=True)

        if load_embeddings:
            try:
                with st.spinner("Loading embeddings data..."):
                    url = f"{GRAPHRAG_BASE_URL}/collections/{selected}/output/{file_to_view}"
                    res = requests.get(url, headers=headers)
                if res.status_code == 200:
                    # Create a temporary directory in /tmp which should be writable
                    temp_dir = "/tmp/graphrag_viz"
                    os.makedirs(temp_dir, exist_ok=True)

                    import uuid

                    unique_id = str(uuid.uuid4())[:8]
                    tmp_path = os.path.join(temp_dir, f"embeddings_{unique_id}.parquet")

                    try:
                        # Write parquet data to file
                        with open(tmp_path, "wb") as f:
                            f.write(res.content)

                        df = pd.read_parquet(tmp_path)
                        st.success(f"✅ Loaded {len(df)} rows from {file_to_view}")

                        # Show some basic info about the data
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("Rows", len(df))
                        with col2:
                            st.metric("Columns", len(df.columns))
                        with col3:
                            st.metric("Memory Usage", f"{df.memory_usage(deep=True).sum() / 1024 / 1024:.1f} MB")

                        # Display the dataframe
                        st.dataframe(df, use_container_width=True, height=400)

                        # Clean up temp file
                        try:
                            os.unlink(tmp_path)
                        except OSError:
                            pass  # Ignore cleanup errors
                    except Exception as df_error:
                        st.error(f"❌ Error reading parquet file: {df_error}")
                        # Try to clean up file even if there was an error
                        try:
                            if os.path.exists(tmp_path):
                                os.unlink(tmp_path)
                        except OSError:
                            pass
                else:
                    st.warning("⚠️ Embeddings file not found. Make sure the collection has been processed and indexed.")
            except Exception as e:
                st.error(f"❌ Embedding load error: {e}")

# Add some footer information
st.markdown("---")
st.markdown(
    """
    <div style='text-align: center; color: #666; margin-top: 2rem;'>
        <small>GraphRAG Service - Knowledge Graph based RAG for enhanced document understanding</small>
    </div>
    """,
    unsafe_allow_html=True,
)
