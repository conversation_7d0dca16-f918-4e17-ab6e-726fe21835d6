import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st
from streamlit_app.constants import LOADER_BASE_URL
from streamlit_app.utils.api_utils import send_json_request
from streamlit_app.utils.auth_utils import ensure_login
from streamlit_app.utils.db_utils import get_all_collections

token, headers, roles = ensure_login()

# API endpoint
LOADER_URL = f"{LOADER_BASE_URL}/collection"
ADMIN_API_URL = f"{LOADER_BASE_URL}/admin"

PAGE_TITLE = "Context Analysis Per Collection"
st.set_page_config(page_title=PAGE_TITLE, page_icon="🔍", layout="wide")
st.markdown(f"# {PAGE_TITLE}")
st.write("View context window analysis, system prompt tokens, and available space for each collection")


def get_collection_token_usage(collection_id: str) -> dict:
    """Call the token usage API endpoint for a specific collection."""
    try:
        response = send_json_request(f"{LOADER_URL}/{collection_id}/token-usage", method="get", headers=headers)
        if response.status_code == 200:
            return response.json()
        else:
            st.warning(f"Token usage fetch failed for {collection_id}: {response.status_code}")
    except Exception as e:
        st.warning(f"Token usage error for {collection_id}: {e}")
    return {}


def create_token_usage_chart(token_info_rows, chart_type="bar"):
    """Create a chart based on token usage data."""
    if not token_info_rows:
        st.info("No token usage data available.")
        return

    df = pd.DataFrame(token_info_rows)

    if chart_type == "bar":
        # Create bar chart for token usage comparison
        fig = px.bar(
            df,
            x="Name",
            y=["System Prompt Tokens", "Chunk Tokens Total", "Available for Input"],
            title="Token Usage Distribution by Collection",
            labels={"value": "Tokens", "variable": "Token Type"},
            color_discrete_map={
                "System Prompt Tokens": "#1f77b4",  # Blue
                "Chunk Tokens Total": "#ff7f0e",  # Orange
                "Available for Input": "#2ca02c",  # Green
            },
            barmode="stack",
        )
        fig.update_layout(
            legend={"orientation": "h", "yanchor": "bottom", "y": 1.02, "xanchor": "right", "x": 1},
            height=500,
        )
        st.plotly_chart(fig, use_container_width=True)

    elif chart_type == "pie":
        # Create pie charts for each collection
        for _, row in df.iterrows():
            col1, col2 = st.columns(2)

            # Token distribution
            with col1:
                token_data = {
                    "Categories": ["System Prompt", "Chunks", "Available"],
                    "Values": [row["System Prompt Tokens"], row["Chunk Tokens Total"], row["Available for Input"]],
                }
                token_df = pd.DataFrame(token_data)
                fig = px.pie(
                    token_df,
                    values="Values",
                    names="Categories",
                    title=f"Token Distribution: {row['Name']}",
                    color="Categories",
                    color_discrete_map={"System Prompt": "#1f77b4", "Chunks": "#ff7f0e", "Available": "#2ca02c"},
                )
                st.plotly_chart(fig, use_container_width=True)

            # Context usage gauge
            with col2:
                context_used_pct = (row["Total Used"] / row["Context Limit"]) * 100
                fig = go.Figure(
                    go.Indicator(
                        mode="gauge+number",
                        value=context_used_pct,
                        title={"text": f"Context Usage: {row['Name']}"},
                        gauge={
                            "axis": {"range": [0, 100]},
                            "bar": {"color": "darkblue"},
                            "steps": [
                                {"range": [0, 30], "color": "green"},
                                {"range": [30, 70], "color": "yellow"},
                                {"range": [70, 100], "color": "red"},
                            ],
                            "threshold": {"line": {"color": "red", "width": 4}, "thickness": 0.75, "value": 90},
                        },
                    )
                )
                fig.update_layout(height=300)
                st.plotly_chart(fig, use_container_width=True)


def display_token_usage_table(token_info_rows):
    """Display token usage data in a table format."""
    if not token_info_rows:
        st.info("No token usage data available.")
        return

    df = pd.DataFrame(token_info_rows)

    # Add percentage columns
    df["System Prompt %"] = (df["System Prompt Tokens"] / df["Context Limit"] * 100).round(2)
    df["Chunks %"] = (df["Chunk Tokens Total"] / df["Context Limit"] * 100).round(2)
    df["Available %"] = (df["Available for Input"] / df["Context Limit"] * 100).round(2)
    df["Total Used %"] = (df["Total Used"] / df["Context Limit"] * 100).round(2)

    # Reorder columns for better readability
    columns = [
        "Name",
        "Model",
        "Context Limit",
        "System Prompt Tokens",
        "System Prompt %",
        "Chunk Size",
        "Top-K",
        "Chunk Tokens Total",
        "Chunks %",
        "Total Used",
        "Total Used %",
        "Available for Input",
        "Available %",
    ]

    # Filter columns that exist in the dataframe
    display_columns = [col for col in columns if col in df.columns]

    # Display the table with formatting
    st.dataframe(
        df[display_columns].style.format(
            {
                "System Prompt %": "{:.1f}%",
                "Chunks %": "{:.1f}%",
                "Available %": "{:.1f}%",
                "Total Used %": "{:.1f}%",
            }
        ),
        use_container_width=True,
    )


def create_model_comparison_chart(token_info_rows):
    """Create a chart comparing token usage across different models."""
    if not token_info_rows:
        return

    df = pd.DataFrame(token_info_rows)

    # Group by model and calculate average usage
    model_stats = df.groupby("Model").agg({"System Prompt Tokens": "mean", "Chunk Tokens Total": "mean", "Available for Input": "mean", "Context Limit": "first"}).reset_index()

    # Calculate percentage of context limit used
    model_stats["Usage %"] = ((model_stats["System Prompt Tokens"] + model_stats["Chunk Tokens Total"]) / model_stats["Context Limit"] * 100).round(1)

    # Create horizontal bar chart
    fig = px.bar(
        model_stats,
        y="Model",
        x="Usage %",
        title="Average Token Usage by Model (% of Context Limit)",
        labels={"Usage %": "% of Context Limit Used", "Model": "LLM Model"},
        color="Usage %",
        color_continuous_scale=["green", "yellow", "red"],
        orientation="h",
        text="Usage %",
    )

    fig.update_traces(texttemplate="%{text:.1f}%", textposition="outside")
    fig.update_layout(height=400)

    st.plotly_chart(fig, use_container_width=True)

    # Create detailed model comparison table
    st.subheader("Model Comparison Details")
    model_stats["Available %"] = (model_stats["Available for Input"] / model_stats["Context Limit"] * 100).round(1)

    formatted_model_stats = model_stats.style.format(
        {"System Prompt Tokens": "{:.0f}", "Chunk Tokens Total": "{:.0f}", "Available for Input": "{:.0f}", "Usage %": "{:.1f}%", "Available %": "{:.1f}%"}
    )

    st.dataframe(formatted_model_stats, use_container_width=True)


# Main content
with st.spinner("Loading collections..."):
    collections = get_all_collections()

if not collections:
    st.warning("No collections found. Please create a collection first.")
else:
    # Filter out GraphRAG collections
    regular_collections = [c for c in collections if c.get("type") != "GraphRAG"]

    if not regular_collections:
        st.warning("No regular collections found. GraphRAG collections don't support token usage analysis.")
    else:
        # Fetch token usage for each collection
        with st.spinner("Fetching token usage data..."):
            token_info_rows = []
            for collection in regular_collections:
                token_usage = get_collection_token_usage(collection["id"])
                if token_usage:
                    token_info_rows.append(
                        {
                            "Name": collection["name"],
                            "Model": token_usage["llm_model"],
                            "Context Limit": token_usage["context_limit"],
                            "System Prompt Tokens": token_usage["system_prompt_tokens"],
                            "Top-K": token_usage["top_k"],
                            "Chunk Size": token_usage["chunk_size"],
                            "Chunk Tokens Total": token_usage["chunk_tokens_total"],
                            "Total Used": token_usage["total_used"],
                            "Available for Input": token_usage["available_for_input"],
                        }
                    )

        # Display visualizations and table
        if token_info_rows:
            # Tabs for different views
            tab1, tab2, tab3, tab4 = st.tabs(["Table View", "Bar Chart", "Detailed View", "Model Comparison"])

            with tab1:
                st.subheader("Token Usage Table")
                display_token_usage_table(token_info_rows)

            with tab2:
                st.subheader("Token Usage Comparison")
                create_token_usage_chart(token_info_rows, chart_type="bar")

            with tab3:
                st.subheader("Detailed Token Usage by Collection")
                create_token_usage_chart(token_info_rows, chart_type="pie")

            with tab4:
                st.subheader("Model Performance Comparison")
                create_model_comparison_chart(token_info_rows)

            # Add download button for the data
            st.markdown("---")
            st.subheader("Download Token Usage Data")

            # Convert data to CSV for download
            df = pd.DataFrame(token_info_rows)
            csv = df.to_csv(index=False)
            st.download_button(
                label="Download as CSV",
                data=csv,
                file_name="token_usage_data.csv",
                mime="text/csv",
            )

        else:
            st.warning("Failed to fetch token usage data for any collection.")
