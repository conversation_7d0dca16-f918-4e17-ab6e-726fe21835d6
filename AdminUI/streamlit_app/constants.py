import os

LOADER_BASE_URL = os.environ.get("LOADER_BASE_URL", "http://loader:8002")
INDEXER_BASE_URL = os.environ.get("INDEXER_BASE_URL", "http://indexer:8001")
QUERY_BASE_URL = os.environ.get("QUERY_BASE_URL", "http://query:8003")
PROMPT_BASE_URL = os.environ.get("PROMPT_BASE_URL", "http://localhost:8006/api")
GRAPHRAG_BASE_URL = os.environ.get("GRAPHRAG_BASE_URL", "https://graphrag.dev.fbeta.tech/api")
