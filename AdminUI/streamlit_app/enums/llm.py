from enum import Enum


class OPENAI_EMBEDDING_MODELS(str, Enum):
    """Enum for different embedding models used in the application."""

    ADA002 = "text-embedding-ada-002"


class OPENAI_MODELS(str, Enum):
    """Enum for different OpenAI models used in the application."""

    GPT4 = "gpt-4"
    GPT4O2 = "gpt-4o-2"
    GPT4O = "gpt-4o"
    GPT4OMINI = "gpt-4o-mini"


class OS_MODELS(str, Enum):
    """Enum for Open Source models."""

    LLAMA3170BINSTRUCT = "Meta-Llama-3.1-70B-Instruct"
    MIXTRAL8B = "Mistral-8B"
    PHI4 = "Phi-4"


class OS_EMBEDDING_MODELS(str, Enum):
    """Enum for Open-source embedding-only models."""

    BGEM3 = "bge-m3"


class ReRankWebLLMModels(str, Enum):
    """Enum for different re-ranking web LLM models used in the application."""

    GPT4OMINI = "gpt-4o-mini"
    LLAMA3170BINSTRUCT = "Meta-Llama-3.1-70B-Instruct"


MIXTRAL8B = "Mistral-8B"
