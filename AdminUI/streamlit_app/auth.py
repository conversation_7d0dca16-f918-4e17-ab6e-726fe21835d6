import os

from msal import PublicClientApplication


def get_token():
    """
    Acquire an access token using Microsoft Azure AD device code flow.

    This function authenticates a user against Azure Active Directory using the Microsoft Authentication Library (MSAL)
    and the device code flow, which is suitable for command-line applications or environments without a web browser.

    The function attempts the following steps:
    1. Retrieves configuration values from environment variables:
       - APP_CLIENT_ID: The client (application) ID registered in Azure AD.
       - TENANT_ID: The Azure AD tenant ID.
    2. Constructs the authority URL using the provided tenant ID.
    3. Initializes a PublicClientApplication with the given client ID and authority.
    4. Checks if there is a cached account and tries to acquire a token silently.
    5. If silent acquisition fails, it initiates a device flow for user authentication.

    Returns:
        tuple:
            - If silent authentication is successful:
                (token_result: dict, error: None, client_id: str, authority: str)
            - If device code flow is initiated:
                (device_flow_info: dict, error: None, client_id: str, authority: str)
            - If device flow initiation fails:
                (None, error_message: str, None, None)

    Notes:
        - The device flow information contains a `user_code` and a `verification_uri`.
          These should be displayed to the user so they can complete the authentication process.
        - The function does not poll for the final access token. After calling this function, you must
          complete the device flow by using `acquire_token_by_device_flow(flow)` on the `PublicClientApplication` instance.

    Example usage:
        flow, error, client_id, authority = get_token()
        if error:
            print("Error:", error)
        elif "user_code" in flow:
            print("Go to", flow["verification_uri"], "and enter code:", flow["user_code"])
    """

    client_id = os.getenv("APP_CLIENT_ID", "")
    tenant_id = os.getenv("TENANT_ID", "")
    authority = f"https://login.microsoftonline.com/{tenant_id}"
    scopes = [f"api://{client_id}/user_impersonation"]

    app = PublicClientApplication(client_id, authority=authority)

    accounts = app.get_accounts()
    result = app.acquire_token_silent(scopes, account=accounts[0]) if accounts else None
    if result:
        return result, None, client_id, authority

    flow = app.initiate_device_flow(scopes=scopes)
    if "user_code" not in flow:
        return None, "Device flow initiation failed.", None, None

    return flow, None, client_id, authority
