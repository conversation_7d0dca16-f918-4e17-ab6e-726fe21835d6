import hashlib
import uuid
from uuid import UUID

import requests
import streamlit as st
from streamlit_app.constants import GRAPHRAG_BASE_URL, LOADER_BASE_URL, QUERY_BASE_URL
from streamlit_app.utils.auth_utils import (
    ensure_login,  # we will call inside functions for fresh tokens
)
from streamlit_app.utils.http_utils import safe_request

DOCUMENT_STATUS_VALUES = [
    "*",
    "added",
    "loading",
    "ready_to_be_indexed",
    "extracting_embeddings",
    "indexed",
    "failed",
    "deleted",
]


def generate_uuid() -> str:
    """Generate a random UUID."""
    return str(uuid.uuid4())


def hash_func(file_id: str) -> str:
    """Generate a SHA-256 hash for the given file ID."""
    return hashlib.sha256(file_id.encode()).hexdigest()


def is_valid_uuid(uuid_str: str) -> bool:
    """Check if a string is a valid UUID4."""
    try:
        _uuid = uuid.UUID(uuid_str, version=4)
        return str(_uuid) == uuid_str
    except Exception:
        return False


# ─────────────────────────────────────────────────────────────────────────────
# Collections / Documents / … helpers (all use safe_request)
# ─────────────────────────────────────────────────────────────────────────────


def get_graphrag_collections_for_query():
    """Graphrag Service – /collections (for query page)"""
    _token, headers, _roles = ensure_login()
    try:
        response = requests.get(f"{GRAPHRAG_BASE_URL}/collections", headers=headers)
        if response.status_code == 200:
            collections_data = response.json().get("collections", [])
            # Convert GraphRAG collections to the same format as regular collections
            graphrag_collections = []
            for collection_name in collections_data:
                # Remove "collection_" prefix if present for display and API calls
                display_name = collection_name.replace("collection_", "") if collection_name.startswith("collection_") else collection_name
                graphrag_collections.append(
                    {
                        "id": f"graphrag_{collection_name}",  # Add prefix to avoid ID conflicts
                        "name": f"{display_name} (GraphRAG)",
                        "description": "GraphRAG Knowledge Graph Collection",
                        "type": "GraphRAG",
                        "original_name": collection_name,  # Keep original name from API
                        "query_name": display_name,  # Use this for queries (without collection_ prefix)
                        "custom_config": {"type": "GraphRAG", "original_name": collection_name, "query_name": display_name},
                    }
                )
            return graphrag_collections
    except Exception as e:
        st.warning(f"Failed to fetch GraphRAG collections: {e}")
    return []


def get_all_collections():
    """Loader Service – /collections + GraphRAG collections"""
    _token, headers, _roles = ensure_login()
    url = f"{LOADER_BASE_URL}/collections"
    resp = safe_request("get", url, headers=headers, timeout=30)
    resp.raise_for_status()
    regular_collections = resp.json()["collections"]

    # Add type to regular collections
    for col in regular_collections:
        col["type"] = "Regular"

    # Get GraphRAG collections
    graphrag_collections = get_graphrag_collections_for_query()

    # Combine both types
    all_collections = regular_collections + graphrag_collections
    return all_collections


def get_all_documents(collection_id: UUID):
    """Loader Service – /documents?collection_id=… ."""
    _token, headers, _roles = ensure_login()
    url = f"{LOADER_BASE_URL}/collection/{collection_id}/documents"
    resp = safe_request("get", url, headers=headers, timeout=30)
    resp.raise_for_status()
    return resp.json()["documents"]


def get_all_urls(collection_id: UUID):
    """Loader Service – /urls?collection_id=… ."""
    _token, headers, _roles = ensure_login()
    url = f"{LOADER_BASE_URL}/collection/{collection_id}/urls"
    resp = safe_request("get", url, headers=headers, timeout=30)
    resp.raise_for_status()
    return resp.json()["urls"]


def get_all_queries(collection_id: UUID):
    """Query Service – /queries?collection_id=…"""
    _token, headers, _roles = ensure_login()
    url = f"{QUERY_BASE_URL}/collection/{collection_id}/queries"
    resp = safe_request("get", url, headers=headers, timeout=30)
    resp.raise_for_status()
    return resp.json()["queries"]


def get_all_feedbacks(query_id: str):
    """Query Service – /feedbacks?query_id=…"""
    _token, headers, _roles = ensure_login()
    url = f"{QUERY_BASE_URL}/feedbacks"
    resp = safe_request("get", url, params={"query_id": query_id}, headers=headers, timeout=30)
    resp.raise_for_status()
    return resp.json()["feedbacks"]


def get_all_sessions(collection_id: UUID):
    """Query Service – /get_sessions_by_collection_id/{collection_id}"""
    _token, headers, _roles = ensure_login()
    url = f"{QUERY_BASE_URL}/get_sessions_by_collection_id/{collection_id}"
    resp = safe_request("get", url, headers=headers, timeout=30)
    resp.raise_for_status()
    return resp.json().get("folders", {})


def delete_query(query_id: str):
    """Query Service – DELETE /queries/{query_id}"""
    _token, headers, _roles = ensure_login()
    url = f"{QUERY_BASE_URL}/query/{query_id}"
    resp = safe_request("delete", url, headers=headers, timeout=10)
    resp.raise_for_status()
    return resp.status_code == 204


def word_wrap(text: str, n_chars: int = 72):
    """Wrap text at the next space after n_chars characters."""
    # Wrap a string at the next space after n_chars
    if len(text) < n_chars:
        return text
    else:
        return text[:n_chars].rsplit(" ", 1)[0] + "\n" + word_wrap(text[len(text[:n_chars].rsplit(" ", 1)[0]) + 1 :], n_chars)


def get_or_set_selected_collection():
    """Get or set the selected collection ID in the session state."""
    all_collections = get_all_collections()

    if not all_collections:
        st.warning("No collections available. Please create a collection.")
        return None

    collection_dict = {str(c["name"]): str(c["id"]) for c in all_collections}

    if "selected_collection_id" not in st.session_state:
        st.session_state["selected_collection_id"] = None

    if st.session_state["selected_collection_id"]:
        selected_name = next(
            (name for name, id_ in collection_dict.items() if id_ == st.session_state["selected_collection_id"]),
            None,
        )
    else:
        selected_name = None

    collection_selection = st.sidebar.selectbox(
        "Select Collection",
        list(collection_dict.keys()),
        index=list(collection_dict.keys()).index(selected_name) if selected_name else 0,
    )
    st.session_state["selected_collection_id"] = collection_dict.get(collection_selection)
    return st.session_state["selected_collection_id"]
