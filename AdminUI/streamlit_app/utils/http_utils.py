from __future__ import annotations

"""HTTP helpers that integrate automatic token refresh for Admin UI."""

from typing import Any

import requests
import streamlit as st
from streamlit_app.utils.auth_utils import ensure_login


def safe_request(method: str, url: str, *, headers: dict | None = None, **kwargs: Any):
    """Send HTTP request and, on 401, trigger re-authentication once.

    Parameters
    ----------
    method:
        HTTP method name (``"get"``, ``"post"``, …).
    url:
        Full request URL.
    headers:
        Optional request headers. If *None*, a fresh ``Authorization`` header
        is injected automatically.
    **kwargs:
        Passed through to :pyfunc:`requests.request`.

    Returns
    -------
    requests.Response
    """

    if headers is None:
        _token, headers, _ = ensure_login()
    kwargs_headers = headers or {}

    resp = requests.request(method, url, headers=kwargs_headers, **kwargs)
    if resp.status_code != 401:
        return resp

    # ---- 401: token expired or invalid -------------------------------------
    st.session_state.pop("auth_token", None)
    st.session_state.pop("roles", None)

    _token, new_headers, _ = ensure_login()
    merged_headers = kwargs_headers.copy()
    merged_headers.update(new_headers)

    return requests.request(method, url, headers=merged_headers, **kwargs)
