"""design_utils.py"""

import streamlit as st


def apply_custom_styles():
    """
    Apply custom CSS styles to the Streamlit app for a consistent design.
    This function modifies the appearance of various components such as headers,
    buttons, alerts, and data frames to create a cohesive look and feel.
    """
    # Custom CSS styles for the Streamlit app
    # The styles are applied using the `st.markdown` function with `unsafe_allow_html=True`
    # to allow HTML and CSS rendering in the Streamlit app.
    st.markdown(
        """
<style>
    html, body, [class*="css"] {
        font-family: 'Segoe UI', sans-serif;
        background-color: #0d1117;
        color: #c9d1d9;
    }

    .block-container {
        padding: 3rem 3rem 5rem 3rem;
        background-color: transparent;
        max-width: 100%;
    }

    h1, h2 {
        color: #58a6ff;
        text-align: left;
        font-weight: 700;
        padding-top: 0.5rem;
        padding-bottom: 1rem;
    }

    .stTabs [role="tab"] {
        font-size: 1rem;
        font-weight: 500;
        background-color: #1c2128;
        padding: 0.5rem 1.5rem;
        margin-right: 6px;
        border-radius: 6px;
        transition: all 0.3s ease;
        color: #c9d1d9;
    }

    .stTabs [aria-selected="true"] {
        background-color: #238636;
        color: #ffffff;
    }

    /* INPUTS */
    .stTextInput input, .stSelectbox div[data-baseweb="select"], .stNumberInput input {
        background-color: #0d1117;
        color: #c9d1d9;
        border: 1px solid #30363d;
        border-radius: 6px;
        padding: 0.5rem 0.75rem;
        font-size: 0.95rem;
    }

    .stSelectbox div[data-baseweb="select"] {
        background-color: #0d1117 !important;
        border: 1px solid #30363d !important;
        border-radius: 6px !important;
    }

    .stSelectbox div[data-baseweb="select"]:hover {
        border-color: #58a6ff !important;
    }

    .stSelectbox div[data-baseweb="select"] > div {
        padding: 0.3rem 0.6rem !important;
    }

    .stRadio > div {
        gap: 1rem;
    }

    .stRadio label {
        background-color: #1c2128;
        padding: 0.4rem 1rem;
        border-radius: 6px;
        border: 1px solid #30363d;
        color: #c9d1d9;
    }

    .stRadio input:checked + div {
        background-color: #238636 !important;
        color: #ffffff !important;
    }

    button[kind="primary"] {
        background-color: #2ea043 !important;
        color: white !important;
        font-weight: 600;
        border-radius: 6px;
        padding: 0.6rem 1.2rem;
        border: none;
        transition: background-color 0.2s ease-in-out;
    }

    button[kind="primary"]:hover {
        background-color: #3fb950 !important;
    }

    /* SUCCESS */
    div[data-testid="stAlert-success"] {
        background-color: #1f3322;
        color: #a6e3a1;
        border-left: 4px solid #3fb950;
        border-radius: 6px;
        padding: 0.75rem 1rem;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(63, 185, 80, 0.2);
        margin-top: 1rem;
    }

    /* ERROR / WARNING */
    .stAlert {
        background-color: #2b1d1d;
        border-left: 5px solid #f85149;
        color: #ffb2b2;
        border-radius: 6px;
        margin-top: 1rem;
    }

    div[data-testid="stJson"] {
        background-color: #0f172a;
        color: #cbd5e1;
        border: 1px solid #334155;
        border-radius: 6px;
        padding: 1rem;
        font-family: 'Consolas', monospace;
        font-size: 0.9rem;
        box-shadow: inset 0 0 8px rgba(255, 255, 255, 0.05);
        margin-top: 1rem;
    }

    .stDataFrame {
        border: 1px solid #30363d;
        border-radius: 8px;
        overflow: hidden;
        margin-top: 1rem;
    }

    .stSpinner > div {
        color: #58a6ff;
    }

    .stMarkdown h4 {
        color: #79c0ff;
        margin-top: 2rem;
        margin-bottom: 0.8rem;
        font-weight: 600;
    }
</style>
""",
        unsafe_allow_html=True,
    )
