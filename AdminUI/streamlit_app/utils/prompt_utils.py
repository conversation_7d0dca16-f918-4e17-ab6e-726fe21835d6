import base64
import logging
import os
from typing import Any, Dict, List, Optional
from uuid import UUID

import requests
import streamlit as st
from Crypto.Cipher import AES
from streamlit_app.constants import PROMPT_BASE_URL

logger = logging.getLogger(__name__)


def encrypt_email(email: str) -> str:
    """Encrypt an email address using AES (ECB mode) with a base64-encoded key."""
    hash_key = os.environ.get("HASH_KEY")
    if not hash_key:
        raise ValueError("HASH_KEY environment variable is not set")

    key_bytes = base64.urlsafe_b64decode(hash_key)
    block_size = 16

    def pad(text: str) -> str:
        padding_length = block_size - len(text) % block_size
        return text + chr(padding_length) * padding_length

    cipher = AES.new(key_bytes, AES.MODE_ECB)
    padded_email = pad(email)
    encrypted_bytes = cipher.encrypt(padded_email.encode("utf-8"))
    return base64.urlsafe_b64encode(encrypted_bytes).decode("utf-8")


def decrypt_email(encrypted_email: str) -> str:
    """Decrypt an AES-encrypted email address using ECB mode."""
    hash_key = os.environ.get("HASH_KEY")
    if not hash_key:
        raise ValueError("HASH_KEY environment variable is not set")

    key_bytes = base64.urlsafe_b64decode(hash_key)
    cipher = AES.new(key_bytes, AES.MODE_ECB)

    decoded_bytes = base64.urlsafe_b64decode(encrypted_email)
    decrypted = cipher.decrypt(decoded_bytes).decode("utf-8")

    # Un padding (PKCS#7-like)
    padding_len = ord(decrypted[-1])
    if not 0 < padding_len <= 16:
        raise ValueError("Invalid padding")
    return decrypted[:-padding_len]


class PromptOrchestrationClient:
    """Client for interacting with the Prompt Orchestration service."""

    def __init__(self, base_url: str):
        """Initialize the client with the base URL of the service."""
        self.base_url = base_url.rstrip("/")

    # Prompt operations
    def get_all_prompts(self) -> List[Dict]:
        """Get all available prompts"""
        response = requests.get(f"{self.base_url}/prompts", verify=False)
        response.raise_for_status()
        return response.json().get("data", [])

    def search_prompts(self, search_term: str) -> List[Dict]:
        """Search prompts by text"""
        response = requests.get(f"{self.base_url}/prompts/search", params={"search_term": search_term}, verify=False)
        response.raise_for_status()
        return response.json().get("data", [])

    def get_user_prompts(self, user_id: str) -> List[Dict]:
        """Get all prompts for a specific user"""
        response = requests.get(f"{self.base_url}/prompts/user/{user_id}", verify=False)
        response.raise_for_status()
        return response.json().get("data", [])

    def search_user_prompts(self, user_id: str, search_term: str) -> List[Dict]:
        """Search prompts for a specific user"""
        response = requests.get(f"{self.base_url}/prompts/user/{user_id}/search", params={"search_term": search_term}, verify=False)
        response.raise_for_status()
        return response.json().get("data", [])

    def get_system_prompts(self):
        """Fetch only SYSTEM prompts from the prompt service."""
        response = requests.get(f"{self.base_url}/prompts?prompt_type=SYSTEM")
        response.raise_for_status()
        return response.json().get("data", [])

    # Template operations
    def get_all_templates(self, skip: int = 0, limit: int = 50, use_case: Optional[str] = None, collection_id: Optional[UUID] = None) -> Dict:
        """Get all templates with optional filtering"""
        params: Dict[str, Any] = {"skip": skip, "limit": limit}
        if use_case:
            params["use_case"] = use_case
        if collection_id:
            params["collection_id"] = str(collection_id)

        response = requests.get(f"{self.base_url}/templates", params=params, verify=False)
        response.raise_for_status()
        return response.json()

    def get_template(self, template_id: str) -> Dict:
        """Get a specific template by ID"""
        response = requests.get(f"{self.base_url}/templates/{template_id}", verify=False)
        response.raise_for_status()
        return response.json()

    def create_template(self, template_data: Dict) -> Dict:
        """Create a new template"""
        response = requests.post(f"{self.base_url}/templates", json=template_data, verify=False)
        response.raise_for_status()
        return response.json()

    def update_template(self, template_id: str, template_data: Dict) -> Dict:
        """Update an existing template"""
        response = requests.put(f"{self.base_url}/templates/{template_id}", json=template_data, verify=False)
        response.raise_for_status()
        return response.json()

    def delete_template(self, template_id: str) -> Dict:
        """Delete a template"""
        response = requests.delete(f"{self.base_url}/templates/{template_id}", verify=False)
        response.raise_for_status()
        return response.json()

    def render_template(self, template_id: str, placeholder_values: Dict) -> Dict:
        """Render a template with placeholder values"""
        response = requests.post(f"{self.base_url}/templates/{template_id}/render", json={"placeholder_values": placeholder_values}, verify=False)
        response.raise_for_status()
        return response.json()

    def search_templates(self, search_term: str, skip: int = 0, limit: int = 50, use_case: Optional[str] = None, collection_id: Optional[UUID] = None) -> Dict:
        """Search templates by title, description, or content"""
        params: Dict[str, Any] = {"search_term": search_term, "skip": skip, "limit": limit}
        if use_case:
            params["use_case"] = use_case
        if collection_id:
            params["collection_id"] = str(collection_id)

        response = requests.get(f"{self.base_url}/templates/search", params=params, verify=False)
        response.raise_for_status()
        return response.json()

    def get_user_templates(self, user_email: str, skip: int = 0, limit: int = 50, use_case: Optional[str] = None) -> Dict:
        """Get templates for a specific user"""
        params: Dict[str, Any] = {"skip": skip, "limit": limit}
        if use_case:
            params["use_case"] = use_case

        response = requests.get(f"{self.base_url}/templates/user/{user_email}", params=params, verify=False)
        response.raise_for_status()
        return response.json()

    def get_system_templates(self, skip: int = 0, limit: int = 50) -> Dict:
        """Get system templates"""
        params: Dict[str, Any] = {"skip": skip, "limit": limit}
        response = requests.get(f"{self.base_url}/templates/system", params=params, verify=False)
        response.raise_for_status()
        return response.json()

    def get_unique_users_with_templates(self) -> List[str]:
        """Get list of unique users who have templates"""
        response = requests.get(f"{self.base_url}/templates/users/unique", verify=False)
        response.raise_for_status()
        return response.json()

    def list_users(self) -> List[Dict]:
        """List all users (unique emails)"""
        response = requests.get(f"{self.base_url}/prompts/users/unique", verify=False)
        user_ids = response.json().get("data", [])
        if not user_ids:
            return []

        seen_emails = set()
        user_list = []

        for user_id in user_ids:
            try:
                decrypted_email = decrypt_email(user_id)
                if decrypted_email not in seen_emails:
                    seen_emails.add(decrypted_email)
                    user_list.append({"email": decrypted_email, "user_id": user_id})
            except Exception as e:
                print(f"Skipping invalid user_id due to decryption error: {e}")

        return user_list


# Prompt Orchestration client
prompt_service_client = PromptOrchestrationClient(base_url=PROMPT_BASE_URL)


def fetch_prompts(user: str, search_term: str) -> List[Dict]:
    """Return prompts according to user selection and search."""
    # Trim any whitespace from user parameter
    user = user.strip() if user else user

    if user == "All":
        return prompt_service_client.search_prompts(search_term) if search_term else prompt_service_client.get_all_prompts()
    elif user == "System":
        return prompt_service_client.get_system_prompts()
    # specific user branch
    return prompt_service_client.search_user_prompts(user, search_term) if search_term else prompt_service_client.get_user_prompts(user)


def add_prompt(prompt: Dict):
    """Add prompt to selected_prompts if not present."""
    if "selected_prompts" not in st.session_state:
        st.session_state.selected_prompts = []
    if prompt not in st.session_state.selected_prompts:
        st.session_state.selected_prompts.append(prompt)


def remove_prompt(index: int):
    """Remove prompt at given index from selection and rerun app."""
    st.session_state.selected_prompts.pop(index)
    st.rerun()


def render_prompt_table(prompts: List[Dict], key_prefix: str = "prompt"):
    """Render prompts in a pseudo-table with Select/Deselect buttons."""
    if not prompts:
        st.warning("No prompts found.")
        return

    # Header
    header_cols = st.columns([3, 6, 1])
    header_cols[0].markdown("**Title**")
    header_cols[1].markdown("**Content (preview)**")
    header_cols[2].markdown("**Select**")

    # Rows
    for idx, prompt in enumerate(prompts):
        cols = st.columns([3, 6, 1])
        cols[0].markdown(prompt.get("title", "No Title"))
        preview = prompt.get("content", "")
        cols[1].markdown(word_wrap(preview[:250] + ("…" if len(preview) > 250 else "")))

        # Decide if prompt is already selected
        is_selected = "selected_prompts" in st.session_state and prompt in st.session_state.selected_prompts
        btn_label = "➖" if is_selected else "➕"
        btn_key = f"{key_prefix}_{idx}_{'del' if is_selected else 'add'}"
        if cols[2].button(btn_label, key=btn_key):
            if is_selected:
                remove_prompt(st.session_state.selected_prompts.index(prompt))
            else:
                add_prompt(prompt)


def word_wrap(text: str, width: int = 80) -> str:
    """Simple word wrapping for display."""
    if len(text) <= width:
        return text
    return text[:width] + "…"


def build_final_prompt(custom_input: str) -> str:
    """Build the final prompt from selected prompts OR selected template, and custom input."""
    prompt_parts = []

    # Check if template is selected (templates take precedence over prompts)
    if "selected_template" in st.session_state and st.session_state.selected_template:
        template = st.session_state.selected_template
        # Check if template has been rendered with placeholder values
        if "rendered_template_content" in st.session_state:
            prompt_parts.append(st.session_state.rendered_template_content)
        else:
            # If not rendered, use the template content as-is
            prompt_parts.append(template.get("template_content", ""))
    # Only use prompts if no template is selected
    elif "selected_prompts" in st.session_state and st.session_state.selected_prompts:
        selected_content = "\n\n".join([p.get("content", "") for p in st.session_state.selected_prompts])
        prompt_parts.append(selected_content)

    # Add custom input
    if custom_input:
        prompt_parts.append(custom_input)

    return "\n\n".join(prompt_parts)
