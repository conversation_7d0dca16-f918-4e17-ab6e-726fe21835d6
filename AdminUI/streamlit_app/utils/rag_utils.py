import logging

from pyvis.network import Network
from streamlit_app.constants import INDEXER_BASE_URL
from streamlit_app.utils.auth_utils import ensure_login
from streamlit_app.utils.http_utils import safe_request

logger = logging.getLogger(__name__)


def initialize_network():
    """Initialize empty pyvis network."""
    return Network(height="800px", width="100%", directed=True)


def generate_network_html(net):
    """Get html for the network.

    Args:
        net: pyvis network.
    """
    net.force_atlas_2based(
        gravity=-10,
        central_gravity=0.001,
        spring_length=250,
        spring_strength=0.03,
        damping=0.6,
        overlap=0.8,
    )
    return net.generate_html()


def visualize_row(net, index, row):
    """Add nodes and edges to network.

    Args:
        net: pyvis network.
        index: item id.
        row: item.
    """
    q_node_id = str(index) + "q"
    net.add_node(
        q_node_id,
        title=row["questions"],
        label="question",
        color="orange",
        level=1,
        shape="ellipse",
    )
    a_node_id = str(index) + "a"
    net.add_node(
        a_node_id,
        title=row["outputs"],
        label="answer",
        color="cyan",
        level=1,
        shape="ellipse",
    )
    source_doc_ids = []
    source_doc_contents = []
    for ind, _doc in enumerate(row["source_documents"]):
        _doc_id = str(index) + "source_doc" + str(ind)
        source_doc_ids.append(_doc_id)
        _title = _doc["page_content"]
        _label = _doc["metadata"]["source"].split("/")[-1]
        source_doc_contents.append((_label, _title))
        net.add_node(_doc_id, title=_title, label=_label, color="blue", level=1, shape="square")
        net.add_edge(q_node_id, _doc_id, arrowStrikethrough=True)
        net.add_edge(_doc_id, a_node_id, arrowStrikethrough=True)


def _send_evaluation_request(collection_id, run_name, questions):
    """Send evaluation request to the service.

    Args:
        collection_id: internal collection identifier.
        run_name: name of the experiment.
        questions: list of questions.
    """
    _, headers, _ = ensure_login()
    request_body = {"run_name": run_name, "questions": questions}
    try:
        r = safe_request(
            "post",
            f"{INDEXER_BASE_URL}/collection/{collection_id}/evaluate",
            headers=headers,
            json=request_body,
            timeout=30,
        )
        r.raise_for_status()
    except Exception as e:
        logger.exception(f"failed to send request to evaluation endpoint. {e}")
        return False
    return True
