import logging
import os
from typing import Callable, Dict, List, Optional

import requests
from streamlit_app.utils.http_utils import safe_request

METHODS_MAPPING: Dict[str, Callable] = {
    "post": requests.post,
    "get": requests.get,
    "delete": requests.delete,
    "patch": requests.patch,
}

LOADER_BASE_URL = os.environ.get("LOADER_BASE_URL", "http://loader:8002")
INDEXER_BASE_URL = os.environ.get("INDEXER_BASE_URL", "http://indexer:8001")
QUERY_BASE_URL = os.environ.get("QUERY_BASE_URL", "http://query:8003")

# custom header for admin UI identification

logger = logging.getLogger(__name__)


def send_json_request(
    url: str,
    json_data: Optional[dict] = None,
    method: str = "post",
    headers: Optional[Dict] = None,
    params: Optional[Dict] = None,
):
    """Send a JSON request with automatic token refresh (401 retry)."""
    json_data = json_data or {}

    # safe_request already handles 401 → re-login
    try:
        response = safe_request(
            method,
            url,
            headers=headers,
            json=json_data,
            params=params,
            timeout=200,
        )
        return response
    except requests.exceptions.RequestException as e:
        print(e)
        return f"Error: {e}"


def send_file_to_service(url, file, method: str = "post", headers: Optional[Dict] = None, params=None):
    """Send file to the service."""
    if params is None:
        params = {}
    _func = METHODS_MAPPING[method]
    try:
        files = {"file": file}
        return _func(url, files=files, headers=headers, params=params)
    except requests.exceptions.RequestException as e:
        return f"Error: {e}"


def send_feedback_for_query(
    query_id: str,
    feedback_type: str,
    feedback_value: str,
    feedback_text: Optional[str] = None,
    headers: Optional[Dict] = None,
):
    """Send add feedback request to the service.

    Args:
        query_id: internal query identifier.
        feedback_type: only rate-3 and rate-10 are supported for now.
        feedback_value: e.g. rate-3: good, bad, incomplete or rate-10:1-10
        feedback_text: feedback extra free text.
        headers: request headers.
    """
    request_body = {
        "query_id": query_id,
        "feedback_type": feedback_type,
        "feedback_value": feedback_value,
    }
    if feedback_text:
        request_body["feedback_text"] = feedback_text
    try:
        r = send_json_request(
            f"{QUERY_BASE_URL}/feedback",
            json_data=request_body,
            method="post",
            headers=headers,
        )
        r.raise_for_status()
    except Exception as e:
        logger.exception(f"failed to send request to feedback endpoint. {e}")
        return False
    return True


def delete_feedbacks_from_query(feedback_ids: List[str], headers: Optional[Dict] = None):
    """Delete feedbacks from query.

    Args:
        feedback_ids: internal feedback identifiers.
        headers: request headers.
    """
    succ = []
    for feedback_id in feedback_ids:
        try:
            r = send_json_request(
                f"{QUERY_BASE_URL}/feedback/{feedback_id}",
                method="delete",
                headers=headers,
            )
            r.raise_for_status()
        except Exception as e:
            logger.exception(f"failed to send request to feedback deletion endpoint. {e}")
            succ.append(False)
        succ.append(True)
    return succ
