import os
from datetime import datetime, timezone
from typing import List, Optional, Tuple

import jwt
import streamlit as st
from msal import PublicClientApplication
from streamlit_app.auth import get_token


# ─────────────────────────────────────────────────────────────────────────────
# Helpers
# ─────────────────────────────────────────────────────────────────────────────
def _extract_roles(access_token: str) -> List[str]:
    """Return lower-case role names from JWT or ``['basic']`` on failure."""
    try:
        payload = jwt.decode(
            access_token,
            options={"verify_signature": False, "verify_aud": False},
        )
        raw = payload.get("roles") or payload.get("role") or []
        raw = [raw] if isinstance(raw, str) else raw
        return [str(r).lower() for r in raw] or ["basic"]
    except Exception:
        return ["basic"]


def _fullpage_msg(message: str, emoji: str = "🚫") -> None:
    """Render a centered full-page message and stop execution."""
    st.markdown(
        f"""
        <style>
        .auth-container {{
            display:flex;justify-content:center;align-items:center;height:80vh;
        }}
        .auth-box {{
            background-color:#1e1e1e;padding:2rem;border-radius:1rem;width:100%;
            max-width:420px;text-align:center;color:white;
            box-shadow:0 0 20px rgba(0,0,0,.5);
        }}
        </style>
        <div class="auth-container">
          <div class="auth-box">
            <h3>{emoji} {message}</h3>
            <p>If you believe this is an error, please contact an administrator.</p>
          </div>
        </div>
        """,
        unsafe_allow_html=True,
    )
    st.stop()


def _is_token_expired(token: str, leeway_seconds: int = 60) -> bool:
    """Return True if the JWT `exp` claim is in the past (plus *leeway_seconds*)."""
    try:
        payload = jwt.decode(
            token,
            options={"verify_signature": False, "verify_aud": False},
        )
        exp = payload.get("exp")
        if exp is None:
            return False  # token without exp – treat as non-expiring (unlikely)
        now_ts = int(datetime.now(timezone.utc).timestamp())
        return exp <= now_ts + leeway_seconds
    except Exception:
        # Any decoding issue – assume token is invalid/expired so we can re-auth
        return True


# ─────────────────────────────────────────────────────────────────────────────
# Main function
# ─────────────────────────────────────────────────────────────────────────────
def ensure_login(*, admin_only: bool = True) -> Optional[Tuple[str, dict, List[str]]]:  # only keyword-args after this  # set True on admin-only pages
    """
    Authenticate the user and return ``(token, headers, roles)``.

    Parameters
    ----------
    admin_only :
        When **True**, non-admin users see a full-page
        *“You do not have sufficient permissions to view this page”*
        message and execution stops.

    """
    # ── Local dev shortcut ───────────────────────────────────────────────
    if os.getenv("LOCAL_DEV", "False").lower() == "true":
        roles = ["admin"]
        if admin_only and "admin" not in roles:
            _fullpage_msg("You do not have sufficient permissions to view this page.")
        return "dev-token", {"X-Admin-UI": "true"}, roles

    token_key, roles_key = "auth_token", "roles"

    # ── 1) Cached credentials ───────────────────────────────────────────
    if token_key in st.session_state and roles_key in st.session_state:
        token = st.session_state[token_key]
        # Check if the cached token has expired (or is about to)
        if _is_token_expired(token):
            # Remove stale credentials and restart auth flow
            st.session_state.pop(token_key, None)
            st.session_state.pop(roles_key, None)
            st.rerun()
        roles = st.session_state[roles_key]
        if admin_only and "admin" not in roles:
            _fullpage_msg("You do not have sufficient permissions to view this page.")
        return token, {"Authorization": f"Bearer {token}"}, roles

    # ── 2) MSAL – silent or device flow ─────────────────────────────────
    result, error_msg, client_id, authority = get_token()
    if error_msg:
        _fullpage_msg(f"Authentication error: {error_msg}", emoji="❌")

    # 2-a silent login success
    if isinstance(result, dict) and "access_token" in result:
        token = result["access_token"]
        roles = _extract_roles(token)
        st.session_state.update({token_key: token, roles_key: roles})
        if admin_only and "admin" not in roles:
            _fullpage_msg("You do not have sufficient permissions to view this page.")
        return token, {"Authorization": f"Bearer {token}"}, roles

    # 2-b interactive device flow
    flow = result
    st.markdown(
        f"""
            <style>
            .auth-container {{
                display: flex;
                justify-content: center;
                align-items: center;
                height: 80vh;
            }}
            .auth-box {{
                background-color: #1e1e1e;
                padding: 2rem;
                border-radius: 1rem;
                width: 100%;
                max-width: 420px;
                text-align: center;
                color: white;
                box-shadow: 0 0 20px rgba(0,0,0,0.5);
            }}
            .code-box {{
                background-color: #222;
                color: #0f0;
                font-size: 1.6rem;
                font-weight: bold;
                padding: 0.6rem 0.8rem;
                border-radius: 0.6rem;
                margin: 1rem 0;
                position: relative;
            }}
            .auth-link {{
                color: #1e90ff;
                font-weight: bold;
                display: inline-block;
                margin-top: 1rem;
                text-decoration: none;
            }}
            .auth-link:hover {{
                text-decoration: underline;
            }}
            </style>
            <div class="auth-container">
              <div class="auth-box">
                <h3>🔐 Login with your Organisation Account</h3>
                <p>Copy the code below and click the link to sign in:</p>
                <div class="code-box">
                  <span id="user-code">{flow['user_code']}</span>
                </div>
                <a href="{flow['verification_uri']}" target="_blank" class="auth-link">
                  Click here to complete login
                </a>
                <p style="margin-top:1rem; color:#ccc; font-size:0.9rem;">
                  Once you're signed in, the app will continue automatically.
                </p>
              </div>
            </div>
            """,
        unsafe_allow_html=True,
    )

    app = PublicClientApplication(client_id, authority=authority)
    token_data = app.acquire_token_by_device_flow(flow)

    # ── 3) Cache or fail ────────────────────────────────────────────────
    if token_data and "access_token" in token_data:
        token = token_data["access_token"]
        roles = _extract_roles(token)
        st.session_state.update({token_key: token, roles_key: roles})
        if admin_only and "admin" not in roles:
            _fullpage_msg("You do not have sufficient permissions to view this page.")
        user_email = token_data.get("id_token_claims", {}).get("preferred_username")
        if user_email:
            st.session_state["user_email"] = user_email
        st.rerun()

    _fullpage_msg("Authentication failed.", emoji="❌")
    return None  # never reached
