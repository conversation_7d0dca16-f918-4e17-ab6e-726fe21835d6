# Core Dependencies
wheel==0.45.1        # A package for building and distributing Python wheels (binary distributions).
setuptools==76.0.0   # A package management tool for installing, upgrading, and managing Python packages.

# Web Frameworks and API
fastapi-azure-auth==5.1.0      # Azure AD authentication integration for FastAPI.
requests==2.32.3               # Popular HTTP library for making API requests in Python.
httpx==0.28.1                  # Asynchronous HTTP client for Python, supporting HTTP/2 and connection pooling.
streamlit==1.43.0              # Web application framework for creating interactive data applications.

# Data Processing and Machine Learning
pandas==2.2.3            # Data analysis and manipulation library for structured data.
numpy==1.26.4            # Numerical computing library for arrays, matrices, and mathematical operations.

# Visualization and Plotting
matplotlib==3.10.1   # Visualization library for creating static, animated, and interactive plots.
plotly==6.0.0        # Interactive graphing library for creating web-based plots and dashboards.
scipy==1.15.2        # Scientific computing library for optimization, signal processing, and statistics.
pyvis==0.3.2         # Library for creating and visualizing network graphs in an interactive way.
networkx==3.2.1      # Graph analysis library for creating, manipulating, and studying complex networks.

# Authentication and Security
msal==1.32.3           # Microsoft Authentication Library for Python, used for Azure AD authentication.
pycryptodome==3.21.0 # Cryptographic library for Python, used for secure data handling.

# Database
redis==5.2.1                  # Redis client for Python, used for caching and real-time data processing.
