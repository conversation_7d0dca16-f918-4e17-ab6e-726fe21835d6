#!/usr/bin/env bash
set -euo pipefail

echo "🚀 Entrypoint started for LoaderWorker"
echo "Queue Prefix : ${DRAMATIQ_QUEUE_PREFIX:-prod}"
echo "Log Level    : ${DRAMATIQ_LOG_LEVEL:-WARNING}"

exec dramatiq loader_app.tasks \
     --processes 1 \
     --threads   2 \
     --queues "${DRAMATIQ_QUEUE_PREFIX:-prod}.loader.document_ready_queue" \
              "${DRAMATIQ_QUEUE_PREFIX:-prod}.loader.document_load_queue" \
              "${DRAMATIQ_QUEUE_PREFIX:-prod}.loader.document_fail_queue" \
              "${DRAMATIQ_QUEUE_PREFIX:-prod}.loader.document_move_queue" \
              "${DRAMATIQ_QUEUE_PREFIX:-prod}.loader.url_added_queue" \
              "${DRAMATIQ_QUEUE_PREFIX:-prod}.loader.url_move_queue" \
              "${DRAMATIQ_QUEUE_PREFIX:-prod}.loader.url_load_queue" \
              "${DRAMATIQ_QUEUE_PREFIX:-prod}.loader.url_load_failed_queue"
