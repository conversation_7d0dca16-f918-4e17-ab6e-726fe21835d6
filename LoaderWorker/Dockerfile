############################
# 1. Builder stage
############################
FROM python:3.11-slim AS builder
WORKDIR /src

RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential gcc \
    && rm -rf /var/lib/apt/lists/*

COPY LoaderWorker/requirements.txt .

RUN --mount=type=cache,target=/root/.cache/pip \
    pip wheel -r requirements.txt --wheel-dir /wheels


############################
# 2. Runtime stage
############################
FROM python:3.11-slim AS runtime
WORKDIR /app

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PATH="/home/<USER>/.local/bin:$PATH"

# Install basic tools and dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl ca-certificates gnupg \
    build-essential gcc \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install GUI libraries for browser automation
RUN apt-get update && apt-get install -y --no-install-recommends \
    libnss3 libatk1.0-0 libatk-bridge2.0-0 libcups2 libxkbcommon0 \
    libxcomposite1 libxdamage1 libxrandr2 libgbm1 libasound2 \
    libpangocairo-1.0-0 libgtk-3-0 \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install document processing tools
RUN apt-get update && apt-get install -y --no-install-recommends \
    poppler-utils tesseract-ocr libmagic-dev \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install optional packages (may fail)
RUN apt-get update && (apt-get install -y --no-install-recommends \
    libmupdf-dev libreoffice pandoc || true) \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install development libraries with dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    libssl-dev libicu-dev sensible-utils \
    libjpeg62-turbo-dev libfreetype-dev \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install final development headers
RUN apt-get update && apt-get install -y --no-install-recommends \
    libxml2-dev libxslt1-dev libpq-dev libgeos-dev \
    libjpeg-dev libpng-dev zlib1g-dev libfreetype6-dev libgeos-c1v5 \
    && apt-get clean && rm -rf /var/lib/apt/lists/*


RUN adduser --disabled-password --gecos "" appuser
USER appuser

COPY --from=builder /wheels /wheels
RUN pip install --no-index --find-links=/wheels /wheels/*

COPY --chown=appuser LoaderService/loader_app /app/loader_app

COPY --chown=appuser LoaderWorker/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh
ENTRYPOINT ["/entrypoint.sh"]
