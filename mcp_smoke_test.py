#!/usr/bin/env python3
"""
Quick smoke-test for the MCP gateway (/mcp JSON-RPC 2.0 endpoint)

• Performs the required initialise → tools/list handshake
• Executes the four sample tools (vector_search, …)
• Streams results for vector_search_stream via SSE

Requires: requests, sseclient-py   (pip install requests sseclient-py)
"""
import json
import os
import textwrap
import time

import requests
import sseclient

GATEWAY = os.getenv("GATEWAY", "http://localhost:8004")
TOKEN = os.getenv("TOKEN")
CID = os.getenv("CID", "8652f723-cfe6-447c-9e44-6e3855e3af3a")  # collection_id to query
QUERY = "what is MCP?"
HEADERS = {"Authorization": f"Bearer {TOKEN}", "Content-Type": "application/json"}

# ───────────────────────── helpers ──────────────────────────
RID = 0


def rpc(method: str, params=None, stream=False) -> requests.Response:
    """Send a JSON-RPC 2.0 request to the MCP gateway."""
    global RID
    RID += 1
    body = {"jsonrpc": "2.0", "id": RID, "method": method}
    if params is not None:
        body["params"] = params
    return requests.post(f"{GATEWAY}/mcp", headers=HEADERS, json=body, stream=stream)


def pretty(resp, title=""):
    """Pretty-print the response from the MCP gateway."""
    bar = "─" * 60
    if title:
        print(f"\n── {title} {bar[len(title) + 4:]}")
    if isinstance(resp, requests.Response):
        print(f"[{resp.status_code}] {resp.url}")
        ct = resp.headers.get("content-type", "")
        if "application/json" in ct:
            print(json.dumps(resp.json(), indent=2, ensure_ascii=False))
        else:
            print(textwrap.shorten(resp.text, 400))
    else:  # parsed dict/obj
        print(json.dumps(resp, indent=2, ensure_ascii=False))
    print(bar)


# ───────────────────────── run ──────────────────────────────
print(f"Gateway → {GATEWAY}")

# 1) health (optional – may be disabled)
health = requests.get(f"{GATEWAY}/health", headers=HEADERS)
pretty(health, "/health")

# 2) MCP handshake
pretty(rpc("initialize"), "initialize")
pretty(rpc("tools/list"), "tools/list")

# 3-a) vector_search
pretty(rpc("tools/call", {"name": "vector_search", "args": {"collection_id": CID, "query": QUERY, "top_k": 3}}), "vector_search")

# 3-b) vector_search_update  (custom_prompt ≈ query)
pretty(
    rpc("tools/call", {"name": "vector_search_update", "args": {"collection_id": CID, "query_id": "76ad4d13-9b36-4daa-a86e-5197afa33256", "adjust_type": "more_detail"}}),
    "vector_search_update",
)

# 3-c) get_retrieved_docs
pretty(rpc("tools/call", {"name": "get_retrieved_docs", "args": {"collection_id": CID, "query": QUERY}}), "get_retrieved_docs")

# 3-d) vector_search_stream (SSE)
print("\n── vector_search_stream (SSE) ───────────────────────────")
stream_resp = rpc("tools/call", {"name": "vector_search_stream", "args": {"collection_id": CID, "query": QUERY}}, stream=True)

if stream_resp.status_code != 200:
    pretty(stream_resp)
else:
    client = sseclient.SSEClient(stream_resp)
    t0 = time.time()
    try:
        for ev in client.events():
            print(ev.data)
            if "[DONE]" in ev.data or time.time() - t0 > 15:
                break
    finally:
        stream_resp.close()
print("─" * 60)

# 4) UI proxy still works
pretty(requests.get(f"{GATEWAY}/collection/{CID}/queries", headers=HEADERS), f"REST proxy /collection/{CID}/queries")
