<template>
  <!-- Mobile Sidebar Overlay -->
  <div v-if="appStore.isSidebarVisible" class="mobile-sidebar-overlay" @click="appStore.toggleSidebar">
    <div class="mobile-sidebar-container" @click.stop>
      <UiLiquidGlassCard
        class="mobile-sidebar"
        size="lg"
        rounded
      >
        <!-- Header with New Chat button -->
        <div class="mobile-header">
          <UButton
            icon="i-lucide-plus"
            label="New chat"
            variant="subtle"
            color="neutral"
            size="lg"
            class="cursor-pointer w-full flex items-center justify-center rounded border"
            :class="appStore.theme === 'light' ? 'text-black-700 hover:text-black-900 border-black-600' : 'text-white/90 hover:text-white'"
            @click="handleNewChat"
          />
        </div>

        <!-- Search (if needed) -->
        <div v-if="showSearch" class="mobile-search">
          <div class="mobile-search-wrapper">
            <UInput
              v-model="searchQuery"
              placeholder="Search..."
              class="w-full mobile-search-input"
              :ui="{
                base: 'bg-white/10 border-white/20 text-white rounded px-4',
                placeholder: 'placeholder-white/50',
                input: 'text-white px-2',
                icon: { base: 'text-white/60' },
                color: { white: { outline: 'text-white' } }
              }"
              @keyup.esc="closeSearch"
            />
            <button
              v-if="searchQuery"
              type="button"
              class="mobile-clear-search-btn"
              @click="clearSearchInput"
            >
              <UIcon name="i-lucide-x" class="w-4 h-4" />
            </button>
          </div>
        </div>

        <!-- Sessions List -->
        <div class="mobile-sessions-list">
          <div v-if="isSearching && !hasSearchResults" class="empty-state">
            <UIcon name="i-lucide-search-x" class="w-8 h-8 mx-auto mb-2" :class="appStore.theme === 'light' ? 'text-gray-300' : 'text-white/30'" />
            <p class="text-sm text-center mb-2" :class="appStore.theme === 'light' ? 'text-gray-600' : 'text-white/60'">
              No results found
            </p>
            <UButton
              label="Clear"
              size="xs"
              variant="soft"
              @click="clearSearchInput"
            />
          </div>
          
          <div v-else>
            <!-- Show only Favorites when appStore.showFavorites is true -->
            <div v-if="appStore.showFavorites">
              <div class="mobile-section-header">
                <h3 class="text-base font-bold" :class="appStore.theme === 'light' ? 'text-gray-900' : 'text-white'">
                   Favorites
                </h3>
                <UIcon
                  :name="expandedSections.favoritesOnly ? 'i-lucide-chevron-down' : 'i-lucide-chevron-right'"
                  class="w-4 h-4 cursor-pointer"
                  :class="appStore.theme === 'light' ? 'text-gray-600 hover:text-gray-800' : 'text-white/80 hover:text-white'"
                  @click="toggleSection('favoritesOnly')"
                />
              </div>

              <div v-show="expandedSections.favoritesOnly" class="mobile-session-items">
                <div
                  v-for="session in filteredFavorites"
                  :key="session.id"
                  class="mobile-session-item"
                  :class="{ 'active': currentSessionId === session.id }"
                  @click="handleSessionSelect(session.id)"
                >
                  <div class="mobile-session-content">
                    <UIcon name="i-lucide-star" class="w-4 h-4 text-yellow-400 shrink-0" />
                    <span class="mobile-session-title">{{ session.title || 'Untitled Session' }}</span>
                  </div>
                  <SessionDropdown :session="session" @refresh="refreshSessions" />
                </div>
                <div v-if="filteredFavorites.length === 0" class="empty-state">
                  <UIcon name="i-lucide-star-off" class="w-6 h-6 mx-auto mb-1" :class="appStore.theme === 'light' ? 'text-gray-300' : 'text-white/30'" />
                  <p class="text-xs text-center" :class="appStore.theme === 'light' ? 'text-gray-500' : 'text-white/50'">
                    No favorites yet
                  </p>
                </div>
              </div>
            </div>

            <!-- Show everything else when appStore.showFavorites is false -->
            <div v-else>
              <!-- Favorites Section - at the top -->
              <div class="mobile-section-header">
                <h3 class="text-base font-bold" :class="appStore.theme === 'light' ? 'text-gray-900' : 'text-white'">
                  ⭐ Favorites
                </h3>
                <UIcon
                  :name="expandedSections.favoritesOnly ? 'i-lucide-chevron-down' : 'i-lucide-chevron-right'"
                  class="w-4 h-4 cursor-pointer"
                  :class="appStore.theme === 'light' ? 'text-gray-600 hover:text-gray-800' : 'text-white/80 hover:text-white'"
                  @click="toggleSection('favoritesOnly')"
                />
              </div>

              <div v-show="expandedSections.favoritesOnly" class="mobile-session-items">
                <div
                  v-for="session in filteredFavorites"
                  :key="session.id"
                  class="mobile-session-item"
                  :class="{ 'active': currentSessionId === session.id }"
                  @click="handleSessionSelect(session.id)"
                >
                  <div class="mobile-session-content">
                    <UIcon name="i-lucide-star" class="w-4 h-4 text-yellow-400 shrink-0" />
                    <span class="mobile-session-title">{{ session.title || 'Untitled Session' }}</span>
                  </div>
                  <SessionDropdown :session="session" @refresh="refreshSessions" />
                </div>
                <div v-if="filteredFavorites.length === 0" class="empty-state">
                  <UIcon name="i-lucide-star-off" class="w-6 h-6 mx-auto mb-1" :class="appStore.theme === 'light' ? 'text-gray-300' : 'text-white/30'" />
                  <p class="text-xs text-center" :class="appStore.theme === 'light' ? 'text-gray-500' : 'text-white/50'">
                    No favorites yet
                  </p>
                </div>
              </div>

              <!-- Recents Header -->
              <div class="mobile-section-header">
                <h3 class="text-base font-bold" :class="appStore.theme === 'light' ? 'text-gray-900' : 'text-white'">
                  💬 Recents
                </h3>
                <div class="flex items-center gap-2">
                  <UIcon
                    name="i-lucide-search"
                    class="w-4 h-4 cursor-pointer"
                    :class="appStore.theme === 'light' ? 'text-gray-600 hover:text-gray-800' : 'text-white/80 hover:text-white'"
                    @click="toggleSearch"
                  />
                  <UIcon
                    :name="allSectionsExpanded ? 'i-lucide-chevron-up' : 'i-lucide-chevron-down'"
                    class="w-4 h-4 cursor-pointer"
                    :class="appStore.theme === 'light' ? 'text-gray-600 hover:text-gray-800' : 'text-white/80 hover:text-white'"
                    @click="toggleAllSections"
                  />
                </div>
              </div>

              <!-- Loading State -->
              <div v-if="isLoadingSessions" class="loading-container">
                <UIcon name="i-lucide-loader-2" class="w-6 h-6 animate-spin mb-2" :class="appStore.theme === 'light' ? 'text-gray-400' : 'text-white/40'" />
                <p class="text-xs" :class="appStore.theme === 'light' ? 'text-gray-500' : 'text-white/50'">
                  Loading...
                </p>
              </div>

              <!-- Empty State -->
              <div v-else-if="!hasAnySessions" class="empty-state">
                <UIcon name="i-lucide-inbox" class="w-8 h-8 mx-auto mb-2" :class="appStore.theme === 'light' ? 'text-gray-300' : 'text-white/30'" />
                <p class="text-sm text-center mb-2" :class="appStore.theme === 'light' ? 'text-gray-500' : 'text-white/50'">
                  No sessions yet
                </p>
                <p class="text-xs text-center mb-3" :class="appStore.theme === 'light' ? 'text-gray-400' : 'text-white/40'">
                  Start a new chat
                </p>
                <UButton
                  icon="i-lucide-plus"
                  label="New chat"
                  variant="soft"
                  size="xs"
                  @click="handleNewChat"
                />
              </div>

              <!-- Sessions -->
              <div v-else>
                <!-- Folder Sessions -->
                <div
                  v-for="(sessions, folderName) in (isSearching ? filteredFolderSessions : folderSessions)"
                  :key="folderName"
                  class="mobile-folder-section"
                >
                  <div class="mobile-folder-header" @click="toggleSection(folderName)">
                    <div class="flex items-center gap-2">
                      <UIcon
                        :name="expandedSections[folderName] ? 'i-lucide-chevron-down' : 'i-lucide-chevron-right'"
                        class="w-3 h-3"
                        :class="appStore.theme === 'light' ? 'text-gray-500' : 'text-white/60'"
                      />
                      <UIcon name="i-lucide-folder" class="w-3 h-3" :class="appStore.theme === 'light' ? 'text-gray-600' : 'text-white/70'" />
                      <h4 class="text-sm font-medium" :class="appStore.theme === 'light' ? 'text-gray-800' : 'text-white/90'">
                        {{ folderName }}
                      </h4>
                    </div>
                    <span class="text-xs" :class="appStore.theme === 'light' ? 'text-gray-500' : 'text-white/50'">
                      {{ sessions.length }}
                    </span>
                  </div>
                  <div v-show="isSearching || expandedSections[folderName]" class="mobile-session-items">
                    <div
                      v-for="session in sessions"
                      :key="session.id"
                      class="mobile-session-item"
                      :class="{ 'active': currentSessionId === session.id }"
                      @click="handleSessionSelect(session.id)"
                    >
                      <div class="mobile-session-content">
                        <UIcon name="i-lucide-message-circle" class="w-4 h-4 shrink-0" :class="appStore.theme === 'light' ? 'text-gray-500' : 'text-white/60'" />
                        <span class="mobile-session-title">{{ session.title || 'Untitled Session' }}</span>
                      </div>
                      <SessionDropdown :session="session" @refresh="refreshSessions" />
                    </div>
                  </div>
                </div>

                <!-- Unassigned Sessions -->
                <div v-if="unassignedSessions.length > 0" class="mobile-folder-section">
                  <div class="mobile-folder-header" @click="toggleSection('unassigned')">
                    <div class="flex items-center gap-2">
                      <UIcon
                        :name="expandedSections.unassigned ? 'i-lucide-chevron-down' : 'i-lucide-chevron-right'"
                        class="w-3 h-3"
                        :class="appStore.theme === 'light' ? 'text-gray-500' : 'text-white/60'"
                      />
                      <h4 class="text-sm font-medium" :class="appStore.theme === 'light' ? 'text-gray-800' : 'text-white/90'">
                        Recent
                      </h4>
                    </div>
                    <span class="text-xs" :class="appStore.theme === 'light' ? 'text-gray-500' : 'text-white/50'">
                      {{ unassignedSessions.length }}
                    </span>
                  </div>
                  <div v-show="isSearching || expandedSections.unassigned" class="mobile-session-items">
                    <div
                      v-for="session in (isSearching ? filteredUnassignedSessions : unassignedSessions)"
                      :key="session.id"
                      class="mobile-session-item"
                      :class="{ 'active': currentSessionId === session.id }"
                      @click="handleSessionSelect(session.id)"
                    >
                      <div class="mobile-session-content">
                        <UIcon name="i-lucide-message-circle" class="w-4 h-4 shrink-0" :class="appStore.theme === 'light' ? 'text-gray-500' : 'text-white/60'" />
                        <span class="mobile-session-title">{{ session.title || 'Untitled Session' }}</span>
                      </div>
                      <SessionDropdown :session="session" @refresh="refreshSessions" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </UiLiquidGlassCard>

      <!-- Controls positioned at the bottom of the mobile sidebar -->
      <div class="mobile-controls-left">
        <UiLiquidGlassCard
          size="sm"
          class="cursor-pointer theme-toggle-btn"
          rounded
          :opacity="0"
          @click="appStore.toggleTheme"
        >
          <UButton
            :icon="appStore.theme === 'dark' ? 'i-lucide-sun' : 'i-lucide-moon'"
            variant="link"
            active-variant="link"
            size="lg"
            class="cursor-pointer control-btn"
            :class="appStore.theme === 'light' ? 'text-gray-700 hover:text-gray-900' : 'text-white hover:text-white'"
          />
        </UiLiquidGlassCard>
      </div>
      <div class="mobile-controls-right">
        <UiLiquidGlassCard
          size="sm"
          class="cursor-pointer"
          rounded
          :opacity="0"
          @click="appStore.toggleSidebar"
        >
          <UButton
            icon="i-lucide-x"
            variant="link"
            active-variant="link"
            size="lg"
            class="cursor-pointer control-btn"
            :class="appStore.theme === 'light' ? 'text-gray-700 hover:text-gray-900' : 'text-white hover:text-white'"
          />
        </UiLiquidGlassCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '~/stores/app'
import { useSidebar } from '~/composables/useSidebar'
import SessionDropdown from './SessionDropdown.vue'

const appStore = useAppStore()

const {
  searchQuery,
  showSearch,
  expandedSections,
  unassignedSessions,
  folderSessions,
  currentSessionId,
  isLoadingSessions,
  hasAnySessions,
  filteredFavorites,
  allSectionsExpanded,
  toggleSection,
  toggleAllSections,
  selectSession,
  handleNewChat,
  refreshSessions,
  toggleSearch,
  closeSearch,
  isSearching,
  filteredFolderSessions,
  filteredUnassignedSessions,
  hasSearchResults,
  clearSearchInput
} = useSidebar()

// Handle session selection and close sidebar on mobile
function handleSessionSelect(sessionId: string) {
  selectSession(sessionId)
  appStore.toggleSidebar() // Close sidebar after selection on mobile
}
</script>

<style scoped>
.mobile-sidebar-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 50;
  backdrop-filter: blur(4px);
}

.mobile-sidebar-container {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  max-width: 380px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
}

.mobile-sidebar {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.mobile-header {
  margin-bottom: 1rem;
}

.mobile-search {
  margin-bottom: 1.5rem;
}

.mobile-search-wrapper {
  position: relative;
}

.mobile-clear-search-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  padding: 4px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: rgba(255,255,255,0.7);
  cursor: pointer;
}

.mobile-clear-search-btn:hover {
  background: rgba(255,255,255,0.1);
  color: #fff;
}

html.light .mobile-clear-search-btn {
  color: var(--brand-primary);
}

html.light .mobile-clear-search-btn:hover {
  background: rgba(0,0,0,0.06);
}

html.dark .mobile-search-input :deep(input) {
  color: white !important;
}

html.dark .mobile-search-input :deep(input::placeholder) {
  color: rgba(255, 255, 255, 0.5) !important;
}

html.light .mobile-search-input :deep(input) {
  color: var(--text-primary) !important;
}

html.light .mobile-search-input :deep(input::placeholder) {
  color: var(--text-muted) !important;
}

/* Controls positioned at bottom */
.mobile-controls-left {
  position: absolute;
  bottom: 0rem;
  left: 0rem;
  display: flex;
  gap: 0.5rem;
}

.mobile-controls-right {
  position: absolute;
  bottom: 0rem;
  right: 0rem;
  display: flex;
  gap: 0.5rem;
}

.mobile-sessions-list {
  flex: 1;
  overflow-y: auto;
  padding-right: 0.5rem;
  margin-right: -0.5rem;
}

.mobile-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 0;
  margin-bottom: 0.5rem;
  border-bottom: 1px solid;
}

html.light .mobile-section-header {
  border-bottom-color: rgba(0, 0, 0, 0.05);
}

html.dark .mobile-section-header {
  border-bottom-color: rgba(255, 255, 255, 0.05);
}

.mobile-folder-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  margin-bottom: 0.25rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.mobile-folder-header:hover {
  background-color: var(--hover-bg);
}

.mobile-session-items {
  margin-bottom: 1rem;
}

.mobile-session-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 0.5rem;
  margin-bottom: 0.125rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
}

.mobile-session-item:hover {
  background-color: var(--hover-bg);
}

.mobile-session-item.active {
  background-color: var(--hover-bg);
  border-left: 3px solid var(--brand-primary);
}

.mobile-session-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  min-width: 0;
}

.mobile-session-title {
  flex: 1;
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

html.dark .mobile-session-title {
  color: rgba(255, 255, 255, 0.9);
}

html.light .mobile-session-title {
  color: var(--text-primary);
}

.mobile-folder-section {
  margin-bottom: 0.5rem;
}

.empty-state {
  padding: 2rem 1rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-container {
  padding: 2rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* Use existing theme system variables */
.mobile-sidebar {
  background: var(--glass-bg) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid var(--glass-border) !important;
}

/* Scrollbar */
.mobile-sessions-list::-webkit-scrollbar {
  width: 4px;
}

.mobile-sessions-list::-webkit-scrollbar-track {
  background: var(--scrollbar-bg);
  border-radius: 10px;
}

.mobile-sessions-list::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 10px;
}

.mobile-sessions-list::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

@media (max-width: 480px) {
  .mobile-sidebar-container {
    max-width: 100%;
    padding: 0.5rem;
  }
  
  .mobile-header {
    padding-bottom: 0.5rem;
    margin-bottom: 0.5rem;
  }
}
</style>