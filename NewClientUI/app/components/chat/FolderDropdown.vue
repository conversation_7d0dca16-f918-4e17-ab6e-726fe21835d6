<template>
  <div class="folder-dropdown-container" :data-folder-dropdown="folderName" @click.stop>
    <button
      class="folder-dropdown-trigger"
      type="button"
      @click.stop="toggleDropdown"
    >
      <UIcon name="i-lucide-more-vertical" class="w-4 h-4" />
    </button>

    <Teleport to="body">
      <Transition
        enter-active-class="transition ease-out duration-100"
        enter-from-class="transform opacity-0 scale-95"
        enter-to-class="transform opacity-100 scale-100"
        leave-active-class="transition ease-in duration-75"
        leave-from-class="transform opacity-100 scale-100"
        leave-to-class="transform opacity-0 scale-95"
      >
        <div v-if="isOpen" class="dropdown-overlay" @click="closeDropdown">
          <div class="dropdown-menu-wrapper" :style="dropdownStyle" @click.stop>
            <UiLiquidGlassCard class="dropdown-menu" size="sm" rounded>
              <button class="dropdown-item" type="button" @click="handleRename">
                <UIcon name="i-lucide-edit-2" class="w-4 h-4" />
                <span>Or<PERSON>er umbenennen</span>
              </button>

              <div class="dropdown-divider" />

              <button class="dropdown-item text-red-400 hover:text-red-300 hover:bg-red-500/20" type="button" @click="handleDelete">
                <UIcon name="i-lucide-trash-2" class="w-4 h-4" />
                <span>Ordner löschen</span>
              </button>
            </UiLiquidGlassCard>
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { useDialog } from '~/composables/useDialog'
import { useCollectionsStore } from '~/stores/collections'
import { useAppStore } from '~/stores/app'

interface Props {
  folderName: string
}

const props = defineProps<Props>()
const emit = defineEmits<{ refresh: [] }>()

const dialog = useDialog()
const collectionsStore = useCollectionsStore()
const appStore = useAppStore()

const isOpen = ref(false)
const dropdownStyle = ref<Record<string, string>>({})

const toggleDropdown = () => {
  isOpen.value ? closeDropdown() : openDropdown()
}

const openDropdown = () => {
  isOpen.value = true
  nextTick(updateDropdownPosition)
}

const closeDropdown = () => {
  isOpen.value = false
}

const updateDropdownPosition = () => {
  const trigger = document.querySelector(`[data-folder-dropdown="${props.folderName}"] .folder-dropdown-trigger`) as HTMLElement
  if (!trigger) return
  const rect = trigger.getBoundingClientRect()
  const dropdownWidth = 220
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  let left = rect.right + 8
  let top = rect.top + rect.height / 2

  if (left + dropdownWidth > viewportWidth - 10) {
    left = rect.left - dropdownWidth - 8
  }

  const dropdownHeight = 160
  if (top - dropdownHeight / 2 < 10) top = 10 + dropdownHeight / 2
  else if (top + dropdownHeight / 2 > viewportHeight - 10) top = viewportHeight - 10 - dropdownHeight / 2

  dropdownStyle.value = {
    position: 'fixed',
    left: `${left}px`,
    top: `${top}px`,
    transform: 'translateY(-50%)',
    zIndex: '9999'
  }
}

const handleRename = async () => {
  isOpen.value = false
  const newName = await dialog.prompt({
    title: 'Ordner umbenennen',
    placeholder: 'Neuer Ordnername',
    initialValue: props.folderName,
    confirmText: 'Umbenennen',
    cancelText: 'Abbrechen',
    required: true
  })
  if (!newName || newName.trim() === '' || newName.trim() === props.folderName) return

  try {
    await collectionsStore.renameFolder(props.folderName, newName.trim())
    appStore.addNotification({ type: 'success', title: 'Ordner umbenannt' })
    emit('refresh')
  } catch (e: any) {
    appStore.addNotification({ type: 'error', title: 'Fehler beim Umbenennen', message: e?.message || '' })
  }
}

const handleDelete = async () => {
  isOpen.value = false
  const confirmed = await dialog.confirm({
    title: 'Ordner löschen',
    message: `Möchten Sie den Ordner "${props.folderName}" wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.`,
    confirmText: 'Löschen',
    cancelText: 'Abbrechen'
  })
  if (!confirmed) return

  try {
    await collectionsStore.deleteFolder(props.folderName)
    appStore.addNotification({ type: 'success', title: 'Ordner gelöscht' })
    emit('refresh')
  } catch (e: any) {
    appStore.addNotification({ type: 'error', title: 'Fehler beim Löschen', message: e?.message || '' })
  }
}

onMounted(() => {
  document.addEventListener('click', outsideClick)
})

onUnmounted(() => {
  document.removeEventListener('click', outsideClick)
})

const outsideClick = (e: MouseEvent) => {
  const container = document.querySelector(`[data-folder-dropdown="${props.folderName}"]`)
  if (container && !container.contains(e.target as Node)) isOpen.value = false
}
</script>

<style scoped>
.folder-dropdown-container {
  position: relative;
  display: inline-flex;
  align-items: center;
}

.folder-dropdown-trigger {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.15s ease;
}

.folder-dropdown-trigger:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

html.light .folder-dropdown-trigger {
  color: var(--brand-primary);
}
html.light .folder-dropdown-trigger:hover {
  background-color: rgba(0,0,0,0.06);
}

.dropdown-overlay {
  position: fixed;
  inset: 0;
  z-index: 9998;
}

.dropdown-menu-wrapper {
  position: fixed;
}

.dropdown-menu {
  min-width: 220px;
  padding: 0.5rem;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.5rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  text-align: left;
  border-radius: 0.375rem;
  transition: all 0.15s;
  background-color: transparent;
  border: none;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

html.light .dropdown-item {
  color: #000;
}
html.light .dropdown-item:hover {
  background-color: var(--hover-bg);
  color: #000;
}

.dropdown-divider {
  height: 1px;
  margin: 0.5rem 0;
  background-color: rgba(255, 255, 255, 0.1);
}
</style>
