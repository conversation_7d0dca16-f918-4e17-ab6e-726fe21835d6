<template>
  <div class="session-dropdown-container" :data-session-dropdown="session.id" @click.stop>
    <!-- Trigger <PERSON>ton -->
    <button
      :class="{ 'opacity-100': isHovered || isOpen, 'opacity-0': !isHovered && !isOpen }"
      class="session-dropdown-trigger"
      @click.stop="toggleDropdown"
    >
      <UIcon name="i-lucide-more-vertical" class="w-4 h-4" />
    </button>

    <!-- Dropdown Menu -->
    <Teleport to="body">
      <Transition
        enter-active-class="transition ease-out duration-100"
        enter-from-class="transform opacity-0 scale-95"
        enter-to-class="transform opacity-100 scale-100"
        leave-active-class="transition ease-in duration-75"
        leave-from-class="transform opacity-100 scale-100"
        leave-to-class="transform opacity-0 scale-95"
      >
        <div v-if="isOpen" class="dropdown-overlay" @click="closeDropdown">
          <div
            class="dropdown-menu-wrapper"
            :style="dropdownStyle"
            @click.stop
          >
            <UiLiquidGlassCard class="dropdown-menu" size="sm" rounded>
              <!-- Rename -->
              <button
                class="dropdown-item"
                @click="handleRename"
              >
                <UIcon name="i-lucide-edit-2" class="w-4 h-4" />
                <span>Umbenennen</span>
              </button>

              <!-- Toggle Favorite -->
              <button
                class="dropdown-item"
                @click="handleToggleFavorite"
              >
                <UIcon
                  :name="session.is_favorite ? 'i-lucide-star-off' : 'i-lucide-star'"
                  class="w-4 h-4"
                />
                <span>{{ session.is_favorite ? 'Favorit entfernen' : 'Als Favorit' }}</span>
              </button>

              <!-- Download -->
              <button
                class="dropdown-item"
                @click="handleDownload"
              >
                <UIcon name="i-lucide-download" class="w-4 h-4" />
                <span>Als Datei herunterladen</span>
              </button>

              <!-- Show Files (if PDFs uploaded) -->
              <button
                v-if="session.uploaded_pdfs"
                class="dropdown-item"
                @click="handleShowFiles"
              >
                <UIcon name="i-lucide-folder-open" class="w-4 h-4" />
                <span>Hochgeladene Dateien</span>
              </button>

              <div class="dropdown-divider" />

              <!-- Delete -->
              <button
                class="dropdown-item text-red-400 hover:text-red-300 hover:bg-red-500/20"
                @click="handleDelete"
              >
                <UIcon name="i-lucide-trash-2" class="w-4 h-4" />
                <span>Löschen</span>
              </button>
            </UiLiquidGlassCard>
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { useCollectionsStore, type Session } from '~/stores/collections'
import { useDialog } from '~/composables/useDialog'
import { useAppStore } from '~/stores/app'

interface Props {
  session: Session
}

const props = defineProps<Props>()
const emit = defineEmits<{
  refresh: []
}>()

const collectionsStore = useCollectionsStore()
const appStore = useAppStore()
const dialog = useDialog()

const isHovered = ref(false)
const isOpen = ref(false)
const dropdownStyle = ref({})

const uploadedFiles = computed(() => {
  if (!props.session.uploaded_pdfs) return []
  return props.session.uploaded_pdfs.split(',').map(f => f.trim())
})

onMounted(() => {
  const parent = document.querySelector(`.session-item[data-session-id="${props.session.id}"]`)
  if (parent) {
    parent.addEventListener('mouseenter', () => {
      isHovered.value = true
    })
    parent.addEventListener('mouseleave', () => {
      isHovered.value = false
    })
  }

  document.addEventListener('click', handleOutsideClick)
})

onUnmounted(() => {
  document.removeEventListener('click', handleOutsideClick)
})

const toggleDropdown = () => {
  if (isOpen.value) {
    closeDropdown()
  } else {
    openDropdown()
  }
}

const openDropdown = () => {
  isOpen.value = true
  nextTick(() => {
    updateDropdownPosition()
  })
}

const closeDropdown = () => {
  isOpen.value = false
}

const updateDropdownPosition = () => {
  const trigger = document.querySelector(`[data-session-dropdown="${props.session.id}"] .session-dropdown-trigger`) as HTMLElement
  if (!trigger) return

  const rect = trigger.getBoundingClientRect()
  const dropdownWidth = 200
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  let left = rect.right + 8
  let top = rect.top + (rect.height / 2)

  if (left + dropdownWidth > viewportWidth - 10) {
    left = rect.left - dropdownWidth - 8
  }

  const dropdownHeight = 250
  if (top - (dropdownHeight / 2) < 10) {
    top = 10 + (dropdownHeight / 2)
  } else if (top + (dropdownHeight / 2) > viewportHeight - 10) {
    top = viewportHeight - 10 - (dropdownHeight / 2)
  }

  dropdownStyle.value = {
    position: 'fixed',
    left: `${left}px`,
    top: `${top}px`,
    transform: 'translateY(-50%)',
    zIndex: 9999
  }
}

const handleOutsideClick = (event: MouseEvent) => {
  const target = event.target as Node
  const container = document.querySelector(`[data-session-dropdown="${props.session.id}"]`)
  if (container && !container.contains(target)) {
    isOpen.value = false
  }
}

const handleRename = async () => {
  isOpen.value = false

  const newName = await dialog.prompt({
    title: 'Sitzung umbenennen',
    placeholder: 'Neuer Name für die Sitzung',
    initialValue: props.session.title || 'Unbenannte Sitzung',
    confirmText: 'Umbenennen',
    cancelText: 'Abbrechen',
    required: true
  })

  if (!newName || newName.trim() === '') return

  try {
    await collectionsStore.renameSession(props.session.id, newName.trim())
    appStore.addNotification({
      type: 'success',
      title: 'Erfolgreich umbenannt'
    })
    emit('refresh')
  } catch (e: any) {
    appStore.addNotification({
      type: 'error',
      title: 'Fehler beim Umbenennen',
      message: e?.message || 'Die Sitzung konnte nicht umbenannt werden.'
    })
  }
}

const handleToggleFavorite = async () => {
  isOpen.value = false
  try {
    await collectionsStore.updateFavoriteStatus(props.session.id, !props.session.is_favorite)
    appStore.addNotification({
      type: 'success',
      title: props.session.is_favorite ? 'Favorit entfernt' : 'Als Favorit markiert'
    })
    emit('refresh')
  } catch (e: any) {
    appStore.addNotification({
      type: 'error',
      title: 'Fehler',
      message: e?.message || 'Der Favoritenstatus konnte nicht geändert werden.'
    })
  }
}

const handleDelete = async () => {
  isOpen.value = false

  const confirmed = await dialog.confirm({
    title: 'Sitzung löschen',
    message: 'Möchten Sie diese Sitzung wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.',
    confirmText: 'Löschen',
    cancelText: 'Abbrechen'
  })

  if (!confirmed) return

  try {
    await collectionsStore.deleteSession(props.session.id)
    appStore.addNotification({
      type: 'success',
      title: 'Sitzung gelöscht'
    })
    emit('refresh')
  } catch (e: any) {
    appStore.addNotification({
      type: 'error',
      title: 'Fehler beim Löschen',
      message: e?.message || 'Die Sitzung konnte nicht gelöscht werden.'
    })
  }
}

const handleDownload = () => {
  isOpen.value = false
  appStore.addNotification({
    type: 'info',
    title: 'Download-Funktion',
    message: 'Diese Funktion wird noch implementiert.'
  })
}

const handleShowFiles = async () => {
  isOpen.value = false
  const files = uploadedFiles.value.join('\n')

  await dialog.alert(
    files || 'Keine Dateien hochgeladen',
    'Hochgeladene Dateien'
  )
}
</script>

<style scoped>
.session-dropdown-container {
  position: relative;
  display: flex;
  align-items: center;
}

/* Light mode: improve contrast for dropdown items */
html.light .dropdown-item {
  color: #000000;
}
html.light .dropdown-item:hover {
  background-color: var(--hover-bg);
  color: #000000;
}

.session-dropdown-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  transition: all 0.2s;
  color: rgba(255, 255, 255, 0.6);
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
}

.session-dropdown-trigger:hover {
  color: rgba(255, 255, 255, 1);
  background-color: rgba(255, 255, 255, 0.1);
}

.dropdown-overlay {
  position: fixed;
  inset: 0;
  z-index: 9998;
}

.dropdown-menu-wrapper {
  position: fixed;
}

.dropdown-menu {
  min-width: 200px;
  padding: 0.5rem;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.5rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  text-align: left;
  border-radius: 0.375rem;
  transition: all 0.15s;
  background-color: transparent;
  border: none;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.dropdown-divider {
  height: 1px;
  margin: 0.5rem 0;
  background-color: rgba(255, 255, 255, 0.1);
}
</style>
