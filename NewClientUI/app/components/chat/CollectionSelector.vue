<template>
  <div class="collection-selector-container" :data-collection-selector="true" @click.stop>
    <!-- Trigger <PERSON> -->
    <UTooltip :text="currentCollection?.name || 'Select Collection'" :popper="{ placement: 'right' }" :prevent="truncatedCollectionName === (currentCollection?.name || 'Select Collection')">
      <button
        ref="triggerEl"
        type="button"
        class="collection-selector-trigger"
        @click.stop="toggleDropdown"
      >
        <UIcon :name="currentIcon" class="w-4 h-4" />
        <span class="collection-name">{{ truncatedCollectionName }}</span>
        <UIcon name="i-lucide-chevron-down" class="w-3 h-3 ml-auto" />
      </button>
    </UTooltip>

    <!-- Dropdown Menu -->
    <Teleport to="body">
      <Transition
        enter-active-class="transition ease-out duration-100"
        enter-from-class="transform opacity-0 scale-95"
        enter-to-class="transform opacity-100 scale-100"
        leave-active-class="transition ease-in duration-75"
        leave-from-class="transform opacity-100 scale-100"
        leave-to-class="transform opacity-0 scale-95"
      >
        <div v-if="isOpen" class="dropdown-overlay" @click="closeDropdown">
          <div
            class="dropdown-menu-wrapper"
            :style="dropdownStyle"
            @click.stop
          >
            <UiLiquidGlassCard class="dropdown-menu-card" size="sm" rounded>
              <div class="dropdown-menu">
                <button
                  v-for="collection in collections"
                  :key="collection.id"
                  type="button"
                  class="dropdown-item"
                  :class="{
                    active: collection.id === modelValue,
                    'expandable': collection.name.length > 24
                  }"
                  @click="selectCollection(collection)"
                >
                  <UIcon :name="getCollectionIcon(collection.iconKey)" class="w-4 h-4 flex-shrink-0" />
                  <span class="item-name">{{ collection.name }}</span>
                </button>
              </div>
            </UiLiquidGlassCard>
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { useCollectionsStore } from '~/stores/collections'

interface Props {
  modelValue: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', collection: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const collectionsStore = useCollectionsStore()

const isOpen = ref(false)
const dropdownStyle = ref({})
const triggerEl = ref<HTMLElement | null>(null)

const collections = computed(() => collectionsStore.collections)
const currentCollection = computed(() =>
  collections.value.find(c => c.id === props.modelValue)
)
const currentIcon = computed(() =>
  getCollectionIcon(currentCollection.value?.iconKey)
)
const truncatedCollectionName = computed(() =>
  truncateName(currentCollection.value?.name || 'Select Collection')
)

const truncateName = (name: string) => {
  if (name.length <= 24) return name
  return name.substring(0, 24) + '...'
}

const getCollectionIcon = (iconKey?: string) => {
  const iconMap: { [key: string]: string } = {
    '01': 'i-lucide-scale',
    '02': 'i-lucide-briefcase',
    '03': 'i-lucide-shield',
    '04': 'i-lucide-file-text'
  }

  return iconKey && iconMap[iconKey] ? iconMap[iconKey] : 'i-lucide-folder'
}
const toggleDropdown = () => {
  if (isOpen.value) {
    closeDropdown()
  } else {
    openDropdown()
  }
}

const openDropdown = () => {
  isOpen.value = true
  nextTick(() => {
    updateDropdownPosition()
  })
}

const closeDropdown = () => {
  isOpen.value = false
}

const updateDropdownPosition = () => {
  const trigger = triggerEl.value
  if (!trigger) return

  const rect = trigger.getBoundingClientRect()
  const spacing = 8 // Space between trigger and dropdown
  const DROPDOWN_HEIGHT = 260 // Fixed height so it always sits above input

  // Always position above the trigger with a fixed height
  const top = rect.top - DROPDOWN_HEIGHT - spacing

  dropdownStyle.value = {
    position: 'fixed',
    left: `${rect.left}px`,
    top: `${top}px`,
    width: `${Math.max(rect.width, 200)}px`,
    height: `${DROPDOWN_HEIGHT}px`,
    zIndex: 9999,
    overflow: 'visible'
  }
}

const selectCollection = (collection: any) => {
  emit('update:modelValue', collection.id)
  emit('change', collection)
  closeDropdown()
}

const handleEscape = (e: KeyboardEvent) => {
  if (e.key === 'Escape' && isOpen.value) {
    closeDropdown()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleEscape)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleEscape)
})
</script>

<style scoped>
.collection-selector-container {
  position: relative;
  display: inline-block;
  min-width: 180px;
  max-width: 300px;
}

/* Light mode styling */
html.light .collection-selector-trigger {
  color: #000000;
  background-color: rgba(255, 255, 255, 0.8);
  border-color: rgba(0, 0, 0, 0.2);
}

html.light .collection-selector-trigger:hover {
  background-color: rgba(255, 255, 255, 0.9);
  border-color: rgba(0, 0, 0, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.collection-selector-trigger {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  color: white;
  background-color: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.15s;
}

.collection-selector-trigger:hover {
  background-color: rgba(0, 0, 0, 0.95);
  border-color: rgba(255, 255, 255, 0.3);
}

.collection-name {
  flex: 1;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-overlay {
  position: fixed;
  inset: 0;
  z-index: 9998;
}

.dropdown-menu-wrapper {
  position: fixed;
  overflow: visible !important;
}

.dropdown-menu-card {
  height: inherit;
  overflow: visible !important;
  width: 250px;
}

.dropdown-menu {
  height: 100%;
  overflow-y: auto; /* Allow vertical scroll */
  overflow-x: hidden; /* Hide horizontal scroll */
  padding: 0.5rem;
  padding-right: 0.25rem; /* Reduce right padding to account for hidden scrollbar */
  display: flex;
  flex-direction: column;
  position: relative;
}
.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: calc(100% - 0.25rem); /* Account for scrollbar space */
  padding: 0.5rem;
  margin-bottom: 0.15rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  text-align: left;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  background-color: transparent;
  border: none;
  cursor: pointer;
  position: relative;
  z-index: 1;
}

/* Light mode dropdown items */
html.light .dropdown-item {
  color: #000000;
}

html.light .dropdown-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #000000;
}

html.light .dropdown-item.active {
  background-color: rgba(0, 0, 0, 0.08);
  color: #000000;
}

.dropdown-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.dropdown-item.expandable:hover {
  width: auto;
  min-width: 100%;
  z-index: 1000;
  position: relative;
  margin-right: -200px; /* Allow expansion beyond container */
  padding-right: 1rem;
  font-size: 11px;
}

.dropdown-item.expandable:hover .item-name {
  overflow: visible !important;
  text-overflow: unset !important;
  white-space: nowrap !important;
  max-width: none !important;
}

.dropdown-item.active {
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
}

.item-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  transition: all 0.2s ease;
}

/* Hide scrollbar but keep functionality */
.dropdown-menu::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

/* For Firefox */
.dropdown-menu {
  scrollbar-width: none;
  -ms-overflow-style: none; /* IE and Edge */
}

@media (max-width: 480px) {
  .collection-selector-container {
    display: none;
  }
}
</style>
