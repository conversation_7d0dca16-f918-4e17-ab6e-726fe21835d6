<template>
  <div
    :class="[
      'liquid-glass-card',
      size === 'sm' ? 'p-2' : size === 'lg' ? 'p-8' : 'p-6',
      { 'liquid-glass-clear': clear },
      appStore.theme
    ]"
    :style="computedStyle"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '~/stores/app'

const appStore = useAppStore()

interface Props {
  size?: 'sm' | 'md' | 'lg'
  rounded?: boolean
  clear?: boolean
  opacity?: number
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  rounded: false,
  clear: false,
  opacity: 0.01
})

const computedStyle = computed(() => {
  const isLight = appStore.theme === 'light'

  if (isLight) {
    const opacity = Math.max(0, Math.min(1, props.opacity))
    const baseOpacity = 0.6 + (opacity * 0.05)
    const gradientOpacity = 0.10 + (opacity * 0.15)

    return {
      borderRadius: props.rounded ? '16px' : '12px',
      background: `
        linear-gradient(0deg, rgba(255, 255, 255, ${baseOpacity}), rgba(255, 255, 255, ${baseOpacity})),
        linear-gradient(180deg, rgba(255, 255, 255, ${gradientOpacity}) 0%, rgba(240, 240, 245, ${gradientOpacity + 0.2}) 100%)
      `,
      border: '1px solid rgba(0, 0, 0, 0.2)'
    }
  } else {
    const whiteOpacity = Math.max(0, Math.min(1, props.opacity))
    const bgOpacity2 = 0.06 + (whiteOpacity * 0.1)
    return {
      borderRadius: props.rounded ? '16px' : '12px',
      background: `
        linear-gradient(0deg, rgba(255, 255, 255, ${whiteOpacity}), rgba(255, 255, 255, ${whiteOpacity})),
        linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, ${bgOpacity2}) 100%)
      `
    }
  }
})
</script>

<style scoped>
.liquid-glass-card {
  backdrop-filter: blur(120px);
  -webkit-backdrop-filter: blur(120px);

  /* Border with gradient image source */
  border-image-source: linear-gradient(270deg, rgba(255, 255, 255, 0.04) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.04) 100%);
  border-image-slice: 1;

  box-shadow: 0px 5px 9px 0px rgba(255, 255, 255, 0.08) inset;

  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Light mode shadow */
html.light .liquid-glass-card {
  box-shadow: 
    0px 5px 9px 0px rgba(0, 0, 0, 0.05) inset,
    0 2px 8px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04);
}

.liquid-glass-card > * {
  position: relative;
  z-index: 0;
}

.liquid-glass-card:hover {
  background:
    linear-gradient(0deg, rgba(255, 255, 255, 0.026), rgba(255, 255, 255, 0.026)),
    linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.104) 100%);
  border-image-source: linear-gradient(270deg, rgba(255, 255, 255, 0.078) 0%, rgba(255, 255, 255, 0.39) 50%, rgba(255, 255, 255, 0.078) 100%);
  box-shadow:
    0px 5px 15px 0px rgba(255, 255, 255, 0.156) inset,
    0 0 34px rgba(135, 10, 43, 0.254),
    0 0 68px rgba(135, 10, 43, 0.169),
    0 0 101px rgba(135, 10, 43, 0.085);
}

/* Light mode hover glow override: blueish tone (#1e293b) */
html.light .liquid-glass-card:hover {
  box-shadow:
    0px 5px 15px 0px rgba(255, 255, 255, 0.156) inset,
    0 0 34px rgba(30, 41, 59, 0.254),
    0 0 68px rgba(30, 41, 59, 0.169),
    0 0 101px rgba(30, 41, 59, 0.10);
}
</style>
