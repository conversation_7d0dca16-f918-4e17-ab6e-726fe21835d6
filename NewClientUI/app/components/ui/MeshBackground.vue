<template>
  <div class="mesh-background" :class="{ 'mesh-animated': animated }">
    <div
      v-if="showMeshBox"
      class="mesh-box"
      :class="meshBoxPositionClass"
      :style="{ opacity: meshBoxOpacity }"
    />
    <slot />
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '~/stores/app'

interface Props {
  animated?: boolean
  showMeshBox?: boolean
  meshBoxPosition?: 'left' | 'right' | 'center'
  meshBoxOpacity?: number
  variant?: 'default' | 'dark' | 'light'
}

const props = withDefaults(defineProps<Props>(), {
  animated: false,
  showMeshBox: true,
  meshBoxPosition: 'left',
  meshBoxOpacity: 0.4,
  variant: 'default'
})

const appStore = useAppStore()

const meshBoxPositionClass = computed(() => {
  switch (props.meshBoxPosition) {
    case 'right':
      return 'mesh-box-right'
    case 'center':
      return 'mesh-box-center'
    default:
      return 'mesh-box-left'
  }
})

onMounted(() => {
  appStore.initTheme()
})
</script>

<style scoped>
/* Dark mode (default) */
html.dark .mesh-background,
.mesh-background {
  background: linear-gradient(
    135deg,
    #050506 0%,
    #060505 25%,
    #0f141a 50%,
    #374151 75%,
    #4b5563 100%
  );
  position: relative;
  min-height: 100vh;
  transition: background 0.3s ease;
}

/* Light mode */
html.light .mesh-background {
  background: linear-gradient(
    135deg,
    #ffffff 0%,
    #f9fafb 25%,
    #f3f4f6 50%,
    #e5e7eb 75%,
    #d1d5db 100%
  );
}

.mesh-animated {
  animation: meshDarken 8s ease-in-out infinite;
}

html.light .mesh-animated {
  animation: meshDarkenLight 8s ease-in-out infinite;
}

/* Mesh grid overlay */
.mesh-box {
  position: absolute;
  top: 0%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

html.dark .mesh-box,
.mesh-box {
  background-image:
    linear-gradient(rgba(120, 120, 120, 0.6) 1px, transparent 1px),
    linear-gradient(90deg, rgba(120, 120, 120, 0.6) 1px, transparent 1px);
  background-size: 70px 70px;
}

/* Color overlays */
html.dark .mesh-background::before,
.mesh-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(to bottom right, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.1) 100%),
    radial-gradient(circle at 6% 5%, rgba(25, 93, 113, 0.5) 0%, transparent 30%),
    radial-gradient(circle at 95% 90%, rgba(1, 13, 20, 0.9) 0%, transparent 60%),
    radial-gradient(circle at 24% 98%, rgba(135, 10, 43, 0.3) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

html.light .mesh-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #044456E5;
  pointer-events: none;
  z-index: 0;
}

.mesh-background > * {
  position: relative;
  z-index: 2;
}

@keyframes meshDarken {
  0%, 100% {
    background: linear-gradient(
      135deg,
      #050506 0%,
      #060505 25%,
      #0f141a 50%,
      #374151 75%,
      #4b5563 100%
    );
  }
  50% {
    background: linear-gradient(
      135deg,
      #040405 0%,
      #050404 25%,
      #0d1218 50%,
      #2e3749 75%,
      #3f4b59 100%
    );
  }
}

@keyframes meshDarkenLight {
  0%, 100% {
    background: linear-gradient(
      135deg,
      #ffffff 0%,
      #f9fafb 25%,
      #f3f4f6 50%,
      #e5e7eb 75%,
      #d1d5db 100%
    );
  }
  50% {
    background: linear-gradient(
      135deg,
      #fafafa 0%,
      #f3f4f6 25%,
      #e5e7eb 50%,
      #d1d5db 75%,
      #9ca3af 100%
    );
  }
}
</style>
