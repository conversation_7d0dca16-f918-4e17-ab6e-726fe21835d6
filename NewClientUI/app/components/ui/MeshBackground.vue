<template>
  <div class="mesh-background" :class="{ 'mesh-animated': animated }">
    <div
      v-if="showMeshBox"
      class="mesh-box"
      :class="meshBoxPositionClass"
      :style="{ opacity: meshBoxOpacity }"
    />
    <slot />
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '~/stores/app'

interface Props {
  animated?: boolean
  showMeshBox?: boolean
  meshBoxPosition?: 'left' | 'right' | 'center'
  meshBoxOpacity?: number
  variant?: 'default' | 'dark' | 'light'
}

const props = withDefaults(defineProps<Props>(), {
  animated: false,
  showMeshBox: true,
  meshBoxPosition: 'left',
  meshBoxOpacity: 0.4,
  variant: 'default'
})

const appStore = useAppStore()

const meshBoxPositionClass = computed(() => {
  switch (props.meshBoxPosition) {
    case 'right':
      return 'mesh-box-right'
    case 'center':
      return 'mesh-box-center'
    default:
      return 'mesh-box-left'
  }
})

onMounted(() => {
  appStore.initTheme()
})
</script>

<style scoped>
/* Dark mode (default) */
html.dark .mesh-background,
.mesh-background {
  background: linear-gradient(
    135deg,
    #050506 0%,
    #060505 25%,
    #0f141a 50%,
    #374151 75%,
    #4b5563 100%
  );
  position: relative;
  min-height: 100vh;
  transition: background 0.3s ease;
}

/* Light mode - more vibrant and distinct */
html.light .mesh-background {
  background: linear-gradient(
    135deg,
    #ffffff 0%,
    #d9e3ea 20%,
    #f1f5f9 40%,
    #dce6f0 70%,
    #cbd5e1 80%,
    #94a3b8 100%
  );
}

.mesh-animated {
  animation: meshDarken 8s ease-in-out infinite;
}

html.light .mesh-animated {
  animation: meshDarkenLight 8s ease-in-out infinite;
}

/* Mesh grid overlay */
.mesh-box {
  position: absolute;
  top: 0%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

html.dark .mesh-box,
.mesh-box {
  background-image:
    linear-gradient(rgba(120, 120, 120, 0.6) 1px, transparent 1px),
    linear-gradient(90deg, rgba(120, 120, 120, 0.6) 1px, transparent 1px);
  background-size: 70px 70px;
}

html.light .mesh-box {
  background-image:
    linear-gradient(rgba(60, 80, 120, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(60, 80, 120, 0.1) 1px, transparent 1px),
    linear-gradient(45deg, transparent 35%, rgba(100, 116, 139, 0.05) 35%, rgba(100, 116, 139, 0.15) 65%, transparent 65%);
  background-size: 50px 50px, 50px 50px, 100px 100px;
}

/* Color overlays */
html.dark .mesh-background::before,
.mesh-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(to bottom right, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.1) 100%),
    radial-gradient(circle at 6% 5%, rgba(25, 93, 113, 0.5) 0%, transparent 30%),
    radial-gradient(circle at 95% 90%, rgba(1, 13, 20, 0.9) 0%, transparent 60%),
    radial-gradient(circle at 24% 98%, rgba(135, 10, 43, 0.3) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

html.light .mesh-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(to bottom right, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%),
    radial-gradient(circle at 10% 10%, rgba(59, 130, 246, 0.15) 0%, transparent 40%),
    radial-gradient(circle at 90% 90%, rgba(168, 85, 247, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 30% 80%, rgba(16, 185, 129, 0.08) 0%, transparent 60%);
  pointer-events: none;
  z-index: 0;
}

.mesh-background > * {
  position: relative;
  z-index: 2;
}

@keyframes meshDarken {
  0%, 100% {
    background: linear-gradient(
      135deg,
      #050506 0%,
      #060505 25%,
      #0f141a 50%,
      #374151 75%,
      #4b5563 100%
    );
  }
  50% {
    background: linear-gradient(
      135deg,
      #040405 0%,
      #050404 25%,
      #0d1218 50%,
      #2e3749 75%,
      #3f4b59 100%
    );
  }
}

@keyframes meshDarkenLight {
  0%, 100% {
    background: linear-gradient(
      135deg,
      #ffffff 0%,
      #f8fafc 20%,
      #f1f5f9 40%,
      #e2e8f0 60%,
      #cbd5e1 80%,
      #94a3b8 100%
    );
  }
  50% {
    background: linear-gradient(
      135deg,
      #f8fafc 0%,
      #f1f5f9 20%,
      #e2e8f0 40%,
      #cbd5e1 60%,
      #94a3b8 80%,
      #64748b 100%
    );
  }
}
</style>
