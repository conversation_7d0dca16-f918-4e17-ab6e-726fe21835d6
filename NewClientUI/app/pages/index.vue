<script setup lang="ts">
const { isAuthenticated, getToken, setToken, login, isLoading, error } = useAuth()

definePageMeta({
  layout: 'general',
  auth: false
})

// Handle OAuth callback and authentication
onMounted(async () => {
  // Check for access token in URL (OAuth callback)
  const route = useRoute()
  const accessToken = route.query.access_token as string

  if (accessToken) {
    // Store token in localStorage and Pinia store
    setToken(accessToken)
    // Clean URL
    window.history.replaceState({}, document.title, '/')
    // Let the auth middleware handle the redirect to /landing
    return
  }

  // If already authenticated, let the middleware handle redirect
  // Don't manually redirect here to avoid conflicts
})

async function handleLogin() {
  try {
    await login()
  } catch (err) {
    console.error('Login error:', err)
  }
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center px-4">
    <div class="w-full max-w-md">
      <UiLiquidGlassCard>
        <div class="space-y-6">
          <div class="space-y-2 text-center">
            <img
              src="/images/fbeta.png"
              alt="fbeta"
              class="h-10 mx-auto"
            >
          </div>
          <div>
            <h2 class="text-xl font-bold mb-2 text-white/80">
              Login
            </h2>
            <p class="text-sm text-white/60">
              Click below to authenticate with your account
            </p>
          </div>

          <!-- Error Message -->
          <div v-if="error" class="p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
            <p class="text-sm text-red-200">
              {{ error }}
            </p>
          </div>

          <!-- Login Button -->
          <div class="space-y-4">
            <UButton
              :loading="isLoading"
              :disabled="isLoading"
              block
              size="md"
              class="btn-primary-custom text-white border-0 cursor-pointer"
              :aria-busy="isLoading ? 'true' : 'false'"
              @click="handleLogin"
            >
              <span class="inline-flex items-center gap-2">
                <span>{{ isLoading ? 'Redirecting...' : 'Login with OAuth' }}</span>
              </span>
            </UButton>
          </div>
        </div>
      </UiLiquidGlassCard>
    </div>
  </div>
</template>
