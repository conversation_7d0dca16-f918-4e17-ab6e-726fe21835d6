/* Default dark theme */
html.dark {
  --bg-primary: #1b1718;
  --bg-secondary: #232021;
  --text-primary: rgba(255, 255, 255, 0.9);
  --text-secondary: rgba(255, 255, 255, 0.6);
  --text-muted: rgba(152, 3, 3, 0.4);
  --border-primary: rgba(255, 255, 255, 0.2);
  --border-secondary: rgba(255, 255, 255, 0.1);
  --hover-bg: rgba(255, 255, 255, 0.1);
  --glass-bg: rgba(255, 255, 255, 0.01);
  --glass-border: rgba(255, 255, 255, 0.2);
  --mesh-gradient-1: #870a2b;
  --mesh-gradient-2: #b30e3a;
  --mesh-gradient-3: #d41245;
  --scrollbar-bg: rgba(255, 255, 255, 0.05);
  --scrollbar-thumb: rgba(255, 255, 255, 0.2);
  --scrollbar-thumb-hover: rgba(255, 255, 255, 0.3);
}

/* Fallback for initial load - defaults to dark theme to prevent white flash */
:root,
html {
  --bg-primary: #1b1718;
  --bg-secondary: #232021;
  --text-primary: rgba(255, 255, 255, 0.9);
  --text-secondary: rgba(255, 255, 255, 0.6);
  --text-muted: rgba(255, 255, 255, 0.4);
  --border-primary: rgba(255, 255, 255, 0.2);
  --border-secondary: rgba(255, 255, 255, 0.1);
  --hover-bg: rgba(255, 255, 255, 0.1);
  --glass-bg: rgba(255, 255, 255, 0.01);
  --glass-border: rgba(255, 255, 255, 0.2);
  --mesh-gradient-1: #870a2b;
  --mesh-gradient-2: #b30e3a;
  --mesh-gradient-3: #d41245;
  --scrollbar-bg: rgba(255, 255, 255, 0.05);
  --scrollbar-thumb: rgba(255, 255, 255, 0.2);
  --scrollbar-thumb-hover: rgba(255, 255, 255, 0.3);
  /* Brand and UI variables */
  --brand-primary: #AD3A4F; /* wine */
  --ui-primary: #870a2b; /* default to wine (dark mode) */
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

html.light {
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --text-primary: rgba(0, 0, 0, 0.87);
  --text-secondary: rgba(0, 0, 0, 0.6);
  --text-muted: rgba(0, 0, 0, 0.38);
  --border-primary: rgba(0, 0, 0, 0.12);
  --border-secondary: rgba(0, 0, 0, 0.06);
  --hover-bg: rgba(0, 0, 0, 0.04);
  --glass-bg: rgba(255, 255, 255, 0.8);
  --glass-border: rgba(0, 0, 0, 0.08);
  --mesh-gradient-1: #25, 93, 113;
  --mesh-gradient-2: #59, 130, 246;
  --mesh-gradient-3: #135, 10, 43;
  --scrollbar-bg: rgba(0, 0, 0, 0.05);
  --scrollbar-thumb: rgba(0, 0, 0, 0.2);
  --scrollbar-thumb-hover: rgba(0, 0, 0, 0.3);
  /* Set primary UI accent for light mode (used by Logo.vue & indicators) */
  --ui-primary: #000000;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

html.dark .text-dynamic {
  color: var(--text-primary);
}

html.light .text-dynamic {
  color: var(--text-primary);
}

html.dark .text-secondary-dynamic {
  color: var(--text-secondary);
}

html.light .text-secondary-dynamic {
  color: var(--text-secondary);
}

html.dark .text-muted-dynamic {
  color: var(--text-muted);
}

html.light .text-muted-dynamic {
  color: var(--text-muted);
}

html.dark .bg-dynamic {
  background-color: var(--bg-secondary);
}

html.light .bg-dynamic {
  background-color: var(--bg-secondary);
}

html.dark .border-dynamic {
  border-color: var(--border-primary);
}

html.light .border-dynamic {
  border-color: var(--border-primary);
}

html.dark .hover-bg-dynamic:hover {
  background-color: var(--hover-bg);
}

html.light .hover-bg-dynamic:hover {
  background-color: var(--hover-bg);
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

/* Light mode overrides */
/* 1) Force text to black when utility classes use white */
html.light .text-white,
html.light .text-white\/90,
html.light .text-white\/80,
html.light .text-white\/70,
html.light .text-white\/60,
html.light .text-white\/50,
html.light .text-white\/40,
html.light .text-white\/30,
html.light .text-white\/20,
html.light .text-white\/10 {
  color: #000000 !important;
}

/* 2) Force all text elements to black in light mode */
html.light * {
  color: #000000 !important;
}

/* 3) Exception: Keep brand colors and specific UI elements */
html.light .u-icon,
html.light .iconify,
html.light [class*="text-yellow"],
html.light [class*="text-red"],
html.light [class*="text-green"],
html.light [class*="text-blue"],
html.light [class*="text-purple"],
html.light [class*="text-brand"] {
  color: var(--brand-primary) !important;
}

/* 4) Input and textarea specific styling */
html.light input,
html.light textarea,
html.light .chat-input,
html.light [role="textbox"],
html.light [contenteditable="true"] {
  color: #000000 !important;
  background-color: rgba(255, 255, 255, 0.9) !important;
}

html.light input::placeholder,
html.light textarea::placeholder {
  color: rgba(0, 0, 0, 0.6) !important;
}



/* 3) Make image logo black in light mode (for <img alt="fbeta" ...>) */
html.light img[alt="fbeta"] {
  filter: brightness(0) saturate(100%) !important;
}
