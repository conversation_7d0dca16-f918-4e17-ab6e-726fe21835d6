# Use Node.js runtime for development server
FROM node:20-alpine

WORKDIR /app

# Install bash for environment variable substitution
RUN apk add --no-cache bash

# Copy package files
# Since the build context is the root directory, we need the full path
COPY NewClientUI/package*.json ./

# Install dependencies with npm
RUN npm ci || npm install

# Copy source code
COPY NewClientUI/ ./

# Copy environment substitution script if it exists
# Using conditional copy to avoid failure if replacer doesn't exist
COPY NewClientUI/replacer* /replacer
RUN if [ -f /replacer ]; then chmod +x /replacer; fi

# Expose port
EXPOSE 8005

# Start the development server with environment variable substitution
CMD ["/bin/bash", "-c", "if [ -f /replacer ]; then /replacer; fi && npm run dev -- --host 0.0.0.0 --port 8005"]
