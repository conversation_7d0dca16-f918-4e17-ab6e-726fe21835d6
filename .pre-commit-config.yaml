repos:
  # ✅ Auto-format Python files
  - repo: https://github.com/psf/black
    rev: 24.3.0
    hooks:
      - id: black
        language_version: python3.11
        args: [ "--line-length=180" ]
        exclude: ^(\.venv|\.eggs|build|dist)/

  # ✅ Sort imports
  - repo: https://github.com/pre-commit/mirrors-isort
    rev: v5.10.1
    hooks:
      - id: isort
        args: [ "--profile", "black" ]
        exclude: ^(\.venv|\.eggs|build|dist)/

  # ✅ Lint Python (PEP8 + bugs)
  - repo: https://github.com/PyCQA/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        additional_dependencies: [ "flake8-bugbear", "flake8-comprehensions" ]
        args: [ "--max-line-length=180", "--ignore=E203,B008,F841,E731,E402,W503" ]
        exclude: ^(\.venv|\.eggs|build|dist)/


  # ✅ Strip trailing whitespace
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
        exclude: ^(\.md|CHANGELOG\.md)$
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json

  # ✅ Detect merge conflict markers
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: check-merge-conflict



    # ✅ Check for type hints - Static type checker
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.10.0
    hooks:
      - id: mypy
        args: [ "--ignore-missing-imports", "--config-file", "mypy.ini" ]
        exclude: ^(\.venv|\.eggs|build|dist)/


    #  ✅ Check for security vulnerabilities
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.6
    hooks:
      - id: bandit
        args:
          - "-lll"                     # log : LOW, MEDIUM, HIGH
          - "--skip"
          - "B413,B501"               # pyCrypto ve verify=False
        exclude: ^(\.venv|\.eggs|build|dist)/


  # ✅ Check for docstrings in functions and classes
  - repo: https://github.com/PyCQA/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        args:
          - "--convention=google"
          - "--add-ignore=D200,D205,D212,D202,D210,D411,D415,D104,D100"
        exclude: ^(\.venv|\.eggs|build|dist)/
