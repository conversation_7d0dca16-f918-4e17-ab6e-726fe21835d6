#!/usr/bin/env bash
set -euo pipefail

QUEUE_PREFIX=${DRAMATIQ_QUEUE_PREFIX:-prod}
LOG_LEVEL=${DRAMATIQ_LOG_LEVEL:-WARNING}

echo "🚀 Entrypoint started for IndexerWorker"
echo "Queue Prefix : $QUEUE_PREFIX"
echo "Log Level    : $LOG_LEVEL"

exec dramatiq indexer_app.tasks \
     --processes 1 \
     --threads   2 \
     --queues \
       "${QUEUE_PREFIX}.indexer.document_move_queue" \
       "${QUEUE_PREFIX}.indexer.document_embedding_queue" \
       "${QUEUE_PREFIX}.indexer.document_indexed_queue" \
       "${QUEUE_PREFIX}.indexer.document_fail_queue" \
       "${QUEUE_PREFIX}.indexer.document_delete_queue" \
       "${QUEUE_PREFIX}.indexer.delete_collection_queue" \
       "${QUEUE_PREFIX}.indexer.delete_collection_itself_queue" \
       "${QUEUE_PREFIX}.indexer.url_move_queue" \
       "${QUEUE_PREFIX}.indexer.url_loading_failed_queue" \
       "${QUEUE_PREFIX}.indexer.url_embedding_queue" \
       "${QUEUE_PREFIX}.indexer.url_indexed_queue" \
       "${QUEUE_PREFIX}.indexer.url_delete_queue" \
       "${QUEUE_PREFIX}.indexer.rag_evaluation"
