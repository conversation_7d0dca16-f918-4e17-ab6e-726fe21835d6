# IndexerWorker/Dockerfile

############################
# 1) Builder stage         #
############################
FROM python:3.11-slim AS builder

WORKDIR /src

RUN apt-get update && apt-get install -y --no-install-recommends \
        build-essential gcc \
    && rm -rf /var/lib/apt/lists/*

COPY IndexerWorker/requirements.txt ./

RUN --mount=type=cache,target=/root/.cache/pip \
    pip wheel -r requirements.txt --wheel-dir /wheels



############################
# 2) Runtime stage         #
############################
FROM python:3.11-slim AS runtime

WORKDIR /app

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

RUN apt-get update && apt-get install -y --no-install-recommends \
        libmagic-dev \
        poppler-utils \
        tesseract-ocr \
        libjpeg-dev \
        zlib1g-dev \
        libfreetype6-dev \
        libpng-dev \
    && rm -rf /var/lib/apt/lists/*

COPY --from=builder /wheels /wheels
RUN pip install --no-index --find-links=/wheels /wheels/*

COPY IndexerService/indexer_app /app/indexer_app

COPY --chown=appuser IndexerWorker/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh
ENTRYPOINT ["/entrypoint.sh"]
