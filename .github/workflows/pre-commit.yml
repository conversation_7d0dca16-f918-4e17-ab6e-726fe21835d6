name: pre-commit checks

on:
  pull_request:
  push:
    branches: [main]

jobs:
  pre-commit:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: <PERSON><PERSON> pip
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt', '.pre-commit-config.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install pre-commit
        run: |
          python -m pip install --upgrade pip
          pip install pre-commit

      - name: Run pre-commit hooks
        run: pre-commit run --all-files
