name: docker_build_push_PROD

on:
  push:
    branches: [ main ]
  workflow_dispatch:

env:
  IMAGE_PREFIX: rag
  REGISTRY_LOGIN_SERVER: ${{ secrets.REGISTRY_LOGIN_SERVER }}

# ─────────────────────────────────────────────────────────────
# 1) <PERSON>ect changed paths
# ─────────────────────────────────────────────────────────────
jobs:
  changes:
    runs-on: ubuntu-latest
    outputs:
      changes: ${{ steps.filter.outputs.changes }}
    steps:
      - uses: actions/checkout@v4
        with: { fetch-depth: 1 }

      - name: Detect changed paths
        id: filter
        uses: dorny/paths-filter@v3
        with:
          filters: |
            admin_ui:
              - 'AdminUI/**'
            old_client_ui:
              - 'ClientUI/**'
            client_ui:
              - 'NewC<PERSON>UI/**'
            loader_service:
              - 'LoaderService/**'
            indexer_service:
              - 'IndexerService/**'
            query_service:
              - 'QueryService/**'
            loader_worker:
              - 'LoaderWorker/**'
              - 'LoaderService/**'
            indexer_worker:
              - 'IndexerWorker/**'
              - 'IndexerService/**'

# ─────────────────────────────────────────────────────────────
# 2) Push images to Azure Container Registry
# ─────────────────────────────────────────────────────────────
  build:
    needs: changes
    runs-on: ubuntu-latest

    strategy:
      max-parallel: 4
      matrix:
        include:
          - { name: loader-service,  filter_key: loader_service,  context: ., dockerfile: LoaderService/Dockerfile }
          - { name: loader-worker,   filter_key: loader_worker,   context: ., dockerfile: LoaderWorker/Dockerfile }
          - { name: indexer-worker,  filter_key: indexer_worker,  context: ., dockerfile: IndexerWorker/Dockerfile }
          - { name: indexer-service, filter_key: indexer_service, context: ., dockerfile: IndexerService/Dockerfile }
          - { name: admin-ui,        filter_key: admin_ui,        context: ., dockerfile: AdminUI/Dockerfile }
          - { name: query-service,   filter_key: query_service,   context: ., dockerfile: QueryService/Dockerfile }
          - { name: old-client-ui,   filter_key: old_client_ui,   context: ., dockerfile: ClientUI/Dockerfile }
          - { name: client-ui,       filter_key: client_ui,       context: ., dockerfile: NewClientUI/Dockerfile }


    steps:
      - uses: actions/checkout@v4
        with: { fetch-depth: 1 }

      - name: Prune buildx cache and dangling images
        run: |
          docker builder prune -af
          docker system prune -af

      # 1) ACR login
      - uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY_LOGIN_SERVER }}
          username: ${{ secrets.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_SECRET }}

      # 3) Set up Buildx (docker‑container driver)
      - name: Ensure /mnt/buildkit exists
        run: sudo mkdir -p /mnt/buildkit

      - name: Set up Buildx (exec driver)
        id: buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver: docker
          driver-opts: |
            network=host
            root=/mnt/buildkit
          buildkitd-flags: --debug



      # 4) (Optional) Inspect active builder
      - name: Show active Buildx builder
        run: docker buildx inspect --bootstrap

      # 5) Build & push image
      - name: Build & Push ${{ matrix.name }}
        if: contains(needs.changes.outputs.changes, matrix.filter_key)
        uses: docker/build-push-action@v5
        with:
          builder: ${{ steps.buildx.outputs.name }}

          context: ${{ matrix.context }}
          file: ${{ matrix.dockerfile }}
          push: true
          tags: |
            ${{ env.REGISTRY_LOGIN_SERVER }}/${{ env.IMAGE_PREFIX }}-${{ matrix.name }}:latest-prod
            ${{ env.REGISTRY_LOGIN_SERVER }}/${{ env.IMAGE_PREFIX }}-${{ matrix.name }}:${{ github.sha }}

      # 6) Prune buildx cache to save space
      - name: Prune buildx cache to save space
        if: always()
        run: docker builder prune -af
