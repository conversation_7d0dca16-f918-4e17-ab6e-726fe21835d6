import logging
from uuid import UUID

import dramatiq
from indexer_app.core.rag_evaluator import RagEvaluator
from indexer_app.models.enums import SearchMethodsEnum
from indexer_app.service_clients.loader_service_client.sync import get_collection
from indexer_app.settings import settings

logger = logging.getLogger(__name__)


@dramatiq.actor(
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
    queue_name=f"{settings.dramatiq_queue_prefix}.indexer.rag_evaluation",
)
def run_rag_evaluation(collection_id: UUID, run_name: str, questions: list, headers: dict) -> None:
    """Run rag evaluation.

    Args:
        collection_id: collection_id.
        run_name: name of the run.
        questions: list of questions to evaluate.
        headers: headers for the request.
    """
    logger.info(f"Running evaluation for questions: {questions[:20]}...")
    collection = get_collection(collection_id=collection_id, headers=headers)
    if collection:
        rag_evaluator_sem = RagEvaluator(run_name, collection, SearchMethodsEnum.SEMANTIC_SEARCH.value)
        rag_evaluator_hyde = RagEvaluator(run_name, collection, SearchMethodsEnum.HYDE.value)

        rag_evaluator_sem.evaluate(questions)
        rag_evaluator_hyde.evaluate(questions)
