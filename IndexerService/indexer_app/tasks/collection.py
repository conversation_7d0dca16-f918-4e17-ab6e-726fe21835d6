# indexer_app/tasks/collection.py
import logging
import time
from typing import Optional
from uuid import UUID

from indexer_app.core.hyde import Hyde
from indexer_app.core.semantic_search import SemanticSearch
from indexer_app.core.vectordb import VectorDB
from indexer_app.llm.models import (
    DEFAULT_EMBEDDING_MODEL,
    DEFAULT_LLM_MODEL,
    DEFAULT_SEARCH_METHOD,
)
from indexer_app.models.enums import DocumentEventsEnum, SearchMethodsEnum
from indexer_app.service_clients.loader_service_client import (
    sync as loader_service_client_sync,
)
from indexer_app.service_clients.query_service_client.sync import (
    delete_feedbacks_by_query_id,
    delete_queries_by_collection_id,
    get_queries_by_collection_id,
)
from indexer_app.settings import settings
from indexer_app.tasks import QUEUE_PREFIX, dramatiq, rabbitmq_broker
from indexer_app.tasks.document import indexer_move_document_forward
from indexer_app.utils.helpers import delete_session_chat_history_redis_keys

logger = logging.getLogger(__name__)


# ────────────────────────────────────────────────────────────────────────────
# Helpers
# ────────────────────────────────────────────────────────────────────────────
def _delete_collection(collection_id: UUID, headers: Optional[dict] = None) -> None:
    """Remove the collection from Loader + pgvector."""
    collection = loader_service_client_sync.get_collection(collection_id=collection_id, headers=headers)
    if not collection:
        logger.warning(f"Collection {collection_id} not found")
        raise Exception(f"Collection not found: {collection_id}")

    name = collection.get("name")
    cfg = collection.get("custom_config", {})

    emb_model = cfg.get("embedding_model", DEFAULT_EMBEDDING_MODEL)
    llm_model = cfg.get("llm_model", DEFAULT_LLM_MODEL)
    method = cfg.get("method", DEFAULT_SEARCH_METHOD)
    system_prompt = cfg.get("system_prompt")

    # Drop vectors from pgvector (only Semantic Search collections own a table)
    if method == SearchMethodsEnum.SEMANTIC_SEARCH.value:
        ss = SemanticSearch(collection_name=name, embedding_model_name=emb_model)
        VectorDB(ss.vectordb.collection_name, ss._embedding_model).db.delete_collection()
    elif method == SearchMethodsEnum.HYDE.value:
        hyde = Hyde(
            collection_name=name,
            embedding_model_name=emb_model,
            llm_model_name=llm_model,
            custom_system_prompt=system_prompt,
        )
        hyde_pgvector = VectorDB(hyde.vectordb.collection_name, hyde._embedding_model)
        # delete collection from pgvector
        hyde_pgvector.db.delete_collection()
        del hyde, hyde_pgvector

    # Finally remove from Loader DB
    loader_service_client_sync.delete_collection(collection_id=collection_id, headers=headers)


# ────────────────────────────────────────────────────────────────────────────
# Dramatiq actors
# ────────────────────────────────────────────────────────────────────────────
@dramatiq.actor(
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.indexer.delete_collection_queue",
)
def delete_collection(collection_id: UUID, **kwargs) -> None:
    """
    Top-level deletion request:
    • delete all URLs / Documents / Queries / Feedbacks
    • wait until docs & queries disappear, then drop the collection itself.
    """
    headers = kwargs.get("headers", {})

    logger.info("Deleting collection %s ...", collection_id)

    collection = loader_service_client_sync.get_collection(collection_id=collection_id, headers=headers)
    if not collection:
        logger.warning("Collection not found – aborting.")
        return

    # purge URLs immediately
    loader_service_client_sync.delete_urls_by_collection(collection_id=collection_id, headers=headers)

    # purge documents (async per document)
    documents = loader_service_client_sync.get_documents(collection_id=collection_id, headers=headers)
    for doc in documents:
        indexer_move_document_forward.send(
            str(doc.get("id")),
            event=DocumentEventsEnum.DELETE_REQUEST.value,
            **kwargs,
        )

    # purge queries & feedback
    queries = get_queries_by_collection_id(collection_id, headers=headers)
    for q in queries:
        delete_feedbacks_by_query_id(query_id=str(q.get("id")), headers=headers)
    if queries:
        delete_queries_by_collection_id(collection_id=collection_id, headers=headers)

    # If docs/queries existed, schedule a delayed self-check:
    if documents or queries:
        logger.info("Scheduling post-check for collection %s", collection_id)
        delete_collection_itself.send_with_options(args=(collection_id, headers), delay=1000)
    else:
        _delete_collection(collection_id, headers=headers)
        logger.info("Collection %s deleted.", collection_id)

    delete_session_chat_history_redis_keys(collection.get("name"))


@dramatiq.actor(
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms * 10,
    max_age=settings.dramatiq_task_max_age_ms,
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.indexer.delete_collection_itself_queue",
)
def delete_collection_itself(collection_id: UUID, headers: Optional[dict] = None) -> None:
    """
    Poll until the collection no longer has documents or queries,
    then drop the collection record + vectors.
    """
    logger.info("Post-check delete for collection %s", collection_id)
    max_retries = 30
    retry_delay = 10

    for attempt in range(max_retries):
        docs = loader_service_client_sync.get_documents(collection_id=collection_id, headers=headers)
        queries = get_queries_by_collection_id(collection_id, headers=headers)

        if not docs and not queries:
            _delete_collection(collection_id, headers=headers)
            logger.info("Post-check deletion completed for %s", collection_id)
            return

        logger.info(
            "Collection %s still has %d docs and %d queries — waiting (%d/%d)...",
            collection_id,
            len(docs),
            len(queries),
            attempt + 1,
            max_retries,
        )
        time.sleep(retry_delay)

    logger.warning(
        "Timeout while waiting for collection %s to be fully purged. Manual check may be required.",
        collection_id,
    )
