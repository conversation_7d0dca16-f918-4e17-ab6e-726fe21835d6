import logging

import dramatiq
from indexer_app.models.enums import DocumentStatusEnum, DocumentTypeEnum
from indexer_app.service_clients.loader_service_client.sync import (
    delete_url as delete_url_sync,
)
from indexer_app.service_clients.loader_service_client.sync import (
    get_collection_by_url_id,
    get_url,
    update_url,
)
from indexer_app.settings import settings
from indexer_app.tasks import QUEUE_PREFIX, core, rabbitmq_broker
from indexer_app.tasks.core import state_mapping

logger = logging.getLogger(__name__)


@dramatiq.actor(
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.indexer.url_move_queue",
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
)
def indexer_move_url_forward(url_id: str, event: str, **kwargs) -> None:
    """Dramatiq actor to move a URL item forward in the indexing process."""
    logger.info(f"🔄 IndexerService: Moving URL {url_id} forward with event: {event}")
    core.move_item_forward(
        item_id=url_id,
        event=event,
        state_mapping_func=state_mapping,
        state_mapping_args={"type": DocumentTypeEnum.URL.value},
        get_item_func=get_url,
        get_item_args={"item_id": url_id, "headers": kwargs.get("headers", {})},
        **kwargs,
    )


@dramatiq.actor(
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.indexer.url_loading_failed_queue",
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
)
def mark_url_as_failed(item_id: str) -> None:
    """Dramatiq actor to mark a URL item as failed."""
    core.mark_as_failed(
        item_id=item_id,
        update_item_func=update_url,
        update_item_args={
            "item_id": item_id,
            "status": DocumentStatusEnum.FAILED.value,
        },
    )


@dramatiq.actor(
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.indexer.url_embedding_queue",
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
)
def extract_embeddings_for_url(item_id: str, **kwargs) -> None:
    """Dramatiq actor to extract embeddings for a URL item."""
    logger.info(f"🚀 IndexerService: Starting embedding extraction for URL {item_id}")
    headers = kwargs.get("headers", {})
    core.extract_embeddings(
        item_id=item_id,
        get_collection_func=get_collection_by_url_id,
        get_collection_args={"item_id": item_id, "headers": headers},
        update_item_func=update_url,
        update_item_args={
            "item_id": item_id,
            "status": DocumentStatusEnum.EXTRACTING_EMBEDDINGS.value,
            "headers": headers,
        },
        state_mapping_func=state_mapping,
        state_mapping_args={"type": DocumentTypeEnum.URL.value, "headers": headers},
        get_item_func=get_url,
        get_item_args={"item_id": item_id, "headers": headers},
        **kwargs,
    )


@dramatiq.actor(
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.indexer.url_delete_queue",
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
)
def delete_url(item_id: str, **kwargs) -> None:
    """Dramatiq actor to delete a URL item."""
    headers = kwargs.get("headers", {})
    # fetch collection id for proper scoping
    if "collection_id" not in kwargs:
        col = get_collection_by_url_id(item_id, headers=headers) or {}
        kwargs["collection_id"] = col.get("id")

    core.delete_item(
        item_id=item_id,
        get_item_func=get_url,
        get_item_args={"item_id": item_id, "headers": headers},
        get_collection_func=get_collection_by_url_id,
        get_collection_args={"item_id": item_id, "headers": headers},
        delete_item_func=delete_url_sync,
        delete_item_args={
            "item_id": item_id,
            "collection_id": kwargs.get("collection_id"),
            "headers": headers,
        },
        **kwargs,
    )


@dramatiq.actor(
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.indexer.url_indexed_queue",
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
)
def mark_url_as_indexed(item_id: str, **kwargs) -> None:
    """Dramatiq actor to mark a URL item as indexed.

    Some Loader endpoints require the collection_id in the PATCH URL. We
    resolve the collection_id lazily to avoid passing it through every message
    hop.
    """

    headers = kwargs.get("headers", {})

    # Resolve collection ID if not provided
    collection_id = kwargs.get("collection_id")
    if collection_id is None:
        col = get_collection_by_url_id(item_id, headers=headers) or {}
        collection_id = col.get("id")

    update_args = {
        "item_id": item_id,
        "status": DocumentStatusEnum.INDEXED.value,
        "headers": headers,
    }
    if collection_id:
        update_args["collection_id"] = collection_id

    core.mark_as_indexed(
        item_id=item_id,
        update_item_func=update_url,
        update_item_args=update_args,
    )
