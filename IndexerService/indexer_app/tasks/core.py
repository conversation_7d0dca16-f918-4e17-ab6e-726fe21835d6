import logging
import pickle
from typing import Union

from dramatiq import get_broker
from indexer_app.core.hyde import <PERSON>
from indexer_app.core.semantic_search import SemanticSearch
from indexer_app.core.vectordb import VectorDB
from indexer_app.llm.models import (
    DEFAULT_EMBEDDING_MODEL,
    DEFAULT_LLM_MODEL,
    DEFAULT_SEARCH_METHOD,
)
from indexer_app.models.enums import (
    DocumentEventsEnum,
    DocumentStatusEnum,
    DocumentTypeEnum,
    SearchMethodsEnum,
)
from indexer_app.settings import settings
from indexer_app.tasks import redis_conn

# Optional: Elasticsearch client for lexical search
try:
    from elasticsearch import Elasticsearch, helpers  # type: ignore
except ImportError:  # ES is optional
    Elasticsearch = None  # type: ignore
    helpers = None  # type: ignore

logger = logging.getLogger(__name__)


def state_mapping(doc_type: Union[str, DocumentTypeEnum] = DocumentTypeEnum.DOCUMENT.value) -> dict[str, dict[str, str]]:
    """
    Mapping:  current_status  →  { event → <actor_name> }

    Only statuses that the Indexer can encounter are included.
    """
    doc_type = doc_type.value if isinstance(doc_type, DocumentTypeEnum) else doc_type

    return {
        DocumentStatusEnum.ADDED.value: {
            DocumentEventsEnum.DELETE_REQUEST.value: f"delete_{doc_type}",
        },
        DocumentStatusEnum.LOADING.value: {
            DocumentEventsEnum.DELETE_REQUEST.value: f"delete_{doc_type}",
        },
        DocumentStatusEnum.READY_TO_BE_INDEXED.value: {
            DocumentEventsEnum.EMBEDDING_REQUEST.value: f"extract_embeddings_for_{doc_type}",
            DocumentEventsEnum.EMBEDDING_FINISHED.value: f"mark_{doc_type}_as_indexed",
            DocumentEventsEnum.DELETE_REQUEST.value: f"delete_{doc_type}",
        },
        DocumentStatusEnum.EXTRACTING_EMBEDDINGS.value: {
            DocumentEventsEnum.EMBEDDING_FINISHED.value: f"mark_{doc_type}_as_indexed",
            DocumentEventsEnum.EMBEDDING_FAILED.value: f"mark_{doc_type}_as_failed",
            DocumentEventsEnum.DELETE_REQUEST.value: f"delete_{doc_type}",
        },
        DocumentStatusEnum.INDEXED.value: {
            DocumentEventsEnum.DELETE_REQUEST.value: f"delete_{doc_type}",
        },
        DocumentStatusEnum.FAILED.value: {
            DocumentEventsEnum.EMBEDDING_REQUEST.value: f"extract_embeddings_for_{doc_type}",
            DocumentEventsEnum.DELETE_REQUEST.value: f"delete_{doc_type}",
        },
        DocumentStatusEnum.DELETED.value: {},
    }


# --------------------------------------------------------------------------- #
# GENERIC "MOVE FORWARD"                                                      #
# --------------------------------------------------------------------------- #


def move_item_forward(
    item_id: str,
    event: str,
    state_mapping_func,
    state_mapping_args,
    get_item_func,
    get_item_args,
    **kwargs,
) -> None:
    """
    Generic state-machine driver.
    Works with the plain-dict returned by Loader's sync client.
    """
    item = get_item_func(**get_item_args)
    logger.debug("move_item_forward – loaded item: %s", item)

    if not item:
        return
    status = item["status"]
    try:
        logger.info(f"Moving item  {item_id=}  from  {status=}  to  {event=}")
        next_actor_name = state_mapping_func(state_mapping_args.get("type", "document"))[status][event]
    except KeyError:
        logger.exception("Invalid transition: %s --(%s)--> ?", status, event)
        return

    broker = get_broker()
    actor = broker.get_actor(next_actor_name)
    if not hasattr(actor, "send"):
        available_actors = [a.name for a in broker.get_declared_actors()]
        raise ValueError(f"Invalid actor name: '{next_actor_name}'\n" f"Available actors: {available_actors}")
    actor.send(item_id=item_id, **kwargs)
    logger.info(
        "Dispatched %s → %s (%s)",
        event,
        next_actor_name,
        item_id,
    )


# --------------------------------------------------------------------------- #
# EMBEDDING EXTRACTION                                                        #
# --------------------------------------------------------------------------- #


def extract_embeddings(
    item_id: str,
    get_collection_func,
    get_collection_args,
    update_item_func,
    update_item_args,
    state_mapping_func,
    state_mapping_args,
    get_item_func,
    get_item_args,
    **kwargs,
) -> None:
    """Actual embedding logic (same as before, just cleaned up)."""
    if redis_conn.get(f"cancel_task_{item_id}") == b"true":
        logger.warning("Embedding task cancelled – %s", item_id)
        return

    # — Collection meta -----------------------------------------------------
    collection = get_collection_func(**get_collection_args)
    if not collection:
        raise RuntimeError("Collection not found for %s", item_id)

    cfg = collection.get("custom_config", {})
    embedding_model = cfg.get("embedding_model", DEFAULT_EMBEDDING_MODEL)
    llm_model = cfg.get("llm_model", DEFAULT_LLM_MODEL)
    search_method = cfg.get("method", DEFAULT_SEARCH_METHOD)
    sys_prompt = cfg.get("system_prompt")
    collection_name = collection.get("name")
    logger.info("Extracting embeddings for collection %s", collection_name)
    # — Update status to EXTRACTING_EMBEDDINGS ------------------------------
    update_item_func(**update_item_args)

    # — Retrieve chunks ------------------------------------------------------
    logger.info(f"📦 Retrieving chunks from Redis for item {item_id}")
    raw = redis_conn.get(f"{item_id}_splits")
    if not raw:
        logger.error(f"❌ Chunks missing in Redis for item {item_id}")
        move_item_forward(
            item_id,
            event=DocumentEventsEnum.EMBEDDING_FAILED.value,
            state_mapping_func=state_mapping_func,
            state_mapping_args=state_mapping_args,
            get_item_func=get_item_func,
            get_item_args=get_item_args,
            **kwargs,
        )
        return

    splits = pickle.loads(raw)
    logger.info(f"✅ Retrieved {len(splits)} chunks from Redis for item {item_id}")

    # — Embed + upsert -------------------------------------------------------
    try:
        if search_method == SearchMethodsEnum.SEMANTIC_SEARCH.value:
            logger.info(f"🔍 Using SemanticSearch method for item {item_id}")
            ss = SemanticSearch(
                collection_name=collection_name,
                embedding_model_name=embedding_model,
            )
            logger.info(f"📊 Indexing {len(splits)} chunks using SemanticSearch for item {item_id}")
            ss.index_anonymized_chunks(item_id, splits)
            logger.info(f"✅ Successfully indexed chunks for item {item_id}")

            # ------------------------------------------------------------------
            # ➊  ALSO INDEX THE CHUNKS INTO ELASTICSEARCH (if available)
            # ------------------------------------------------------------------
            logger.info(f"*** {bool(Elasticsearch and settings.elastic_enabled)} – Elasticsearch available for {item_id}")
            if Elasticsearch and settings.elastic_enabled:
                try:
                    es = Elasticsearch(settings.elastic_url, request_timeout=10)

                    es_index = ss.vectordb.collection_name  # aligns with QueryService expectation

                    # Ensure index exists (mapping: document + metadata)
                    if not es.indices.exists(index=es_index):
                        es.indices.create(
                            index=es_index,
                            mappings={
                                "properties": {
                                    "document": {"type": "text"},
                                    "cmetadata": {"type": "object", "enabled": True},
                                }
                            },
                        )

                    actions = (
                        {
                            "_op_type": "index",
                            "_index": es_index,
                            "_id": f"{item_id}_{i}",
                            "document": split.page_content,
                            "cmetadata": split.metadata,
                        }
                        for i, split in enumerate(splits)
                    )

                    helpers.bulk(es, actions)
                    logger.info("Indexed %d chunks into Elasticsearch", len(splits))
                except Exception as es_exc:  # pragma: no cover
                    logger.warning("Elasticsearch indexing failed: %s", es_exc)

        elif search_method == SearchMethodsEnum.HYDE.value:
            hyde = Hyde(
                collection_name=collection_name,
                embedding_model_name=embedding_model,
                llm_model_name=llm_model,
                custom_system_prompt=sys_prompt,
            )
            hyde.index_anonymized_chunks(item_id, splits)

    except Exception as e:
        logger.exception("Embedding failed – %s", item_id)
        move_item_forward(
            item_id,
            event=DocumentEventsEnum.EMBEDDING_FAILED.value,
            state_mapping_func=state_mapping_func,
            state_mapping_args=state_mapping_args,
            get_item_func=get_item_func,
            get_item_args=get_item_args,
            **kwargs,
        )
        return

    # — Clean redis & signal success ----------------------------------------
    for key in redis_conn.scan_iter(f"{item_id}_splits*"):
        redis_conn.delete(key)

    move_item_forward(
        item_id,
        event=DocumentEventsEnum.EMBEDDING_FINISHED.value,
        state_mapping_func=state_mapping_func,
        state_mapping_args=state_mapping_args,
        get_item_func=get_item_func,
        get_item_args=get_item_args,
        **kwargs,
    )


# --------------------------------------------------------------------------- #
# SIMPLE HELPERS                                                              #
# --------------------------------------------------------------------------- #


def mark_as_indexed(item_id, update_item_func, update_item_args):
    """Mark an item as indexed."""
    logger.info("Marking %s as INDEXED", item_id)
    update_item_func(**update_item_args)


def mark_as_failed(item_id, update_item_func, update_item_args):
    """Mark an item as failed."""
    logger.info("Marking %s as FAILED", item_id)
    update_item_func(**update_item_args)


def delete_item(
    item_id,
    get_item_func,
    get_item_args,
    get_collection_func,
    get_collection_args,
    delete_item_func,
    delete_item_args,
    **kwargs,
):
    """Delete an item from the Loader DB and remove its embeddings from the vector DB."""
    if not get_item_func(**get_item_args):
        return

    collection = get_collection_func(**get_collection_args)
    if not collection:
        raise RuntimeError("Collection not found while deleting %s", item_id)

    # remove from vector DB (semantic_search)
    try:
        cfg = collection.get("custom_config", {})
        if cfg.get("method", DEFAULT_SEARCH_METHOD) == SearchMethodsEnum.SEMANTIC_SEARCH.value:
            ss = SemanticSearch(
                collection_name=collection.get("name"),
                embedding_model_name=cfg.get("embedding_model", DEFAULT_EMBEDDING_MODEL),
            )
            # delete all chunks with prefix "<doc_id>::"
            all_docs = ss.vectordb.get_documents()
            doc_ids_to_delete = [doc["id"] for doc in all_docs if doc["id"].startswith(f"{item_id}_")]
            for doc_id in doc_ids_to_delete:
                ss.vectordb.delete_document(doc_id)
                logger.info(f"Deleted document {doc_id} from vector DB")

        elif cfg.get("method", DEFAULT_SEARCH_METHOD) == SearchMethodsEnum.HYDE.value:
            hyde = Hyde(
                collection_name=collection.get("name"),
                embedding_model_name=cfg.get("embedding_model", DEFAULT_EMBEDDING_MODEL),
                llm_model_name=cfg.get("llm_model", DEFAULT_LLM_MODEL),
                custom_system_prompt=cfg.get("system_prompt"),
            )
            hyde_pgvector = VectorDB(hyde.vectordb.collection_name, hyde._embedding_model)
            hyde_item_ids = [doc["id"] for doc in hyde_pgvector.get_documents(document_id=item_id)]
            del hyde_pgvector

            for d_id in hyde_item_ids:
                if d_id.startswith(item_id):
                    hyde.vectordb.delete_document(d_id)
            del hyde, hyde_item_ids
    except Exception as e:
        logger.exception(f"Deleting item {item_id} from vector DB failed: {e}")

    # remove from Loader DB
    logger.info(f"Deleting item {item_id} from Loader DB")
    try:
        delete_item_func(**delete_item_args)
    except Exception as e:
        logger.exception(f"Failed to delete item {item_id} from Loader DB: {e}")

    logger.info(f"Deleted item {item_id} from vector DB and Loader DB")
