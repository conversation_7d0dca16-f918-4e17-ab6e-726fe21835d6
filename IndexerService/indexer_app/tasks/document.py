""" Document-related tasks for the Indexer service."""

import logging

import dramatiq
from dramatiq.middleware.current_message import CurrentMessage
from indexer_app.models.enums import DocumentStatusEnum, DocumentTypeEnum
from indexer_app.service_clients.loader_service_client.sync import (
    delete_document_by_document_id,
    get_collection_by_document_id,
    get_document,
    update_document,
)
from indexer_app.settings import settings
from indexer_app.tasks import QUEUE_PREFIX, core, rabbitmq_broker
from indexer_app.tasks.core import state_mapping
from indexer_app.utils.helpers import dramatiq_task_logger

logger = logging.getLogger(__name__)


# --------------------------------------------------------------------------- #
# 2.  “MOVE FORWARD” –  Main entry point                                      #
# --------------------------------------------------------------------------- #
@dramatiq.actor(
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.indexer.document_move_queue",
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
)
def indexer_move_document_forward(document_id: str, event: str, **kwargs) -> None:
    """
    Single *entry* for every event reaching Indexer – exactly like Loader
    has its own `loader_move_document_forward`.
    """
    msg = CurrentMessage.get_current_message()
    dramatiq_task_logger("indexer_move_document_forward", msg)
    core.move_item_forward(
        item_id=document_id,
        event=event,
        state_mapping_func=state_mapping,
        state_mapping_args={"type": DocumentTypeEnum.DOCUMENT.value},
        get_item_func=get_document,
        get_item_args={
            "document_id": document_id,
            "headers": kwargs.get("headers", {}),
        },
        **kwargs,
    )


# --------------------------------------------------------------------------- #
# 3.  ACTORS  (the ones referenced in the mapping above)                      #
# --------------------------------------------------------------------------- #


@dramatiq.actor(
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.indexer.document_embedding_queue",
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
)
def extract_embeddings_for_document(item_id: str, **kwargs) -> None:
    """Extract embeddings for a document.
    • Fetch the document & collection info from Loader
    • Pull the chunks from Redis (written by Loader)
    • Embed and upsert into pgvector (or whichever DB)
    • Emit EMBEDDING_FINISHED  /  EMBEDDING_FAILED
    """
    msg = CurrentMessage.get_current_message()
    dramatiq_task_logger("extract_embeddings_for_document", msg)
    headers = kwargs.get("headers", {})
    core.extract_embeddings(
        item_id=item_id,
        get_collection_func=get_collection_by_document_id,
        get_collection_args={"document_id": item_id, "headers": headers},
        update_item_func=update_document,
        update_item_args={
            "document_id": item_id,
            "status": DocumentStatusEnum.EXTRACTING_EMBEDDINGS.value,
            "headers": headers,
        },
        state_mapping_func=state_mapping,
        state_mapping_args={"type": DocumentTypeEnum.DOCUMENT.value},
        get_item_func=get_document,
        get_item_args={"document_id": item_id, "headers": headers},
        **kwargs,
    )


@dramatiq.actor(
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.indexer.document_indexed_queue",
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
)
def mark_document_as_indexed(item_id: str, **kwargs) -> None:
    """Mark the document as indexed in Loader."""
    msg = CurrentMessage.get_current_message()
    dramatiq_task_logger("mark_document_as_indexed", msg)

    core.mark_as_indexed(
        item_id=item_id,
        update_item_func=update_document,
        update_item_args={
            "document_id": item_id,
            "status": DocumentStatusEnum.INDEXED.value,
            "headers": kwargs.get("headers", {}),
        },
    )


@dramatiq.actor(
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.indexer.document_fail_queue",
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
)
def mark_document_as_failed(item_id: str, **kwargs) -> None:
    """Mark the document as failed in Loader."""
    msg = CurrentMessage.get_current_message()
    dramatiq_task_logger("mark_document_as_failed", msg)

    core.mark_as_failed(
        item_id=item_id,
        update_item_func=update_document,
        update_item_args={
            "document_id": item_id,
            "status": DocumentStatusEnum.FAILED.value,
            "headers": kwargs.get("headers", {}),
        },
    )


@dramatiq.actor(
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.indexer.document_delete_queue",
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
)
def delete_document(item_id: str, **kwargs) -> None:
    """
    Remove the document both from pgvector + Loader database.
    """
    msg = CurrentMessage.get_current_message()
    dramatiq_task_logger("delete_document", msg)

    core.delete_item(
        item_id=item_id,
        get_item_func=get_document,
        get_item_args={"document_id": item_id, "headers": kwargs.get("headers", {})},
        get_collection_func=get_collection_by_document_id,
        get_collection_args={
            "document_id": item_id,
            "headers": kwargs.get("headers", {}),
        },
        delete_item_func=delete_document_by_document_id,
        delete_item_args={"document_id": item_id, "headers": kwargs.get("headers", {})},
    )
