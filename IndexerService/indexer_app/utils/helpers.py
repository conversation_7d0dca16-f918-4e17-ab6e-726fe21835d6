import logging
from urllib.parse import urlparse

import redis
from indexer_app.settings import settings

logger = logging.getLogger(__name__)


def dramatiq_task_logger(task_name, rmq_message):
    """Log the details of a Dramatiq task."""
    logger.info(f"{task_name=} - Processing message_id={rmq_message.message_id}, queue={rmq_message.queue_name}, retried_so_far={rmq_message.options.get('retries', 0)}")


# Middleware to check for adminui requests
def extract_host_with_port_from_url(url: str) -> str:
    """
    Extract host:port from a URL.
    Example: "http://loader:8002" -> "loader:8002"
    """
    parsed = urlparse(url)
    return f"{parsed.hostname}:{parsed.port}"


def get_allowed_admin_hosts() -> set:
    """
    Extract host:port from the URLs in settings.
    Example: "http://loader:8002" -> "loader:8002"
    """
    return {
        extract_host_with_port_from_url(settings.indexer_base_url),
        extract_host_with_port_from_url(settings.loader_base_url),
        extract_host_with_port_from_url(settings.query_base_url),
    }


def delete_session_chat_history_redis_keys(collection_name: str):
    """Delete Redis keys for chat history associated with a specific collection."""
    try:
        redis_chat_history_conn = redis.from_url(settings.redis_query_history_url)

        # Key prefix pattern
        key_prefix = f"chat_history:{collection_name}:*"
        matching_keys = redis_chat_history_conn.keys(key_prefix)

        if matching_keys:
            redis_chat_history_conn.delete(*matching_keys)
            logger.info(f"delete_session_chat_history_redis_keys - Deleted Redis keys for session {matching_keys}")
        else:
            logger.info(f"delete_session_chat_history_redis_keys - No Redis keys found for with prefix {key_prefix}")

    except Exception as e:
        logger.error(f"Error in delete_session_chat_history_redis_keys: {str(e)}", exc_info=e)
