from enum import Enum


class OPENAI_EMBEDDING_MODELS(str, Enum):
    """Enum for embedding models."""

    ADA002 = "text-embedding-ada-002"


class OPENAI_MODELS(str, Enum):
    """Enum for OpenAI models."""

    GPT4 = "gpt-4"
    GPT4O2 = "gpt-4o-2"
    GPT4O = "gpt-4o"
    GPT4OMINI = "gpt-4o-mini"


class OS_MODELS(str, Enum):
    """Enum for Open Source models."""

    LLAMA3170BINSTRUCT = "Meta-Llama-3.1-70B-Instruct"
    MIXTRAL8B = "Mistral-8B"
    PHI4 = "Phi-4"


class OS_EMBEDDING_MODELS(str, Enum):
    """Enum for Open-source embedding-only models."""

    BGEM3 = "bge-m3"
