from enum import Enum


class DocumentTypeEnum(str, Enum):
    """Enum for different types of documents that can be indexed."""

    DOCUMENT = "document"
    URL = "url"


class DocumentStatusEnum(str, Enum):
    """Enum for different statuses of a document during the indexing process."""

    ADDED = "added"
    LOADING = "loading"
    READY_TO_BE_INDEXED = "ready_to_be_indexed"
    EXTRACTING_EMBEDDINGS = "extracting_embeddings"
    INDEXED = "indexed"
    FAILED = "failed"
    DELETED = "deleted"


class DocumentEventsEnum(str, Enum):
    """Enum for different events that can occur during the document processing lifecycle."""

    DELETE_REQUEST = "delete_request"
    LOAD_REQUEST = "loading_request"
    LOAD_FINISHED = "loading_finished"
    LOAD_FAILED = "loading_failed"
    EMBEDDING_REQUEST = "embedding_request"
    EMBEDDING_FINISHED = "embedding_finished"
    EMBEDDING_FAILED = "embedding_failed"


class LoadMethodEnum(str, Enum):
    """Enum for different document loading methods."""

    USE_TESSERACT = "use_tesseract"
    USE_LANGCHAIN_AND_TESSERACT = "use_langchain_and_tesseract"
    USE_LANGCHAIN = "use_langchain"
    USE_GPT_VISION = "use_gpt_vision"


class SearchMethodsEnum(str, Enum):
    """Enum for different search methods used in the application."""

    SEMANTIC_SEARCH = "semantic_search"
    HYDE = "hyde"
