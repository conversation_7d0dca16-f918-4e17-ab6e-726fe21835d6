from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field


class CollectionResponse(BaseModel):
    """Response model for collection operations."""

    message: Optional[str]


class DocumentResponse(BaseModel):
    """Response model for document operations."""

    message: Optional[str]


class ChunkIn(BaseModel):
    """Payload used to insert a new chunk."""

    document_id: UUID = Field(..., example="doc_42_0")
    content: str = Field(..., example="Chunk text …")
    metadata: dict = Field(default_factory=dict, example={"author": "alice"})


class ChunkUpdate(BaseModel):
    """Patch a chunk’s content and/or metadata."""

    content: Optional[str] = None
    metadata: Optional[dict] = None


class SearchHit(BaseModel):
    """Search result row."""

    content: str
    metadata: dict
    score: float


class EvaluationResponse(BaseModel):
    """Response model for evaluation operations."""

    status: Optional[str] = Field(None, description="status")
