"""Database module."""

import logging

from indexer_app.settings import settings
from sqlalchemy import MetaData, create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

logger = logging.getLogger(__name__)

Base = declarative_base()
metadata = MetaData()
engine = create_engine(settings.db_url, pool_pre_ping=True)
DBSession = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def check_and_create_pgvector_extension():
    """Check if pgvector extension exists, and create it if not."""
    with DBSession() as session:
        # pgvector existence check
        result = session.execute(text("SELECT EXISTS(SELECT * FROM pg_extension WHERE extname = 'vector');"))
        exists = result.fetchone()[0]

        if not exists:
            # pgvector creation
            session.execute(text("CREATE EXTENSION vector;"))
            session.commit()
            logger.info("pgvector extension has been created.")


try:
    check_and_create_pgvector_extension()
except Exception as e:
    logger.warning(f"unable to create pgvector extension! {e}")
