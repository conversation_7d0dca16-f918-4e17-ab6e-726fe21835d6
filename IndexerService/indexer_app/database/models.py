from indexer_app.database import Base
from indexer_app.database.utils import generate_uuid
from sqlalchemy import JSO<PERSON>, Column, DateTime, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func


class ReplicaInfo(Base):
    """Model - ReplicaInfo."""

    __tablename__ = "replicas"

    id = Column(UUID(as_uuid=True), default=generate_uuid, unique=True, nullable=False)
    name = Column(String, primary_key=True)
    collection_ids = Column(JSON)
    created_at = Column(DateTime, default=func.now())
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())

    def to_dict(self):
        """Convert ReplicaInfo model to dictionary."""
        return {
            "id": str(self.id),
            "name": self.name,
            "collection_ids": self.collection_ids if self.collection_ids else {},
            "created_at": self.created_at.isoformat(),
            "last_updated": self.last_updated.isoformat(),
        }
