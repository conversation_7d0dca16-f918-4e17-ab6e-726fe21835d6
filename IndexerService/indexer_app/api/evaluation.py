from __future__ import annotations

import logging
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from indexer_app.core.auth import get_auth
from indexer_app.database.session import get_db
from indexer_app.models.request import EvaluationRequest
from indexer_app.models.response import EvaluationResponse
from indexer_app.service_clients.loader_service_client.asynchronous import (
    get_collection,
)
from indexer_app.tasks.evaluation import run_rag_evaluation
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

router = APIRouter()


async def _verify_collection_existance(collection_id=None, collection_name=None, headers=None):
    if collection_name:
        collection = await get_collection(collection_name=collection_name, headers=headers)
    else:
        collection = await get_collection(collection_id=collection_id, headers=headers)

    if not collection:
        raise HTTPException(status_code=404, detail="Collection not found")
    return collection


def _create_headers(auth):
    """Create headers for the request based on authentication."""
    if isinstance(auth, dict):
        local_dev = auth.get("user") == "developer" and auth.get("role") == "dev_local"
        headers = {"X-Admin-UI": "true"} if local_dev else {}
        return headers
    elif not isinstance(auth, dict) and not auth.access_token:
        raise HTTPException(status_code=401, detail="Unauthorized")
    else:
        return {"Authorization": f"Bearer {auth.access_token}"}


@router.post("/collection/{collection_id}/evaluate", response_model=EvaluationResponse)
async def api_post_evaluate(
    collection_id: UUID,
    evaluation_request: EvaluationRequest,
    db_session: Session = Depends(get_db),
    auth=Depends(get_auth),
) -> JSONResponse:
    """Evaluation endpoint.

    Args:
        collection_id: collection identifier.
        evaluation_request: evaluation data.
        auth: Azure AD authentication.
        db_session: database session.

    Returns:
        JSONResponse: status.
    """
    headers = _create_headers(auth)
    await _verify_collection_existance(collection_id=collection_id, headers=headers)

    logger.info(f"Running rag evaluation({evaluation_request.run_name}) for {len(evaluation_request.questions)} questions")
    # run in the background
    run_rag_evaluation.send(
        collection_id,
        evaluation_request.run_name,
        evaluation_request.questions,
        headers,
    )

    response = EvaluationResponse(status="ok")
    return JSONResponse(content=jsonable_encoder(response), status_code=200)
