import logging

import requests
from fastapi import <PERSON><PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.responses import RedirectResponse
from indexer_app.settings import settings

logger = logging.getLogger(__name__)

router = APIRouter()


# Azure AD login
@router.get("/login-url")
async def login_url():
    """Generate the Azure AD login URL for user authentication."""
    _login_url = (
        f"{settings.openapi_authorization_url}"
        f"?client_id={settings.app_client_id}"
        f"&response_type=code"
        f"&redirect_uri={settings.client_ui_url}/api/query/callback"
        f"&response_mode=query"
        f"&scope=openid%20profile%20email%20api://{settings.app_client_id}/user_impersonation"
        f"&state=12345"
    )

    return {"login_url": _login_url}


# Azure AD callback
@router.get("/callback")
async def callback(request: Request):
    """Handle the callback from Azure AD after user authentication."""
    code = request.query_params.get("code")
    if not code:
        raise HTTPException(status_code=400, detail="Authorization code not found")

    data = {
        "grant_type": "authorization_code",
        "client_id": settings.app_client_id,
        "client_secret": settings.app_client_secret,
        "code": code,
        "redirect_uri": f"{settings.client_ui_url}/api/query/callback",
        "scope": settings.scope_name,
    }

    response = requests.post(settings.openapi_token_url, data=data)

    if response.status_code == 200:
        access_token = response.json().get("access_token")
        if access_token:
            # Redirect to frontend with access token
            return RedirectResponse(url=f"{settings.client_ui_url}?access_token={access_token}")
        else:
            raise HTTPException(status_code=400, detail="Access token not found in response")
    else:
        raise HTTPException(status_code=response.status_code, detail="Failed to acquire token")


# Azure AD logout
@router.get("/logout")
async def logout():
    """
    Logout endpoint that logs out the user from Azure AD and redirects to the UI.
    """
    logout_url = f"https://login.microsoftonline.com/{settings.tenant_id}/oauth2/v2.0/logout" f"?post_logout_redirect_uri={settings.client_ui_url}"
    return RedirectResponse(url=logout_url)
