import logging
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from indexer_app.core.auth import get_auth
from indexer_app.models.response import CollectionResponse
from indexer_app.service_clients.loader_service_client.asynchronous import (
    get_collection,
)
from indexer_app.tasks.collection import delete_collection

logger = logging.getLogger(__name__)

router = APIRouter()


async def _verify_collection_existance(collection_id=None, collection_name=None, headers=None):
    if collection_name:
        collection = await get_collection(collection_name=collection_name, headers=headers)
    else:
        collection = await get_collection(collection_id=collection_id, headers=headers)

    if not collection:
        raise HTTPException(status_code=404, detail="Collection not found")
    return collection


def _create_headers(auth):
    if isinstance(auth, dict):
        is_admin = auth.get("user") == "admin" and auth.get("role") == "admin_ui"
        local_dev = auth.get("user") == "developer" and auth.get("role") == "dev_local"
        headers = {"X-Admin-UI": "true"} if is_admin or local_dev else {}
        return headers
    elif not isinstance(auth, dict) and not auth.access_token:
        raise HTTPException(status_code=401, detail="Unauthorized")
    else:
        return {"Authorization": f"Bearer {auth.access_token}"}


@router.delete("/collection/{collection_id}", response_model=CollectionResponse)
async def api_delete_collection(collection_id: UUID, auth=Depends(get_auth)) -> JSONResponse:
    """Delete collection by asking LoaderService."""

    headers = _create_headers(auth)
    collection_data = await _verify_collection_existance(collection_id=collection_id, headers=headers)

    if not collection_data:
        response = CollectionResponse(message="not found")
        return JSONResponse(content=jsonable_encoder(response), status_code=404)
    delete_collection.send(collection_id=str(collection_id), **{"headers": headers})

    response = CollectionResponse(message="ok")
    return JSONResponse(content=jsonable_encoder(response), status_code=200)
