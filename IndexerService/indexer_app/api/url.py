"""API endpoints for document management in the Indexer Service."""

import logging
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from indexer_app.core.auth import get_auth
from indexer_app.database.session import get_db
from indexer_app.models.enums import DocumentEventsEnum
from indexer_app.models.response import DocumentResponse
from indexer_app.service_clients.loader_service_client.asynchronous import (
    get_collection,
)
from indexer_app.service_clients.loader_service_client.sync import get_url
from indexer_app.tasks.url import indexer_move_url_forward
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)
router = APIRouter()


async def _verify_collection_existance(collection_id=None, collection_name=None, headers=None):
    """Verify if the collection exists."""
    if collection_name:
        collection = await get_collection(collection_name=collection_name, headers=headers)
    else:
        collection = await get_collection(collection_id=collection_id, headers=headers)

    if not collection:
        raise HTTPException(status_code=404, detail="Collection not found")
    return collection


def _create_headers(auth):
    """Create headers for the request based on authentication."""
    if isinstance(auth, dict):
        is_admin = auth.get("user") == "admin" and auth.get("role") == "admin_ui"
        local_dev = auth.get("user") == "developer" and auth.get("role") == "dev_local"
        headers = {"X-Admin-UI": "true"} if is_admin or local_dev else {}
        return headers
    elif not isinstance(auth, dict) and not auth.access_token:
        raise HTTPException(status_code=401, detail="Unauthorized")
    else:
        return {"Authorization": f"Bearer {auth.access_token}"}


@router.delete(
    "/collection/{collection_id}/url/{url_id}",
    response_model=DocumentResponse,
)
async def api_delete_url(
    collection_id: UUID,
    url_id: UUID,
    db_session: Session = Depends(get_db),
    auth=Depends(get_auth),
) -> JSONResponse:
    """Delete a URL in a specific collection.

    Args:
        collection_id: collection identifier (uuid).
        url_id: URL identifier (uuid).
        auth: Azure AD authentication.
        db_session: database session.

    Returns:
        JSONResponse: status message.
    """

    headers = _create_headers(auth)
    await _verify_collection_existance(collection_id=collection_id, headers=headers)

    try:
        url = get_url(item_id=str(url_id), collection_id=collection_id, headers=headers)
    except ValueError:
        return JSONResponse(
            content=jsonable_encoder(DocumentResponse(message="Invalid URL ID")),
            status_code=400,
        )

    if not url:
        response = DocumentResponse(message="not found")
        return JSONResponse(content=jsonable_encoder(response), status_code=404)

    try:
        indexer_move_url_forward.send(
            str(url_id),
            DocumentEventsEnum.DELETE_REQUEST.value,
            **{"headers": headers},
        )
    except Exception as e:
        logger.error(
            f"Failed to move URL {url_id} forward to {DocumentEventsEnum.DELETE_REQUEST.value}",
            exc_info=e,
        )
        return JSONResponse(
            content=jsonable_encoder(DocumentResponse(message="Internal server error")),
            status_code=500,
        )

    response = DocumentResponse(message="ok")
    return JSONResponse(content=jsonable_encoder(response), status_code=200)
