from __future__ import annotations

import logging
from typing import Generic, List, Optional, Sequence, TypeVar
from uuid import UUID

from fastapi import APIRouter, Body, Depends, HTTPException, Query, status
from fastapi.responses import J<PERSON>NResponse
from indexer_app.core.auth import get_auth
from indexer_app.core.vectordb import VectorD<PERSON>
from indexer_app.database.session import db_context
from indexer_app.llm.models import MODEL_PRESETS
from indexer_app.models.request import RenameCollectionRequest
from indexer_app.models.response import ChunkIn, ChunkUpdate, SearchHit
from indexer_app.service_clients.loader_service_client.asynchronous import (
    get_collection,
)
from langchain_community.vectorstores.pgembedding import CollectionStore
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter()
T = TypeVar("T")


def _resolve_embedding(name: str) -> object:
    """Return the LangChain embedding model picked from MODEL_PRESETS."""
    try:
        return MODEL_PRESETS[name].embedding_model
    except KeyError as exc:
        raise HTTPException(
            status_code=400,
            detail=f"Unknown embedding_model '{name}'. " f"Allowed: {list(MODEL_PRESETS)}",
        ) from exc


def _create_headers(auth):
    """Create headers for the request based on authentication."""
    if isinstance(auth, dict):
        local_dev = auth.get("user") == "developer" and auth.get("role") == "dev_local"
        headers = {"X-Admin-UI": "true"} if local_dev else {}
        return headers
    elif not isinstance(auth, dict) and not auth.access_token:
        raise HTTPException(status_code=401, detail="Unauthorized")
    else:
        return {"Authorization": f"Bearer {auth.access_token}"}


def _vdb(
    collection_name: str,
    embed_name: str,
    use_jsonb: bool = True,
    search_method: str = "similarity",
    k: int = 4,
) -> VectorDB:
    """Instantiate a ready-to-use VectorDB wrapper."""
    return VectorDB(collection_name=collection_name, embedding_model=_resolve_embedding(embed_name), use_jsonb=use_jsonb, search_method=search_method, top_k=k)


async def _initialize_vdb(
    collection_id: UUID,
    headers: Optional[dict] = None,
    search_type: str = "semantic_search",
    use_jsonb=True,
    search_method="similarity",
    k: int = 4,
) -> VectorDB:
    """Initialize the VectorDB wrapper using the collection_id."""
    collection_data = await get_collection(collection_id, headers=headers)
    if not collection_data:
        raise HTTPException(status_code=404, detail="Collection not found")

    collection_name = f"{collection_data.get('name')}_{search_type}"
    cfg = collection_data.get("custom_config", {})
    embedding_model = cfg.get("embedding_model")

    return _vdb(collection_name, embedding_model, use_jsonb, search_method, k)


class Page(BaseModel, Generic[T]):
    """Generic pagination envelope used when `pagination_on=true`."""

    page: int
    page_size: int
    total: int
    items: Sequence[T]


def paginate(
    items: List[T],
    pagination_on: bool,
    page: int,
    page_size: int,
) -> JSONResponse:
    """Return either the full list or a paginated slice."""
    if not pagination_on:
        return JSONResponse(items)

    start = (page - 1) * page_size
    end = start + page_size
    payload = Page[T](
        page=page,
        page_size=page_size,
        total=len(items),
        items=items[start:end],
    )
    return JSONResponse({"items": payload})


@router.get("/embedding/item_count", summary="Total chunk count across *all* collections")
async def total_items(collection_id: UUID, search_type: str = "semantic_search", auth=Depends(get_auth)):
    """
    Returns a single integer: the amount of chunks stored in the pgvector table,
    regardless of collection.
    """
    headers = _create_headers(auth=auth)
    vdb = await _initialize_vdb(collection_id, headers=headers, search_type=search_type)
    return JSONResponse({"total_items": vdb.get_item_count()})


@router.get("/embedding/collections", summary="List every collection")
async def list_collections(collection_id: UUID, search_type: str = "semantic_search", auth=Depends(get_auth)):
    """Return minimal metadata for every pgvector collection."""
    headers = _create_headers(auth=auth)
    vdb = await _initialize_vdb(collection_id, headers=headers, search_type=search_type)
    return JSONResponse(vdb.get_collections())


@router.get("/embedding/collection/{collection_id}")
async def collection_info(collection_id: UUID, search_type: str = "semantic_search", auth=Depends(get_auth)):
    """Return metadata + chunk count for the selected collection."""
    headers = _create_headers(auth=auth)
    vdb = await _initialize_vdb(collection_id, headers=headers, search_type=search_type)
    coll = vdb.get_collection()

    if not coll:
        raise HTTPException(404, "Collection not found")

    payload = {
        "name": coll.name,
        "metadata": coll.cmetadata,
        "chunk_count": vdb._get_collection_count(),
    }
    return JSONResponse(payload)


@router.get("/embedding/collection/{collection_id}/documents/embed")
async def list_chunks_with_vectors(
    collection_id: UUID,
    document_ids: Optional[List[str]] = Query(None),
    pagination_on: bool = Query(False),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, le=200),
    search_type: str = "semantic_search",
    auth=Depends(get_auth),
):
    """Return chunks **with** their vectors (heavy payload)."""
    headers = _create_headers(auth=auth)
    vdb = await _initialize_vdb(collection_id, headers=headers, search_type=search_type)
    docs = vdb.get_documents_with_embeddings(document_ids=document_ids, stringfy=True, filter_by_collection=True)
    return paginate(docs, pagination_on, page, page_size)


@router.get("/embedding/collection/{collection_id}/search")
async def search(
    collection_id: UUID,
    query: str,
    method: str = Query(
        "similarity",
        enum=["similarity", "mmr", "keyword", "hybrid"],
        description="Search strategy",
    ),
    k: int = Query(4, gt=1, le=50),
    search_type: str = "semantic_search",
    auth=Depends(get_auth),
):
    """Search the vector store using the requested strategy."""
    headers = _create_headers(auth=auth)
    vdb = await _initialize_vdb(collection_id, headers=headers, search_type=search_type, search_method=method)
    hits = vdb.search(query, k=k)
    payload = [SearchHit(content=d.page_content, metadata=d.metadata, score=s).model_dump() for d, s in hits]
    return JSONResponse(payload)


@router.get("/embedding/collection/{collection_id}/relevant")
async def relevant_docs(
    collection_id: UUID,
    query: str,
    k: int = Query(4, gt=1, le=50),
    search_type: str = "semantic_search",
    auth=Depends(get_auth),
):
    """Shortcut wrapper around `VectorDB.get_relevant_documents`."""
    headers = _create_headers(auth=auth)
    vdb = await _initialize_vdb(collection_id, headers=headers, search_type=search_type, k=k)
    docs = vdb.get_relevant_documents(query, k=k)
    return JSONResponse([{"content": d.page_content, "metadata": d.metadata} for d in docs])


# ── chunk CRUD ───────────────────────────────────────────────────────
@router.post(
    "/embedding/collection/{collection_id}/document",
    status_code=status.HTTP_201_CREATED,
    summary="Insert a single chunk",
)
async def insert_chunk(
    collection_id: UUID,
    chunk: ChunkIn,
    search_type: str = "semantic_search",
    auth=Depends(get_auth),
):
    """Insert a single chunk into the collection."""
    headers = _create_headers(auth=auth)
    vdb = await _initialize_vdb(collection_id, headers=headers, search_type=search_type)
    vdb.add_document(chunk.document_id, chunk.content, chunk.metadata)
    return JSONResponse({"status": "inserted"})


@router.post(
    "/embedding/collection/{collection_id}/documents/batch",
    status_code=status.HTTP_201_CREATED,
    summary="Insert several chunks in one call",
)
async def insert_chunk_batch(
    collection_id: UUID,
    chunks: List[ChunkIn] = Body(...),
    search_type: str = "semantic_search",
    auth=Depends(get_auth),
):
    """Insert several chunks into the collection in one call."""
    headers = _create_headers(auth=auth)
    vdb = await _initialize_vdb(collection_id, headers=headers, search_type=search_type)
    for c in chunks:
        vdb.add_document(c.document_id, c.content, c.metadata)
    return JSONResponse({"status": f"inserted {len(chunks)} chunks"})


@router.patch(
    "/embedding/collection/{collection_id}/document/{document_id}",
    summary="Update chunk content and/or metadata",
)
async def update_chunk(
    collection_id: UUID,
    document_id: UUID,
    upd: ChunkUpdate = Body(...),
    search_type: str = "semantic_search",
    auth=Depends(get_auth),
):
    """Update a chunk's content and/or metadata."""
    if upd.content is None and upd.metadata is None:
        raise HTTPException(400, "Nothing to update")

    headers = _create_headers(auth=auth)
    vdb = await _initialize_vdb(collection_id, headers=headers, search_type=search_type)
    current = vdb.get_documents(document_id=document_id)
    if not current:
        raise HTTPException(404, "Document not found")

    base = current[0]
    new_content = upd.content or base["document"]
    new_meta = {**base["cmetadata"], **(upd.metadata or {})}
    vdb.update_document(document_id, new_content, new_meta)
    return JSONResponse({"status": "updated"})


@router.delete(
    "/embedding/collection/{collection_id}/document/{document_id}",
    summary="Delete a chunk (all its vectors)",
)
async def delete_chunk(
    collection_id: UUID,
    document_id: UUID,
    search_type: str = "semantic_search",
    auth=Depends(get_auth),
):
    """Delete a chunk by its ID."""
    headers = _create_headers(auth=auth)
    vdb = await _initialize_vdb(collection_id, headers=headers, search_type=search_type)
    vdb.delete_document(document_id)
    return JSONResponse({"status": "deleted"})


@router.put("/embedding/collection/{collection_id}/rename", summary="Rename a collection")
async def rename_collection(
    collection_id: str,
    req: RenameCollectionRequest,
    auth=Depends(get_auth),
):
    """Rename an existing collection."""
    headers = _create_headers(auth=auth)
    collection_data = await get_collection(collection_id=collection_id, headers=headers)

    if not collection_data:
        raise HTTPException(status_code=404, detail="Collection not found")

    method = collection_data.get("custom_config", {}).get("method", "semantic_search")
    old_name = f"{collection_data['name']}_{method}"
    new_name = f"{req.new_name}_{method}"

    with db_context() as session:
        coll = session.query(CollectionStore).filter_by(name=old_name).first()
        if not coll:
            return JSONResponse({"status": "not in db", "message": "Collection not found in database"})
        if old_name != new_name:
            return JSONResponse({"status": "not in db", "message": "Collection name is the same"})

        # check for name conflict
        name_conflict = session.query(CollectionStore).filter_by(name=new_name).first()
        if name_conflict:
            raise HTTPException(409, f"Collection with name '{new_name}' already exists.")

        coll.name = new_name
        session.commit()

    return JSONResponse({"status": "renamed", "old_name": old_name, "new_name": new_name})
