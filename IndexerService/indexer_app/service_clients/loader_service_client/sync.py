"""Client module for synchronous communication with the Loader service API."""

import logging
from typing import Dict, Optional
from uuid import UUID

import requests
from indexer_app.settings import settings

logger = logging.getLogger(__name__)


def get_collection(
    collection_id: Optional[UUID] = None,
    collection_name: Optional[str] = None,
    headers: Optional[Dict] = None,
) -> Optional[dict]:
    """Retrieve a collection by its ID or name."""
    if collection_id:
        url = f"{settings.loader_base_url}/collection?collection_id={collection_id}"
    elif collection_name:
        url = f"{settings.loader_base_url}/collection?collection_name={collection_name}"
    else:
        logger.error("Either collection_id or collection_name must be provided.")
        return None

    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            return response.json()
    except requests.RequestException:
        logger.error(f"Error in get_collection: {collection_id or collection_name}", exc_info=True)
    return {}


def get_document(document_id: UUID, collection_id: Optional[UUID] = None, headers: Optional[Dict] = None) -> Optional[dict]:
    """Fetch a document by ID, optionally providing the collection ID."""
    if not collection_id:
        try:
            url = f"{settings.loader_base_url}/document/{document_id}"
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                return response.json().get("document", {})
        except requests.RequestException:
            return None
        return None

    try:
        url = f"{settings.loader_base_url}/collection/{collection_id}/document/{document_id}"
        response = requests.get(url)
        if response.status_code == 200:
            return response.json().get("document", {})
    except requests.RequestException:
        return None
    return None


def delete_collection(collection_id: UUID, headers: Optional[dict] = None) -> bool:
    """Delete a collection by ID."""
    url = f"{settings.loader_base_url}/collection/{collection_id}"
    try:
        response = requests.delete(url, headers=headers)
        return response.status_code == 200
    except requests.RequestException:
        return False


def delete_urls_by_collection(collection_id: UUID, headers: Optional[dict] = None) -> bool:
    """Delete all URLs associated with a given collection."""
    url = f"{settings.loader_base_url}/collection/{collection_id}/urls"
    try:
        response = requests.delete(url, headers=headers)
        return response.status_code == 200
    except requests.RequestException:
        return False


def delete_url(item_id: str, collection_id: Optional[UUID] = None, headers: Optional[dict] = None) -> bool:
    """Delete a single URL by ID, optionally scoping by collection_id to satisfy RBAC."""

    if collection_id:
        url = f"{settings.loader_base_url}/collection/{collection_id}/url/{item_id}"
    else:
        url = f"{settings.loader_base_url}/url/{item_id}"

    try:
        response = requests.delete(url, headers=headers)
        return response.status_code == 200
    except requests.RequestException:
        return False


def update_document(
    document_id: UUID,
    collection_id: Optional[UUID] = None,
    file_path: Optional[str] = None,
    status: Optional[str] = None,
    document_metadata: Optional[Dict] = None,
    headers: Optional[Dict] = None,
) -> bool:
    """Update document metadata or status."""
    url = f"{settings.loader_base_url}/document/{document_id}"
    payload = {
        "collection_id": collection_id,
        "file_path": file_path,
        "status": status,
        "document_metadata": document_metadata,
    }
    data = {k: v for k, v in payload.items() if v is not None}

    try:
        response = requests.patch(url, json=data, headers=headers)
        return response.status_code == 200
    except requests.RequestException:
        logger.error(f"Error in update_document: {document_id}", exc_info=True)
    return False


def get_collection_by_document_id(document_id: UUID, headers: Optional[Dict] = None) -> Optional[dict]:
    """Get the collection associated with a given document."""
    url = f"{settings.loader_base_url}/document/{document_id}/collection"
    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            return response.json()
    except requests.RequestException:
        logger.error(f"Error in get_collection_by_document_id: {document_id}", exc_info=True)
    return {}


def delete_document_by_document_id(
    document_id: UUID,
    collection_id: Optional[UUID] = None,
    headers: Optional[Dict] = None,
) -> bool:
    """Delete a document by its ID (with optional collection ID)."""
    if collection_id:
        url = f"{settings.loader_base_url}/document/{document_id}?collection_id={collection_id}"
    else:
        url = f"{settings.loader_base_url}/document/{document_id}"

    try:
        response = requests.delete(url, headers=headers)
        return response.status_code == 200
    except requests.RequestException:
        logger.error(f"error in delete_document_by_document_id: {document_id}", exc_info=True)

    return False


def get_documents(collection_id: UUID, headers: Optional[Dict] = None) -> Optional[list]:
    """Get all documents associated with a collection."""
    url = f"{settings.loader_base_url}/collection/{collection_id}/documents"
    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            return response.json().get("documents", [])
    except requests.RequestException:
        logger.error("get_documents-response error", exc_info=True)
    return []


def get_url(
    item_id: str,
    collection_id: Optional[UUID] = None,
    collection_name: Optional[str] = None,
    headers: Optional[Dict] = None,
) -> Optional[dict]:
    """Get a URL item by ID, and optionally collection ID or name."""
    if collection_id:
        url = f"{settings.loader_base_url}/collection/{collection_id}/url/{item_id}"
    elif collection_name:
        url = f"{settings.loader_base_url}/collection/by_name/{collection_name}/url/{item_id}"
    else:
        url = f"{settings.loader_base_url}/url/{item_id}"

    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            return response.json()
    except requests.RequestException:
        logger.error(f"Error in get_url: {item_id}", exc_info=True)
    return {}


def update_url(
    item_id: str,
    collection_id: Optional[UUID] = None,
    status: Optional[str] = None,
    content: Optional[str] = None,
    headers: Optional[Dict] = None,
) -> bool:
    """Update the status or content of a URL item."""
    if collection_id:
        url = f"{settings.loader_base_url}/collection/{collection_id}/url/{item_id}"
    else:
        url = f"{settings.loader_base_url}/url/{item_id}"
    payload = {}
    if status is not None:
        payload["status"] = status
    if content is not None:
        payload["content"] = content

    try:
        response = requests.patch(url, json=payload, headers=headers)
        return response.status_code == 200
    except requests.RequestException:
        logger.error(f"Error in update_url: {item_id}", exc_info=True)
    return False


def get_collection_by_url_id(item_id: str, headers: Optional[Dict] = None) -> Optional[dict]:
    """Retrieve the collection associated with a given URL ID."""
    url = f"{settings.loader_base_url}/url/{item_id}/collection"
    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            return response.json()
    except requests.RequestException:
        logger.error(f"Error in get_collection_by_url_id: {item_id}", exc_info=True)
    return {}
