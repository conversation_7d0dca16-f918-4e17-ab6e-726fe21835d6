"""Async client functions for interacting with the LoaderService API."""

from typing import Optional, Union
from uuid import UUID

import httpx
from indexer_app.settings import settings


async def get_collection(
    collection_id: Optional[Union[UUID, str]] = None,
    collection_name: Optional[str] = None,
    headers: Optional[dict] = None,
) -> Optional[dict]:
    """Fetch collection details by ID or name from the LoaderService.

    Args:
        collection_id (Optional[UUID | str]): The UUID or string ID of the collection.
        collection_name (Optional[str]): The name of the collection.
        headers (Optional[dict]): Optional headers to include in the request (e.g., auth).

    Returns:
        Optional[dict]: The collection object if found, otherwise None.
    """
    if collection_id:
        url = f"{settings.loader_base_url}/collection?collection_id={collection_id}"
    elif collection_name:
        url = f"{settings.loader_base_url}/collection?collection_name={collection_name}"
    else:
        return None

    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, headers=headers)
            if response.status_code == 200:
                return response.json().get("collection", response.json())
        except httpx.HTTPError:
            return None

    return None


async def get_document(
    document_id: UUID,
    collection_id: UUID,
    headers: Optional[dict] = None,
) -> Optional[dict]:
    """Fetch a document by its ID and collection ID from the LoaderService.

    Args:
        document_id (UUID): The ID of the document.
        collection_id (UUID): The ID of the collection that contains the document.
        headers (Optional[dict]): Optional headers to include in the request (e.g., auth).

    Returns:
        Optional[dict]: The document object if found, otherwise None.
    """
    url = f"{settings.loader_base_url}/collection/{collection_id}/document/{document_id}"
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, headers=headers)
            if response.status_code == 200:
                return response.json().get("document", response.json())
        except httpx.HTTPError:
            return None

    return None
