import logging
from typing import Optional
from uuid import UUID

import requests
from indexer_app.settings import settings

logger = logging.getLogger(__name__)


def get_queries_by_collection_id(collection_id: UUID, headers: Optional[dict] = None) -> Optional[list]:
    """Fetches all queries associated with a specific collection ID."""
    url = f"{settings.query_base_url}/collection/{collection_id}/queries"
    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            return response.json().get("queries", [])
    except requests.RequestException:
        logger.error(f"Failed to get queries for collection {collection_id}", exc_info=True)
    return []


def delete_queries_by_collection_id(collection_id: UUID, headers: Optional[dict] = None) -> bool:
    """Deletes all queries associated with a specific collection ID."""
    url = f"{settings.query_base_url}/collection/{collection_id}/queries"
    try:
        response = requests.delete(url, headers=headers)
        return response.status_code == 200
    except requests.RequestException:
        logger.error(f"Failed to delete queries for collection {collection_id}", exc_info=True)
    return False


def delete_feedbacks_by_query_id(query_id: str, headers: Optional[dict] = None) -> bool:
    """Deletes all feedbacks associated with a specific query ID."""
    url = f"{settings.query_base_url}/query/{query_id}/feedbacks"
    try:
        response = requests.delete(url, headers=headers)
        return response.status_code == 200
    except requests.RequestException:
        logger.error(f"Failed to delete feedbacks for query {query_id}", exc_info=True)
    return False
