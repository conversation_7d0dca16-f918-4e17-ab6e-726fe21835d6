# type: ignore

import time
from typing import Optional

import redis
import tiktoken
from indexer_app.llm.enums import (
    OPENAI_EMBEDDING_MODELS,
    OPENAI_MODELS,
    OS_EMBEDDING_MODELS,
    OS_MODELS,
)
from indexer_app.settings import settings


class TokenLimiter(object):
    """Token Limiter class to manage token usage and limits."""

    def __init__(self) -> None:
        """Initialize token limiter."""
        self.expire_interval_seconds = 30 * 24 * 60 * 60  # max ttl (30 days)
        self.redis_conn = redis.Redis(settings.redis_host, port=settings.redis_port, db=settings.dramatiq_redis_db)
        self.response_token_buffer = 2500

    def _get_timestamp(self) -> int:
        """Get timestamp."""
        return int(time.time())

    def store_token_usage(self, tokens_used: int, cost: Optional[float] = None) -> None:
        """Store token usage in redis.

        Args:
            tokens_used: token used.
            cost: Optional cost associated with the usage.
        """
        current_time = self._get_timestamp()
        key = f"token_usage:{current_time}"
        self.redis_conn.set(key, tokens_used)
        # set ttl
        self.redis_conn.expire(key, self.expire_interval_seconds)

        if cost:
            cost_key = f"token_usage_cost:{current_time}"
            self.redis_conn.set(cost_key, cost)
            # set ttl
            self.redis_conn.expire(cost_key, self.expire_interval_seconds)

    def store_token_usage_of_threads(self, tokens_used: int, cost: Optional[float] = None, thread_id=None) -> None:
        """Store or update token usage in Redis for a thread.

        Args:
            tokens_used: The number of tokens used.
            cost: Optional cost associated with the usage.
            thread_id: The thread identifier.
        """
        if thread_id:
            key = f"threads_token_usage:{thread_id}"
            if self.redis_conn.exists(key):
                self.redis_conn.incrby(key, tokens_used)
            else:
                self.redis_conn.set(key, tokens_used)

            if cost:
                cost_key = f"threads_token_usage_cost:{thread_id}"
                if self.redis_conn.exists(cost_key):
                    self.redis_conn.incrbyfloat(cost_key, cost)
                else:
                    self.redis_conn.set(cost_key, cost)
                    self.redis_conn.expire(cost_key, self.expire_interval_seconds)

    def get_token_usage_of_threads(self, thread_id) -> int:
        """Read token usage from redis."""
        total_usage = self.redis_conn.get(f"threads_token_usage:{thread_id}")
        return int(total_usage) if total_usage else 0

    def get_token_usage(self, interval_seconds: int = -1) -> float:
        """Read token usage from redis.

        Args:
            interval_seconds: period to query. -1: all.
        """
        current_time = self._get_timestamp()
        keys = self.redis_conn.keys("token_usage:*")

        total_usage: float = 0
        # calculate the total token count
        for key in keys:
            if interval_seconds != -1:
                token_time = int(key.decode().split(":")[-1])  # extract time from key
                if current_time - token_time <= interval_seconds:
                    usage = self.redis_conn.get(key)
                    if usage is not None:
                        total_usage += float(usage)
            else:
                usage = self.redis_conn.get(key)
                if usage is not None:
                    total_usage += float(usage)
        return total_usage

    def get_token_usage_cost(self, interval_seconds: int = -1) -> float:
        """Read token usage cost from redis.

        Args:
            interval_seconds: period to query. -1: all.
        """
        current_time = self._get_timestamp()
        keys = self.redis_conn.keys("token_usage_cost:*")

        total_usage: float = 0
        # calculate the total token count
        for key in keys:
            if interval_seconds != -1:
                token_time = int(key.decode().split(":")[-1])  # extract time from key
                if current_time - token_time <= interval_seconds:
                    usage = self.redis_conn.get(key)
                    if usage is not None:
                        total_usage += float(usage)
            else:
                usage = self.redis_conn.get(key)
                if usage is not None:
                    total_usage += float(usage)
        return total_usage

    @staticmethod
    def get_token_limit(model_name: str):
        """Get the token limit for a specific model."""
        TOKEN_LIMITS = {
            OPENAI_EMBEDDING_MODELS.ADA002.value: 2048,  # ADA-002
            OPENAI_MODELS.GPT4.value: 8192,  # GPT-4
            OPENAI_MODELS.GPT4O2.value: 32768,  # GPT-4 O2
            OS_MODELS.LLAMA270CHATHF.value: 4096,  # Llama 2 70B
            OS_MODELS.LLAMA370CHATHF.value: 128000,  # Llama 3.1 70B
            OS_MODELS.LLAMA3170BINSTRUCT.value: 128000,  # Llama 3.1 70B
            OS_MODELS.MIXTRAL8B.value: 32768,  # Mixtral 8x7B
            OS_MODELS.PHI4.value: 8000,  # Phi-3 Small
            OS_EMBEDDING_MODELS.BGEM3.value: 8192,  # BGE-M3
        }
        limit = TOKEN_LIMITS.get(model_name)
        if limit:
            return limit
        return None

    @staticmethod
    def calculate_tokens(text: str, model_name: str) -> int:
        """Calculate the number of tokens in a given text for a specific model."""
        encoding = tiktoken.encoding_for_model(model_name)
        return len(encoding.encode(text))

    @staticmethod
    def trim_previous_messages(text: str, max_tokens: int, model="gpt-4o") -> str:
        """Trim previous messages to fit within the token limit."""
        try:
            encoding = tiktoken.encoding_for_model(model)
        except Exception as e:
            encoding = None

        if not encoding:
            try:
                encoding = tiktoken.get_encoding("cl100k_base")
            except Exception as e:
                print(f"Failed to get encoding for model: {model}. Error: {str(e)}")
                encoding = None

        if not encoding:
            raise ValueError(f"Failed to get encoding for model: {model}")

        encoded_messages = encoding.encode(text)
        if len(encoded_messages) <= max_tokens:
            return text
        trimmed_encoded = encoded_messages[:max_tokens]
        trimmed_str = encoding.decode(trimmed_encoded)
        return trimmed_str

    def prepare_token_safe_question(
        self,
        user_question: str,
        previous_messages: str,
        model_name: str,
        safety_margin=100,
    ) -> str:
        """Prepare a token-safe question by trimming previous messages if necessary."""
        max_token_limit = self.get_token_limit(model_name)
        combined_prompt = f"Vorherige Nachrichten:\n{previous_messages}\n\nNeue Frage:\n{user_question}"
        prompt_tokens = self.calculate_tokens(combined_prompt, model_name)
        total_usage = prompt_tokens + self.response_token_buffer
        if total_usage <= max_token_limit:
            return combined_prompt

        user_question_tokens = self.calculate_tokens(user_question, model_name)
        max_for_previous = max_token_limit - self.response_token_buffer - user_question_tokens - safety_margin
        if max_for_previous <= 0:
            return user_question

        trimmed_prev = self.trim_previous_messages(text=previous_messages, max_tokens=max_for_previous, model=model_name)

        final_prompt = f"Vorherige Nachrichten:\n{trimmed_prev}\n\nNeue Frage:\n{user_question}"
        while True:
            final_tokens = self.calculate_tokens(final_prompt, model_name)
            total_usage_final = final_tokens + self.response_token_buffer
            if total_usage_final <= max_token_limit:
                break
            overflow = total_usage_final - max_token_limit
            trimmed_prev = self.trim_previous_messages(trimmed_prev, max_for_previous - overflow, model=model_name)
            final_prompt = f"Vorherige Nachrichten:\n{trimmed_prev}\n\nNeue Frage:\n{user_question}"
            if not trimmed_prev.strip():
                final_prompt = user_question
                break

        return final_prompt
