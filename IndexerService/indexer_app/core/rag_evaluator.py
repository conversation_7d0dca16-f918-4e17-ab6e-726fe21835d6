"""RAG Evaluator for evaluating RAG systems using MLflow"""

import json
import logging
import os

import mlflow
import pandas as pd
from indexer_app.core import semantic_search
from indexer_app.llm.models import DEFAULT_EMBEDDING_MODEL, DEFAULT_LLM_MODEL
from indexer_app.models.enums import SearchMethodsEnum
from indexer_app.settings import settings
from mlflow.metrics.genai import EvaluationExample, faithfulness, relevance


class RagEvaluator(object):
    """RAG Evaluator for evaluating RAG systems using MLflow."""

    def __init__(self, run_name, collection, search_method):
        """Initializes the RagEvaluator."""
        self.run_name = run_name
        self.collection = collection
        self.search_method = search_method

        self.logger = logging.getLogger(__name__)

        self._init()

    def _init(
        self,
    ):
        """Initialize the evaluator."""
        self.logger.info("initializing evaluator..")
        mlflow.set_tracking_uri("sqlite:////data/mlruns.db")
        # mlflow.set_tracking_uri("postgresql+psycopg://postgres:postgres@postgres:5432/mlflow")

        # get custom system prompt if exists
        self.collection_name = self.collection.get("name")
        self.custom_config = self.collection.get("custom_config", {})
        # get custom models if exists, otherwise use defaults
        self._embedding_model_name = self.custom_config.get("embedding_model", DEFAULT_EMBEDDING_MODEL)
        self._llm_model_name = self.custom_config.get("llm_model", DEFAULT_LLM_MODEL)

        # get custom system prompt if exists
        self.custom_system_prompt = self.custom_config.get("system_prompt")

        # os.environ["OPENAI_DEPLOYMENT_NAME"] = settings.llm_model_deployment_name  #  "gpt35turbo" "gpt4turbotest"
        os.makedirs(settings.evaluator_log_dir, exist_ok=True)
        self.run_log_dir = f"{settings.evaluator_log_dir}/{self.run_name}.logs"

        # TODO: fix
        if self.search_method == SearchMethodsEnum.SEMANTIC_SEARCH.value:
            self._method = semantic_search.SemanticSearch(
                collection_name=self.collection_name,
                embedding_model_name=self._embedding_model_name,
                llm_model_name=self._llm_model_name,
                custom_system_prompt=self.custom_system_prompt,
            )

    def call_rag(self, content):
        """Call the RAG method to query the content."""
        return self._method.query(content)

    def _model(self, input_df):
        """Model function to be used with MLflow evaluate."""
        answer = []
        for _, row in input_df.iterrows():
            answer.append(self.call_rag(row["questions"]))
        return answer

    def evaluate(self, questions: list, _reset: bool = False):
        """Run evaluation on given queries."""
        self.logger.info(f"running evaluation for {len(questions)} questions..")

        faithfulness_examples = [
            EvaluationExample(
                input="How do I disable MLflow autologging?",
                output="mlflow.autolog(disable=True) will disable autologging for all functions. In Databricks, autologging is enabled by default. ",
                score=2,
                justification="The output provides a working solution, using the mlflow.autolog() function that is provided in the context.",
                grading_context={
                    "context": "mlflow.autolog(log_input_examples: bool = False, log_model_signatures: bool = True, log_models: bool = True, log_datasets: bool = True, "
                    "disable: bool = False, exclusive: bool = False, disable_for_unsupported_versions: bool = False, silent: bool = False, "
                    "extra_tags: Optional[Dict[str, str]] = None) → None[source] Enables (or disables) and configures autologging for all supported integrations. "
                    "The parameters are passed to any autologging integrations that support them. See the tracking docs for a list of supported autologging "
                    "integrations. Note that framework-specific configurations set at any point will take precedence over any configurations set by this function."
                },
            ),
            EvaluationExample(
                input="How do I disable MLflow autologging?",
                output="mlflow.autolog(disable=True) will disable autologging for all functions.",
                score=5,
                justification="The output provides a solution that is using the mlflow.autolog() function that is provided in the context.",
                grading_context={
                    "context": "mlflow.autolog(log_input_examples: bool = False, log_model_signatures: bool = True, log_models: bool = True, log_datasets: bool = True, "
                    "disable: bool = False, exclusive: bool = False, disable_for_unsupported_versions: bool = False, silent: bool = False, extra_tags: "
                    "Optional[Dict[str, str]] = None) → None[source] Enables (or disables) and configures autologging for all supported integrations. "
                    "The parameters are passed to any autologging integrations that support them. See the tracking docs for a list of supported autologging "
                    "integrations. Note that framework-specific configurations set at any point will take precedence over any configurations set by this function."
                },
            ),
        ]
        faithfulness_metric = faithfulness(model=f"openai:/{self._llm_model_name}", examples=faithfulness_examples)
        relevance_metric = relevance(model=f"openai:/{self._llm_model_name}")
        evaluation_results = None

        # read previous results if exists
        if not _reset and os.path.exists(self.run_log_dir):
            self.logger.info("reading previous results from file")
            evaluation_results = pd.read_pickle(self.run_log_dir)

        _eval_df = pd.DataFrame(
            {
                "questions": questions,
                # "expected_answers": [] # in the same order with questions
            }
        )

        results = mlflow.evaluate(
            self._model,
            _eval_df,
            model_type="question-answering",
            evaluators="default",
            predictions="result",
            # targets="expected_answers",
            extra_metrics=[
                faithfulness_metric,
                relevance_metric,
                mlflow.metrics.latency(),
                mlflow.metrics.rougeL(),
            ],
            evaluator_config={"col_mapping": {"inputs": "questions", "context": "source_documents"}},
        )

        # log extra metadata
        metadata = {}
        metadata["method"] = self.search_method
        metadata["collection_name"] = self.collection_name
        metadata["llm_model"] = self._llm_model_name
        metadata["embedding_model"] = self._embedding_model_name
        metadata["llm_temperature"] = settings.default_model_temperature
        # metadata["llm_model_deployment_name"] = settings.llm_model_deployment_name
        metadata["llm_multi_answer_count"] = settings.default_llm_multi_answer_count
        # metadata["chat_model_deployment_name"] = settings.chat_model_deployment_name
        metadata["semantic_search_top_k"] = settings.semantic_search_top_k
        metadata["hyde_top_k"] = settings.hyde_top_k
        metadata["api_base"] = os.environ.get("OPENAI_API_BASE")
        metadata["metrics"] = results.metrics
        metadata["chunker_config"] = {
            "chunk_size": settings.chunk_size,
            "chunk_overlap": settings.chunk_overlap,
            "separators": settings.separators,
        }
        if self.search_method == SearchMethodsEnum.SEMANTIC_SEARCH.value:
            metadata["prompt_templates"] = {
                "prompt_template": self._method.prompt_template,
            }
        elif self.search_method == SearchMethodsEnum.HYDE.value:
            metadata["prompt_templates"] = {
                "hyde_prompt_template": self._method.hyde_prompt_template,
                "chat_prompt_template": self._method.prompt_template,
            }

        df = results.tables["eval_results_table"]
        df["metadata"] = json.dumps(metadata)

        if evaluation_results is None:
            evaluation_results = pd.DataFrame(columns=df.columns)

        evaluation_results = pd.concat([evaluation_results, df], ignore_index=True)

        # save evaluation results to disk
        self.logger.info(f"saving evaluation results to: {self.run_log_dir}")
        evaluation_results.to_pickle(self.run_log_dir)


""" SAMPLE USAGE
from app.services.rag_evaluator import RagEvaluator
from app.models.query import SearchMethodsEnum

run_name = "evaluate_chunk_size_variation"
collection_name = "evaluation-test"
questions = [
    "Welchen Beruf hat M-aria Müller ausgeübt und was waren dort ihre Hauptaufgaben?",
    "Welchen Beruf hat Frau Müller früher ausgeübt und was waren ihre Tätugkeiten in diesem Job?",
    "Welche Tätigkeiten hat Frau Müller als Krankenschwester ausgeübt?",
    "Welche Tätigkeiten hat Maria Mueller als Krankenschwester ausgeübt?",
    "Was gehörte zu den Hauptaufgaben",
    "Was gehörte zu den Hauptaufgaben einer Krankenschwester?"
]
rag_evaluator_sem = RagEvaluator(run_name, collection_name, SearchMethodsEnum.SEMANTIC_SEARCH.value)
rag_evaluator_hyde = RagEvaluator(run_name, collection_name, SearchMethodsEnum.HYDE.value)

rag_evaluator_sem.evaluate(questions)
rag_evaluator_hyde.evaluate(questions)
"""
