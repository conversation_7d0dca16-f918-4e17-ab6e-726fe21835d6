import logging
from enum import Enum
from time import time
from typing import Optional
from uuid import UUID

from indexer_app.core.token_limiter import Token<PERSON>imiter
from indexer_app.core.vectordb import VectorDB
from indexer_app.llm.models import MODEL_PRESETS
from indexer_app.settings import settings

logger = logging.getLogger(__name__)


class ChatHistoryTTL(Enum):
    """Enum for chat history TTL (Time To Live) values in seconds."""

    ONE_DAY = 60 * 60 * 24  # 86400 sec
    ONE_WEEK = 60 * 60 * 24 * 7  # 604800 sec
    TWO_WEEKS = 60 * 60 * 24 * 14  # 1209600 sec
    THREE_WEEKS = 60 * 60 * 24 * 21  # 1814400 sec
    ONE_MONTH = 60 * 60 * 24 * 30  # 2592000 sec
    SIX_MONTHS = 60 * 60 * 24 * 180  # 15552000 sec
    ONE_YEAR = 60 * 60 * 24 * 365  # 31536000 sec


DEFAULT_SYSTEM_PROMPT_MEMORY = """
Du bist ein Assistent für die Beantwortung von Ausschreibungen bzw. das E<PERSON>elle<PERSON> von Bewerbungsunterlagen auf Ausschreibungen.
Anbei hast du eine ZIP-Datei mit diversen Unterlagen zu diesem Thema von einem unserer Kunden.
Wenn dir eine Aufgabe gestellt wird, stellst du entsprechende Nachfragen, bis du sicher bist, dass du einen entsprechenden Text formulieren kannst.
Bitte beschreibe deine Arbeitsschritte.Wenn Sie eines oder mehrere der bereitgestellten Dokumente zitieren, geben Sie diese bitte in Ihren Antworten in Form ["Name Der Quelle 1",
"Name Der Quelle 2"] an. So viel wie möglich verwenden.Formuliere die Texte sachlich und beziehe dich auf innerhalb der Dokumente vorhandene Informationen.

Zusätzlich darfst du gerne auf den bisherigen Gesprächsverlauf (Chatverlauf) zurückgreifen, um bereits genannte Informationen (z.B. den Namen des Nutzers) in deine Antworten mit
einzubeziehen.
"""

DEFAULT_SYSTEM_PROMPT = """
Du bist ein assistent für die beantwortung von ausschreibungen bzw. das erstellen von bewerbungsunterlagen auf ausschreibungen.
    Anbei hast du eine zip datei mit diversen unterlagen zu diesem thema von einem unserer kunden.
    wenn dir eine aufgabe gestellt wird, stellst du entsprechende nachfragen bis du sicher bist dass du einen entsprechenden text formulieren kannst.
    Bitte beschreibe deine arbeitsschritte.
    Wenn Sie eines oder mehrere der bereitgestellten dokumente zitieren, geben Sie diese bitte in Ihren Antworten in form ["Name Der Quelle 1", "Name Der Quelle 2"] an. So viel
    wie möglich verwenden.Formuliere die texte sachlich und beziehe dich auf innerhalb der dokumente vorhandene informationen.
"""

PROMPT_TEMPLATE_SUFFIX = """

    {context}

    Frage: {question}
    Hilfreiche Antwort:"""

PROMPT_TEMPLATE_SUFFIX_MEMORY = """
    Chatverlauf:
    {chat_history}

    Kontext:
    {context}

    Frage:
    {question}

    Hilfreiche Antwort:
"""


class SemanticSearch(object):
    """SemanticSearch class for searching relevant documents in a collection based on a user's query."""

    def __init__(
        self,
        collection_name: str,
        embedding_model_name: str,
        top_k: Optional[int] = settings.semantic_search_top_k,
        search_method: str = "similarity",
    ):
        """Semantic search class.Initializes the semantic search class.What is the purpose of this class?
        It is used to search for relevant documents in the collection based on the user's query.
        How does it work?
        It uses the retrieval chain to retrieve relevant documents from the collection based on the user's query.
        What are the inputs?
        The inputs are the collection name, the embedding model name, the llm model name, the top k value, the custom system prompt, the streaming flag, the search method, and the
         memory flag.
        What are the outputs?
        The outputs are the relevant documents from the collection based on the user's query.
        Args:
            collection_name: The name of the collection.
            embedding_model_name: The name of the embedding model.
            top_k: The number of documents to be retrieved.
            search_method: The search method.
        """
        self.collection_name = f"{collection_name}_semantic_search"
        self.top_k = top_k
        self.search_method = search_method

        # LLM models
        self._embedding_model = MODEL_PRESETS[embedding_model_name].embedding_model

        # Loading the vector database and token limiter
        self._load_db()
        self.token_limiter = TokenLimiter()

    def _load_db(self) -> None:
        """Load/init vectordb."""
        self.vectordb = VectorDB(
            self.collection_name,
            embedding_model=self._embedding_model,
            search_method=self.search_method,
        )

    def index_anonymized_chunks(self, document_id: UUID, splits: list) -> None:
        """Add documents(preferably anonymized) to the collection.

        Args:
            document_id: document identifier.
            splits: chunks.
        """
        _start = time()
        logger.debug(f"extracting embeddings for {len(splits)} chunks")
        self.vectordb.add_documents(document_id, splits)
        _elapsed_time = time() - _start
        logger.debug(f"extracted embeddings for {len(splits)} chunks in {_elapsed_time} seconds")
