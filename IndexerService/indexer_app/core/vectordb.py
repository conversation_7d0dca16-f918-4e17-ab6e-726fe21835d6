import json
import logging
import re
from collections import defaultdict
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor
from time import sleep
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

import numpy as np
from indexer_app.database.session import db_context
from indexer_app.settings import settings
from langchain.schema import Document
from langchain_postgres import PGVector
from sqlalchemy import or_

logger = logging.getLogger(__name__)


class VectorDB:
    """
    A lightweight wrapper around the new `langchain-postgres` PGVector store.

    Responsibilities
    ---------------
    • Create / retrieve a pgvector-backed collection in PostgreSQL
    • CRUD operations on document chunks (add, update, delete)
    • Provide multiple search strategies (similarity, MMR, keyword, hybrid)
    • Expose convenience helpers for raw documents / embeddings
    """

    # ------------------------------------------------------------------ #
    # Construction
    # ------------------------------------------------------------------ #

    def __init__(
        self,
        collection_name: str,
        embedding_model,
        search_method: str = "similarity",
        top_k: int = 5,
        use_jsonb: bool = True,
    ) -> None:
        """
        Parameters
        ----------
        collection_name : str
            Name of the pgvector collection in PostgreSQL.
        embedding_model :
            Any LangChain-compatible embedding model instance.
        search_method : str, default "similarity"
            Which search strategy to use (`similarity`, `mmr`, `keyword`, `hybrid`).
        top_k : int, default 4
            Default number of results returned by similarity-based searches.
        """
        self.collection_name = collection_name
        self.embedding_model = embedding_model
        self.top_k = top_k
        self.use_jsonb = use_jsonb

        self.db = PGVector(
            embeddings=self.embedding_model,
            collection_name=self.collection_name,
            connection=str(settings.db_url),
            use_jsonb=use_jsonb,
        )

        # Quick-and-easy Retriever façade
        self.retriever = self.db.as_retriever(search_kwargs={"k": top_k})

        # Dispatch table for the search strategies
        self.search_method_dispatcher = self._initialize_search_method(search_method)

    # ------------------------------------------------------------------ #
    # Serialisation helpers (useful for API responses)
    # ------------------------------------------------------------------ #

    @staticmethod
    def serialize_collection_store(collection_store) -> dict:
        """Return a minimal JSON-serialisable representation of a collection."""
        return {"name": collection_store.name, "cmetadata": collection_store.cmetadata}

    @staticmethod
    def _json_serialize_safe(obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")

    def serialize_embedding_store(self, embedding_store, with_embedding: bool = False, stringfy: bool = False) -> dict:
        """
        Convert an EmbeddingStore row to a dict.

        `with_embedding=True` includes the embedding vector itself
        (nice for export or analysis, heavy for web responses).
        """
        if stringfy:
            data = {
                "collection_id": str(embedding_store.collection_id),
                "document": str(embedding_store.document),
                "cmetadata": json.dumps(embedding_store.cmetadata),
                "id": str(embedding_store.id),
            }
            if with_embedding:
                data["embedding"] = json.dumps(embedding_store.embedding, default=self._json_serialize_safe)

        else:
            data = {
                "collection_id": embedding_store.collection_id,
                "document": embedding_store.document,
                "cmetadata": embedding_store.cmetadata,
                "id": embedding_store.id,
            }
            if with_embedding:
                data["embedding"] = embedding_store.embedding
        return data

    # ------------------------------------------------------------------ #
    # Collection statistics
    # ------------------------------------------------------------------ #

    def _get_collection_count(self) -> int:
        """Return the number of chunks in *this* collection."""
        logger.info("_get_collection_count")
        with db_context() as session:
            collection = self.db.get_collection(session)
            if not collection:
                return 0

            return session.query(self.db.EmbeddingStore).filter_by(collection_id=collection.uuid).count()

    def get_item_count(self) -> int:
        """Return total number of chunks across *all* collections."""
        with db_context() as session:
            return session.query(self.db.EmbeddingStore).count()

    # ------------------------------------------------------------------ #
    # CRUD
    # ------------------------------------------------------------------ #

    def add_document(self, document_id: UUID, page_content: str, metadata: dict) -> None:
        """Insert a single chunk into the collection (synchronous)."""
        doc = Document(page_content=page_content, metadata=metadata)
        self.db.add_documents([doc], ids=[document_id])

    def add_documents(self, document_id: UUID, splits: list) -> None:
        """
        Batch-insert a list of `splits` (chunks) with minimal rate-limit pressure.

        Chunks are pushed in batches of `split_by` with a small sleep between
        to stay on the safe side of OpenAI rate limits.
        """
        split_by = 10
        sleep_interval = 1
        logger.info(f"[{document_id=}] | {len(splits)} chunks; batch={split_by}, sleep={sleep_interval}s")

        for batch_start in range(0, len(splits), split_by):
            batch = splits[batch_start : batch_start + split_by]
            docs, ids = [], []

            for i, split in enumerate(batch):
                chunk_id = f"{document_id}_{batch_start + i}"
                # Replace NULL bytes to prevent Postgres errors
                docs.append(
                    Document(
                        page_content=str(split.page_content).replace("\x00", "\uFFFD"),
                        metadata=split.metadata,
                    )
                )
                ids.append(chunk_id)

            # Run the insert in a background thread to unblock the event-loop
            with ThreadPoolExecutor(max_workers=8) as pool:
                future = pool.submit(self.db.add_documents, docs, ids=ids)
                try:
                    future.result()
                except Exception as exc:  # noqa: BLE001
                    logger.error("Batch insert failed: %s", exc)

            sleep(sleep_interval)

    def delete_document(self, document_id: UUID) -> None:
        """Delete a chunk by its custom ID."""
        self.db.delete(ids=[str(document_id)])

    def update_document(self, document_id: UUID, content: str, metadata: dict) -> None:
        """
        Brute-force update: delete then re-insert.

        If you need atomic updates, wrap this call in a database transaction.
        """
        self.delete_document(document_id)
        self.add_document(document_id, content, metadata)

    # ------------------------------------------------------------------ #
    # Search strategies
    # ------------------------------------------------------------------ #

    def _initialize_search_method(self, search_method: str) -> Any:
        """Return the chosen search function."""
        methods = {
            "similarity": self.similarity_search,
            "mmr": self.max_marginal_relevance_search_with_score_by_vector,
            "keyword": self.keyword_search,
            "hybrid": self.hybrid_search,
        }
        if search_method not in methods:
            raise ValueError(f"Unknown search method: {search_method}. Choose from {list(methods)}")
        return methods[search_method]

    # Public search entry-point ----------------------------------------- #

    def search(self, query: str, k: Optional[int] = None) -> List[Tuple[Document, float]]:
        """Delegate to the configured search strategy."""
        return self.search_method_dispatcher(query, k=k)

    # -- similarity ----------------------------------------------------- #

    def similarity_search(self, query: str, k: int = 4) -> List[Tuple[Document, float]]:
        """Safer cosine-similarity top-K search with collection_id filtering."""
        with db_context() as session:
            collection = self.db.get_collection(session)
            if not collection:
                return []
            return self.db.similarity_search_with_score(
                query,
                k=k or self.top_k,
                filter={"collection_id": str(collection.uuid)},
            )

    # -- MMR ------------------------------------------------------------ #

    def max_marginal_relevance_search_with_score_by_vector(
        self,
        query: str,
        k: int = 4,
        fetch_k: int = 20,
        lambda_mult: float = 0.5,
    ) -> List[Tuple[Document, float]]:
        """
        Maximal Marginal Relevance search – promotes relevance *and* diversity.

        `lambda_mult` ∈ [0, 1] controls the trade-off:
        • 0 → pure diversity, 1 → pure similarity
        """
        embedding = self.embedding_model.embed_query(query)
        return self.db.max_marginal_relevance_search_with_score_by_vector(embedding, k=k, fetch_k=fetch_k, lambda_mult=lambda_mult)

    # -- keyword -------------------------------------------------------- #

    def keyword_search(self, query: str, k: int = 4) -> List[Tuple[Document, float]]:
        """
        Simple full-scan keyword search (no indexing).

        *Only* recommended for small demo datasets –
        Postgres full-text search would scale better for production workloads.
        """
        keywords = query.split()
        with db_context() as session:
            docs = session.query(self.db.EmbeddingStore).all()

        matches = [(Document(page_content=doc.document, metadata=doc.cmetadata), 1.0) for doc in docs if any(re.search(word, doc.document, re.IGNORECASE) for word in keywords)]
        return matches

    # -- hybrid --------------------------------------------------------- #

    def hybrid_search(self, query: str, k: int = 4) -> List[Tuple[Document, float]]:
        """
        Aggregate results from all three methods and rank by combined score.

        Very naive fusion (score summation); tweak aggregation if you need
        better weighting.
        """
        all_results = self.similarity_search(query, k) + self.max_marginal_relevance_search_with_score_by_vector(query, k) + self.keyword_search(query)

        # Merge duplicates (same content & metadata) by summing the scores
        aggregated: Dict[Tuple[str, frozenset], float] = defaultdict(float)
        for doc, score in all_results:
            key = (doc.page_content, frozenset(doc.metadata.items()))
            aggregated[key] += score

        ranked = sorted(aggregated.items(), key=lambda x: x[1], reverse=True)
        return [(Document(page_content=content, metadata=dict(meta)), score) for (content, meta), score in ranked]

    # ------------------------------------------------------------------ #
    # Convenience read helpers
    # ------------------------------------------------------------------ #

    def get_relevant_documents(self, query: str, k: Optional[int] = None) -> list:
        """Retrieve relevant documents based on a query."""
        return self.db.as_retriever(search_kwargs={"k": k or self.top_k}).get_relevant_documents(query)

    def get_documents(
        self,
        collection_id: Optional[UUID] = None,
        document_id: Optional[UUID] = None,
        stringfy: bool = False,
    ) -> list:
        """
        Flexible document fetcher.

        • If *both* parameters supplied → chunks of that document inside that collection
        • If only `collection_id` → all chunks in that collection
        • If only `document_id` → all chunks starting with that custom ID
        • If neither → every chunk in the database
        """
        with db_context() as session:
            query = session.query(self.db.EmbeddingStore)
            if collection_id:
                query = query.filter(self.db.EmbeddingStore.collection_id == collection_id)
            if document_id:
                query = query.filter(self.db.EmbeddingStore.id.like(f"{document_id}%"))
            docs = query.all()

        return [self.serialize_embedding_store(d, stringfy=stringfy) for d in docs]

    # ------------------------------------------------------------------ #
    # Collection helpers
    # ------------------------------------------------------------------ #

    def get_collection(self):
        """Return the CollectionStore row for *this* collection."""
        with db_context() as session:
            return self.db.get_collection(session)

    def get_collections(self) -> list:
        """Return *all* collections in the database."""
        with db_context() as session:
            rows = session.query(self.db.CollectionStore).all()
        return [self.serialize_collection_store(r) for r in rows]

    def get_documents_with_embeddings(
        self,
        document_ids: Optional[List[str]] = None,
        stringfy: bool = False,
        filter_by_collection: bool = False,
    ) -> list:
        """Fetch documents with embeddings."""
        with db_context() as session:
            query = session.query(self.db.EmbeddingStore)

            if filter_by_collection:
                matching_collections = session.query(self.db.CollectionStore.uuid).filter(self.db.CollectionStore.name == self.collection_name).all()
                collection_uuids = [row.uuid for row in matching_collections]
                if not collection_uuids:
                    return []
                query = query.filter(self.db.EmbeddingStore.collection_id.in_(collection_uuids))

            if document_ids:
                patterns = [f"{doc_id}%" for doc_id in document_ids]
                like_conds = [self.db.EmbeddingStore.id.like(p) for p in patterns]
                query = query.filter(or_(*like_conds))

            docs = query.all()

        return [self.serialize_embedding_store(d, with_embedding=True, stringfy=stringfy) for d in docs]
