# Indexer Service – **Deep-Dive Guide**

*(architecture · task graph · semantic indexing pipeline)*

> **Goal of this file** Give engineers a **single, in-depth reference** for how
> the **Indexer** service works internally:
> • which moving parts exist and why
> • how Dramatiq actors communicate
> • how chunks become vectors and end-up in pgvector
> • where *Semantic Search* and *HyDE* differ in practice
> • how evaluation & clean-up jobs piggy-back on the same queues
> Copy-paste this into `docs/internal/indexer_service.md`.

---

## 1. Bird’s-eye view

```mermaid
flowchart LR
    subgraph Loader
      L1[Upload / Scrape] -->|chunks→Redis| L2(Loader · load_* actors)
      L2 -->|READY_TO_BE_INDEXED| MQ[RabbitMQ message]
    end
    MQ --> IDX0[indexer_move_*_forward]
    subgraph Indexer
      IDX1[extract_embeddings_*] --> PGV[(pgvector)]
      PGV --> IDX2[mark_*_as_indexed]
      IDX1 -->|fail| IDX3[mark_*_as_failed]
      IDX4[delete_*] -.->|drop vectors<br/>delete in Loader| Loader
    end
```

*Loader* parses & chunks **documents / URLs**, stashes them in Redis,
then publishes a *READY\_TO\_BE\_INDEXED* event.
The **Indexer** consumes that event and takes over:

1. **Embedding extraction** – generate vectors, upsert into *pgvector*
2. **Status update** – patch original record in Loader (`INDEXED` or `FAILED`)
3. **House-keeping** – delete vectors & Loader rows when asked

Everything is driven by Dramatiq actors, one logical step per queue.

---

## 2. Key components

| Layer                 | Artifact                                                    | What it does                                                                                   |
| --------------------- | ----------------------------------------------------------- | ---------------------------------------------------------------------------------------------- |
| **Task broker**       | **RabbitMQ** (`dramatiq.brokers.rabbitmq.RabbitmqBroker`)   | Durable queues, ACK semantics, retry routing keys (\*\*.DQ\*).                                 |
| **State machine**     | `indexer_app.tasks.core.state_mapping()`                    | Maps **(current status, event)** ⇒ **next actor name**. Shared by Doc & URL flows.             |
| **Router actors**     | `indexer_move_document_forward`, `indexer_move_url_forward` | Stateless dispatchers; pick the next actor from the FSM and `.send()` it.                      |
| **Vector storage**    | `VectorDB` wrapper around **`langchain-postgres.PGVector`** | Collection CRUD, similarity / MMR / keyword / hybrid search.                                   |
| **Embedding engines** | `SemanticSearch` / `HyDE`                                   | Build `VectorDB` with the proper embedding model; HyDE first halluc-creates synthetic queries. |
| **Redis**             | Ephemeral chunk buffer                                      | Loader pushes ➔ Indexer pops ➔ cleans up.                                                      |
| **PG**                | `embedding_store` table per collection                      | Stores vectors + JSONB metadata.                                                               |

---

## 3. Document & URL lifecycle

### 3.1 Finite-state machine

```
ADDED/FAILED
   │  (EMBEDDING_REQUEST)
   ▼
EXTRACTING_EMBEDDINGS
   │  ├── EMBEDDING_FINISHED → INDEXED
   │  └── EMBEDDING_FAILED   → FAILED
   └── DELETE_REQUEST        → DELETED
INDEXED
   └── DELETE_REQUEST        → DELETED
```

*Only these five statuses ever reach the Indexer – anything else stays Loader-side.*

### 3.2 Who fires which event?

| Event                | Fired by                                         | Typical reason                                        |
| -------------------- | ------------------------------------------------ | ----------------------------------------------------- |
| `EMBEDDING_REQUEST`  | Loader, *after* chunking / via REST *reload*     | Fresh upload, manual retry                            |
| `EMBEDDING_FINISHED` | `extract_embeddings_*` actor                     | Vectors were inserted successfully                    |
| `EMBEDDING_FAILED`   | `extract_embeddings_*` actor                     | Exception during embedding or DB write                |
| `DELETE_REQUEST`     | REST `DELETE` endpoints, cascade collection wipe | User or clean-up                                      |
| *implicit* `TIMEOUT` | Dramatiq (`TimeLimit` middleware)                | Worker killed after **`DRAMATIQ_TASK_TIME_LIMIT_MS`** |

---

## 4. Embedding pipeline in depth

```mermaid
sequenceDiagram
    participant R as Redis
    participant E as extract_embeddings_*
    participant V as VectorDB  / PGVector
    participant L as Loader API

    Loader->>R: LSET doc_id_splits = pickled LangChain docs
    Note right of R: 1 key per chunk + one master list
    Loader->>RabbitMQ: emit READY_TO_BE_INDEXED
    RabbitMQ->>E: EMBEDDING_REQUEST
    E->>R: GET doc_id_splits
    alt method == "semantic_search"
         E->>V: add_documents()
    else method == "hyde"
         E->>HyDE: hallucinate question
         HyDE->>V: add_documents()
    end
    V-->>E: success / error
    E->>R: DEL doc_id_splits*           (cleanup)
    E->>RabbitMQ: EMBEDDING_FINISHED / EMBEDDING_FAILED
    E->>L: PATCH /document status=INDEXED|FAILED
```

### 4.1 Chunk source

| Source       | Who produced it        | Field                                   | Notes                                         |
| ------------ | ---------------------- | --------------------------------------- | --------------------------------------------- |
| **Document** | Loader `load_document` | `split.page_content` + `split.metadata` | PDF / DOCX / etc.                             |
| **URL**      | Loader `load_url`      | same                                    | `content` scraped via `scrape_text_from_url`. |

### 4.2 SemanticSearch vs HyDE

| Step                         | *SemanticSearch*                                   | *HyDE*                                                                                |
| ---------------------------- | -------------------------------------------------- | ------------------------------------------------------------------------------------- |
| **Embedding**                | direct → `Ada-002` (or model from `custom_config`) | *two-step* ⚑ – generate hypothetical answer with LLM (`llm_model`), embed that answer |
| **pgvector collection name** | `<collection>_semantic_search`                     | `<collection>_hyde`                                                                   |
| **Search options**           | similarity / MMR / hybrid / keyword                | similarity only (for now)                                                             |
| **Pros**                     | deterministic, cheap                               | often recalls more niche docs                                                         |
| **Cons**                     | may miss sparse signals                            | slower + LLM cost                                                                     |

You can switch per collection through `custom_config.method`.

---

## 5. Collection deletion cascade

1. REST `DELETE /collection/{id}` → **Indexer** calls `delete_collection` actor.
2. Actor fan-outs:
   • `LoaderService.DELETE /urls` (sync)
   • `indexer_move_document_forward → …DELETE_REQUEST` for every doc
   • delete Queries + Feedback via Query-service client
3. Schedules `delete_collection_itself` (1 s later) → polls Loader until 0 docs + 0 queries → drops pgvector table → `LoaderService.DELETE /collection`.

All chat histories in Redis whose key starts with the collection name are
also purged (`delete_session_chat_history_redis_keys`).

---

## 6. RAG evaluation workflow

| Phase              | Implementation                                                                                      | Output                                              |
| ------------------ | --------------------------------------------------------------------------------------------------- | --------------------------------------------------- |
| **1. Scheduling**  | REST `POST /collection/{id}/evaluate` collects `run_name + questions` → `run_rag_evaluation` actor. | Dramatiq message                                    |
| **2. Execution**   | Actor instantiates two evaluators: Semantic and HyDE.                                               | Each writes metrics to Postgres via `RagEvaluator`. |
| **3. Consumption** | BI dashboards pull from `rag_run` table.                                                            | Success / error is logged; no FSM involved.         |

---

## 7. Error handling & observability

* **Retries** – Every actor inherits `max_retries = ${DRAMATIQ_TASK_MAX_RETRIES}`.
* **Dead-letter queues** – Dramatiq auto-routes to `*.DQ` on unrecoverable failure.
* **Timeouts** – `TimeLimit` middleware kills jobs exceeding `${…TIME_LIMIT_MS}`.
* **Structured logs** – all actors call `dramatiq_task_logger` (JSON), ready for Loki.
* **Health** – start-up calls `MessageBroker.check_broker_health()`; readiness probe checks Postgres/Redis/RabbitMQ via `/ready`.

---

## 8. Extending the Indexer

| Task                              | Where to patch                                                                       | Gotchas                                                     |
| --------------------------------- | ------------------------------------------------------------------------------------ | ----------------------------------------------------------- |
| **Add a new search strategy**     | • implement `VectorDB.my_fancy_search` <br>• register in `_initialize_search_method` | return `List[Tuple[Document,float]]`.                       |
| **Support a new embedding model** | • add to `MODEL_PRESETS` <br>• allow in `SemanticSearch`, `HyDE` config              | ensure model returns `embed_query()` & `embed_documents()`. |
| **Per-tenant pgvector schema**    | wrap `VectorDB.db` calls with schema switch (`SET search_path`).                     | collection rename logic must follow.                        |
| **Distributed chunk storage**     | swap Redis with S3/gcs + presigned URLs.                                             | `extract_embeddings_*` must change fetch logic.             |

---

> **TL;DR** Indexer is a queue-driven, pluggable embedding pipeline.
> Loader handles raw files, Indexer owns vectors & lifecycle.
> Everything that happens after *“READY\_TO\_BE\_INDEXED”* is documented here.


# Indexer Service API Reference

*(v1 – Auth, Collection, Document, Embedding & Evaluation modules)*

> **Copy-paste-ready.** Drop the file into `docs/indexer_api.md`.
> Keep this doc and the earlier “Deep-Dive Guide” side-by-side:
> *this file* = external contract; *guide* = internal plumbing.

---

## Table of Contents

1. [Authentication](#authentication)
   1.1 [`GET /login-url`](#get-login-url)
   1.2 [`GET /callback`](#get-callback)
   1.3 [`GET /logout`](#get-logout)

2. [Collections](#collections)
   2.1 [`DELETE /collection/{collection_id}`](#delete-collection-id)

3. [Documents](#documents)
   3.1 [`DELETE /collection/{collection_id}/document/{document_id}`](#delete-document)

4. [Embeddings / Vector DB](#embeddings)
   4.1 [`GET /embedding/item_count`](#embedding-item-count)
   4.2 [`GET /embedding/collections`](#embedding-collections)
   4.3 [`GET /embedding/collection/{collection_id}`](#embedding-collection-info)
   4.4 [`GET /embedding/collection/{collection_id}/documents/embed`](#embedding-docs-with-vectors)
   4.5 [`GET /embedding/collection/{collection_id}/search`](#embedding-search)
   4.6 [`GET /embedding/collection/{collection_id}/relevant`](#embedding-relevant)
   4.7 [`POST /embedding/collection/{collection_id}/document`](#embedding-insert-chunk)
   4.8 [`POST /embedding/collection/{collection_id}/documents/batch`](#embedding-insert-batch)
   4.9 [`PATCH /embedding/collection/{collection_id}/document/{document_id}`](#embedding-update-chunk)
   4.10 [`DELETE /embedding/collection/{collection_id}/document/{document_id}`](#embedding-delete-chunk)
   4.11 [`PUT /embedding/collection/{collection_id}/rename`](#embedding-rename)

5. [Evaluation](#evaluation)
   5.1 [`POST /collection/{collection_id}/evaluate`](#post-evaluate)

---

## Conventions

* **Auth** — except the three `/auth` routes every call needs an **Azure AD Bearer
  token** or an `X-Admin-UI: true` header (Admin UI & local dev).
* **UUIDs** are 36-char RFC 4122 strings.
* Unless stated otherwise, all responses are JSON (`application/json`).
* Timestamps are ISO-8601 UTC.

---

## 1  Authentication

### <a id="get-login-url"></a>`GET /login-url`

|                    |                                                              |
| ------------------ | ------------------------------------------------------------ |
| **Purpose**        | Returns a pre-built Microsoft login URL.                     |
| **Auth**           | none                                                         |
| **Response `200`** | `{ "login_url": "<https://login.microsoftonline.com/...>" }` |

---

### <a id="get-callback"></a>`GET /callback`

Handle the Azure AD redirect, exchange the *code* for a JWT and forward the
browser to the Client UI.

|             |                                                     |
| ----------- | --------------------------------------------------- |
| **Query**   | `code` (string, required)                           |
| **Success** | **302** → `<CLIENT_UI>?access_token=<jwt>`          |
| **Errors**  | `400` missing / invalid code • upstream HTTP status |

---

### <a id="get-logout"></a>`GET /logout`

Logs the user out of Azure AD then redirects to the Client UI.

|              |                                     |
| ------------ | ----------------------------------- |
| **Redirect** | **302** to Microsoft logout then UI |
| **Auth**     | none                                |

---

## 2  Collections  <a id="collections"></a>

### <a id="delete-collection-id"></a>`DELETE /collection/{collection_id}`

Trigger a **cascade purge**: URLs → Docs → pgvector → Loader DB row.

|                   |                                |
| ----------------- | ------------------------------ |
| **Path**          | `collection_id` (UUID)         |
| **Auth**          | required                       |
| **Success `200`** | `{ "message": "ok" }`          |
| **Errors**        | `404` not found • `401` unauth |

---

## 3  Documents  <a id="documents"></a>

### <a id="delete-document"></a>`DELETE /collection/{collection_id}/document/{document_id}`

Marks the document for deletion and queues background jobs that
remove vectors + DB rows.

|                   |                                       |
| ----------------- | ------------------------------------- |
| **Path**          | `collection_id`, `document_id` (UUID) |
| **Auth**          | required                              |
| **Success `200`** | `{ "message": "ok" }`                 |
| **Errors**        | `404` not found • `500` queue failure |

---

## 4  Embeddings & Vector Store  <a id="embeddings"></a>

All routes below require the caller to supply **one existing collection ID**.
Indexer auto-derives the pgvector table name.

| Abbrev   | Meaning                                                  |
| -------- | -------------------------------------------------------- |
| **SS**   | *Semantic Search* table (`<collection>_semantic_search`) |
| **HyDE** | *HyDE* table (`<collection>_hyde`)                       |

### 4.1 <a id="embedding-item-count"></a>`GET /embedding/item_count`

Returns the **total chunk count** across *all* pgvector collections.

```
GET /embedding/item_count?collection_id=<uuid>&search_type=semantic_search
→ 200 { "total_items": 123456 }
```

### 4.2 <a id="embedding-collections"></a>`GET /embedding/collections`

List every pgvector collection (name + JSONB metadata).

### 4.3 <a id="embedding-collection-info"></a>`GET /embedding/collection/{collection_id}`

Returns:

```json
{
  "name": "customer_faq_semantic_search",
  "metadata": { "embedding_model": "ada-002" },
  "chunk_count": 987
}
```

### 4.4 <a id="embedding-docs-with-vectors"></a>`GET /embedding/collection/{collection_id}/documents/embed`

Heavy endpoint – each item contains the 1536-dim vector.

Query params:
`document_ids[]=...` (repeat) • `pagination_on` • `page` • `page_size`.

### 4.5 <a id="embedding-search"></a>`GET /embedding/collection/{collection_id}/search`

| Query    | Default      | Allowed                             |
| -------- | ------------ | ----------------------------------- |
| `query`  | —            | free text                           |
| `method` | `similarity` | similarity · mmr · keyword · hybrid |
| `k`      | 4            | 2 – 50                              |

Returns a list of:

```json
{ "content": "<chunk>", "metadata": { ... }, "score": 0.83 }
```

### 4.6 <a id="embedding-relevant"></a>`GET /embedding/collection/{collection_id}/relevant`

Thin wrapper around Retriever API – no scores.

### 4.7 <a id="embedding-insert-chunk"></a>`POST /embedding/collection/{collection_id}/document`

Insert **one** chunk.

```jsonc
// Body
{
  "document_id": "af81…",
  "content": "lorem ipsum…",
  "metadata": { "page": 4 }
}
```

### 4.8 <a id="embedding-insert-batch"></a>`POST /embedding/collection/{collection_id}/documents/batch`

Array version of the above.

### 4.9 <a id="embedding-update-chunk"></a>`PATCH /embedding/collection/{collection_id}/document/{document_id}`

Supply at least one of `content` or `metadata`.

### 4.10 <a id="embedding-delete-chunk"></a>`DELETE /embedding/collection/{collection_id}/document/{document_id}`

Deletes **all vectors** whose ID starts with `<document_id>`.

### 4.11 <a id="embedding-rename"></a>`PUT /embedding/collection/{collection_id}/rename`

Body:

```json
{ "new_name": "customer_faq_v2" }
```

> Only succeeds if the new name is not already taken.

---

## 5  Evaluation  <a id="evaluation"></a>

### <a id="post-evaluate"></a>`POST /collection/{collection_id}/evaluate`

Kick-off an async **RAG evaluation run.**

|                   |                                                                   |
| ----------------- | ----------------------------------------------------------------- |
| **Body**          | `jsonc { "run_name":"may_regression", "questions":["Q1","Q2"] } ` |
| **Success `200`** | `{ "status": "ok" }`                                              |
| **Notes**         | Results land in the `rag_run` table; pull with BI tools.          |

---

### Appendix A – Common Models (excerpt)

<details>
<summary>ChunkIn</summary>

```jsonc
{
  "document_id": "af81…",
  "content": "string",
  "metadata": { "foo": "bar" }
}
```

</details>

<details>
<summary>SearchHit</summary>

```jsonc
{
  "content": "…",
  "metadata": { "page": 2 },
  "score": 0.7643
}
```

</details>

---

> © 2025 Indexer Team — suggestions & PRs welcome.
