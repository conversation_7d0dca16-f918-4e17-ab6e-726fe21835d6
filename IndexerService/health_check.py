import asyncio
import logging
import os

import aio_pika
import asyncpg
import redis.asyncio as redis

logger = logging.getLogger(__name__)

# --- Health-check helpers ------------------------------------------------ #
DB_URL = os.getenv(
    "DB_URL",
    "postgresql+psycopg://postgres_indexer:postgres_indexer@postgres_indexer:5432/postgres_indexer",
)
REDIS_EMBEDDING_URL = os.getenv("REDIS_EMBEDDING_URL", "redis://redis:6379/1")
RABBITMQ_URL = os.getenv("RABBITMQ_URL", "amqp://guest:guest@rabbitmq:5672/")
SKIP_CHECK = os.getenv("SKIP_HEALTH_CHECKS", "false").lower() == "true"


async def check_postgres(timeout: float = 1.0) -> bool:
    """Check PostgreSQL database connection for health check only."""
    try:
        # Replace '+psycopg' with empty string to make it compatible with asyncpg
        dsn = DB_URL.replace("+psycopg", "")
        conn = await asyncio.wait_for(asyncpg.connect(dsn), timeout)
        await conn.close()
        return True
    except Exception as exc:
        logger.error("PostgreSQL health-check failed: %s", exc)
        return False


async def check_redis(timeout: float = 1.0) -> bool:
    """Check Redis connection for embeddings."""
    try:
        _redis = await asyncio.wait_for(redis.from_url(REDIS_EMBEDDING_URL), timeout)
        await _redis.ping()
        await _redis.close()
        return True
    except Exception as exc:
        logger.error("Redis health-check failed: %s", exc)
        return False


async def check_rabbit(timeout: float = 1.0) -> bool:
    """Check RabbitMQ connection."""
    try:
        conn = await asyncio.wait_for(aio_pika.connect_robust(RABBITMQ_URL), timeout)
        await conn.close()
        return True
    except Exception as exc:
        logger.error("RabbitMQ health-check failed: %s", exc)
        return False
