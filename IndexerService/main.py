"""Main FastAPI application entry point for the Indexer service.

Includes router registration, middleware setup, health-check endpoints, and Azure AD auth.
"""

import asyncio
import logging
import os

import uvicorn
from fastapi import FastAPI, Request, Security, status
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSO<PERSON><PERSON>ponse
from fastapi_azure_auth import SingleTenantAzureAuthorizationCodeBearer
from health_check import SKIP_CHECK, check_postgres, check_rabbit, check_redis
from indexer_app.api import collection, document, embedding, evaluation, url
from indexer_app.database import Base, engine
from indexer_app.settings import settings
from indexer_app.utils.helpers import get_allowed_admin_hosts
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint

# ─────────────────────────────────────────────────────────────
# Config & helpers
# ─────────────────────────────────────────────────────────────
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

azure_scheme = SingleTenantAzureAuthorizationCodeBearer(
    app_client_id=settings.app_client_id,
    tenant_id=settings.tenant_id,
    scopes=settings.scopes,
    allow_guest_users=True,
)

# ─────────────────────────────────────────────────────────────
# App & middleware
# ─────────────────────────────────────────────────────────────
app = FastAPI(
    swagger_ui_oauth2_redirect_url="/oauth2-redirect",
    swagger_ui_init_oauth={
        "usePkceWithAuthorizationCodeGrant": True,
        "clientId": settings.openapi_client_id,
        "scopes": settings.scope_name,
    },
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class AdminUIMiddleware(BaseHTTPMiddleware):
    """Custom middleware to detect Admin UI requests via special headers."""

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint):
        """Intercept the request and mark it as admin UI if conditions match."""
        admin_ui_header = request.headers.get("x-admin-ui", "")
        host = request.headers.get("host", "")
        if admin_ui_header == "true" and host in get_allowed_admin_hosts():
            request.state.is_admin_ui = True
        else:
            request.state.is_admin_ui = False
        response = await call_next(request)
        return response


# Add middleware to check for adminui requests
app.add_middleware(AdminUIMiddleware)

# Routers
app.include_router(collection.router, tags=["collection"])
app.include_router(document.router, tags=["document"])
app.include_router(url.router, tags=["url"])
app.include_router(embedding.router, tags=["embedding"])
app.include_router(evaluation.router, tags=["evaluation"])


@app.get("/live", tags=["_internal"])
async def live() -> JSONResponse:
    """Liveness probe for Kubernetes.

    We are alive if the service is running. This endpoint returns a 200 status code
    if the service is alive.

    Returns:
        JSONResponse: JSON indicating the status.
    """
    return JSONResponse({"status": "alive"}, status_code=200)


@app.get("/ready", tags=["_internal"])
async def ready() -> JSONResponse:
    """Readiness probe for Kubernetes.

    We are ready if all services are up and running. Checks PostgreSQL, Redis,
    and RabbitMQ health. If SKIP_CHECK is enabled, returns 200 with skipped status.

    Returns:
        JSONResponse: JSON with individual health statuses and readiness result.
    """
    if SKIP_CHECK:
        return JSONResponse({"status": "ready", "checks": "skipped"}, 200)

    checks = await asyncio.gather(check_postgres(), check_redis(), check_rabbit())
    all_ok = all(checks)
    status_code = 200 if all_ok else 503
    return JSONResponse(
        {
            "status": "ready" if all_ok else "not_ready",
            "postgres": checks[0],
            "redis": checks[1],
            "rabbitmq": checks[2],
        },
        status_code=status_code,
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Exception handler for validation errors.

    Args:
        request (Request): The incoming HTTP request.
        exc (RequestValidationError): The validation exception raised.

    Returns:
        JSONResponse: A 422 response with error details.
    """
    exc_str = f"{exc}".replace("\n", " ").replace("   ", " ")
    logging.error(f"{request}: {exc_str}")
    content = {"status_code": 422, "message": exc_str, "data": None}
    return JSONResponse(content=content, status_code=status.HTTP_422_UNPROCESSABLE_ENTITY)


@app.get("/secure-endpoint")
async def secure_endpoint(user=Security(azure_scheme)):
    """Secure endpoint that requires Azure AD authentication.

    Useful for verifying login flows via Swagger UI (/docs).

    Args:
        user (Any): Authenticated user context from Azure.

    Returns:
        dict: Message and user info.
    """
    return {"message": "Hello, authenticated user!", "user": user}


@app.on_event("startup")
async def load_config() -> None:
    """Load Azure AD OpenID configuration on app startup.

    Ensures OpenAPI integration is configured for Swagger OAuth2 flows.

    Returns:
        None
    """
    await azure_scheme.openid_config.load_config()


@app.on_event("startup")
def startup_event():
    """Create database tables on application startup."""
    with engine.begin() as conn:
        Base.metadata.create_all(bind=conn)


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=os.getenv("API_PORT", 8001))
