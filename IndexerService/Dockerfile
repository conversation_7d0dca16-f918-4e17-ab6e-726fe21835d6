# ─────────────────────────────────────────────────────────────
#  docker/Dockerfile.fastapi
# ─────────────────────────────────────────────────────────────
# Build example:
#   docker build -f docker/Dockerfile.fastapi \
#                --build-arg SERVICE_DIR=IndexerService \
#                -t indexer-service .
#
# ─────────────────────────────────────────────────────────────
# syntax=docker/dockerfile:1.6

############################
# 1.  Builder stage        #
############################
FROM python:3.11-slim AS builder

WORKDIR /src

# System packages required only while building wheels
RUN apt-get update && apt-get install -y --no-install-recommends \
        build-essential gcc && \
    rm -rf /var/lib/apt/lists/*

# Pre‑build Python dependencies into wheels
COPY IndexerService/requirements.txt requirements.txt

RUN --mount=type=cache,target=/root/.cache/pip \
    pip wheel -r requirements.txt --wheel-dir /wheels

############################
# 2. Runtime stage         #
############################
FROM python:3.11-slim AS runtime

# Base env
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    UVICORN_ACCESS_LOG=false

WORKDIR /app

# 1) install wheels **as root**
COPY --from=builder /wheels /wheels
RUN pip install --no-index --find-links=/wheels /wheels/*

# 2) create a non‑root user and switch to it
RUN adduser --disabled-password --gecos "" appuser
USER appuser

# 3) copy application code (owned by appuser)
COPY --chown=appuser IndexerService /app


# ---- Service‑specific settings --------------------------------
# Expose the port this service listens on
#   * Change only the number if a service needs a different port
EXPOSE 8001

# Health check endpoint (adapt if your service path differs)
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s CMD \
  curl -f http://localhost:8001/health || exit 1

# Default command:
#   • LOCAL_DEV=true → hot‑reload
#   • otherwise      → production mode
CMD ["sh", "-c", "if [ \"$LOCAL_DEV\" = \"True\" ]; then \
                    uvicorn main:app --host 0.0.0.0 --port 8001 --reload; \
                  else \
                    uvicorn main:app --host 0.0.0.0 --port 8001; \
                  fi"]
