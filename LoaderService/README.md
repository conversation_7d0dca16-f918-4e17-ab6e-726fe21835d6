# LoaderService
## Table of contents

- [High-level Architecture](#high-level-architecture)

- [Features at a Glance](#features-at-a-glance)

- [Chunking Strategies](#chunking-strategies)

- [Load Methods & File-Type Support](#load-methods--file-type-support)

- [Asynchronous Task Pipeline](#asynchronous-task-pipeline)

- [Data Lifecycle & State Machine](#data-lifecycle--state-machine)

- [Running Locally](#running-locally)



## High-level Architecture

```jsonc
          ┌────────────┐  upload          ┌──────────────┐
client ───►  FastAPI    ├───────────────► │   RabbitMQ    │
          │  gateway    │  status updates └──────▲───────┘
          └────┬────────┘                           │ events
               │ REST                               │
               │                                    │
          ┌────▼────────┐  fetch-content   ┌────────┴─────────┐
          │ PostgreSQL  │◄─────────────────│ Loader worker    │
          │  metadata   │                  │  (Dramatiq)      │
          └─────────────┘                  │  – load_document │
                                           │  – load_url      │
                                           │  – mark_*        │
                                           └─────┬────────────┘
                                                 │chunks → Redis
                                                 ▼
                                           ┌────────────┐
                                           │  Indexer    │ (separate
                                           │  worker     │  service)
                                           └────────────┘
```

- **FastAPI Gateway** – AuthN/AuthZ, validation and synchronous operations

- **Loader Worker** – CPU-heavy parsing, OCR, vision, chunking

- **Redis** – temporary chunk store (low latency)

- **RabbitMQ** – decouples upload from background processing

- **PostgreSQL** – authoritative metadata (status, collections, …)



## Features at a Glance

| Category              | What it does                                                                                        |
| --------------------- | --------------------------------------------------------------------------------------------------- |
| **Multi-modal input** | PDFs (incl. images), DOCX, XLSX, PPTX, Markdown, TXT, images (JPG/PNG), JSON, URLs                  |
| **Pluggable loaders** | `LoadMethodEnum`: `USE_LANGCHAIN`, `USE_TESSERACT`, `USE_LANGCHAIN_AND_TESSERACT`, `USE_GPT_VISION` |
| **Four chunkers**     | Recursive, Token, Headline (custom), Markdown                                                       |
| **Streaming safe**    | Images & OCR processed in threads; huge PDFs paged; back-pressure via Dramatiq                      |
| **State machine**     | Precise status transitions (`UPLOADED → LOADING → READY_TO_BE_INDEXED` or `FAILED`)                 |
| **Typed models**      | `pydantic` request/response schemas; OpenAPI out-of-the-box                                         |
| **Cloud-native**      | CORS, Azure AD single-tenant, `/live` & `/ready` probes, Helm-friendly env                          |
| **Observability**     | Structured logging, per-actor task timings, detailed error metadata                                 |

## Chunking Strategies

Centralised in **`loader_app/services/chunker.py`**. Pick one with `ChunkingStrategy` env var or per-request override.

| Strategy (`Enum`)   | Ideal for                               | Splitting logic                                                                      | Default `chunk_size` | Default `chunk_overlap`                     |
| ------------------- | --------------------------------------- | ------------------------------------------------------------------------------------ | -------------------- | ------------------------------------------- |
| `recursive`         | Mixed prose or code                     | LangChain `RecursiveCharacterTextSplitter` – recursive pass over a separator cascade | 1 800 chars          | 150 chars                                   |
| `token`             | Polyglot corpora & cost-sensitive flows | LangChain `TokenTextSplitter` – boundaries follow model tokens                       | 512 tokens           | 50 tokens                                   |
| `headline` (custom) | Markdown docs, notebooks, blogs         | 1st split on `#` headers. Oversized sections re-split _once_ without inner overlap   | 2 100 chars          | 200 chars (between top-level sections only) |
| `markdown`          | Keep whole sections intact              | LangChain `MarkdownHeaderTextSplitter` – emits chunk at every header, **no overlap** | _N/A_                | 0                                           |

**Choosing a strategy**

- Want maximum context across paragraphs? **Recursive**.

- Need deterministic token budgets? **Token**.

- Docs already have headings? **Headline** gives clean section extracts with minimal duplication.

- Pure section export (e.g., knowledge-base import)? **Markdown**.

## Load Methods & File-Type Support

| Enum value                    | What happens                                                                        |
| ----------------------------- | ----------------------------------------------------------------------------------- |
| `USE_LANGCHAIN`               | Use LangChain loaders only (fast, no OCR)                                           |
| `USE_TESSERACT`               | Ignore LangChain; route image-like docs through Tesseract OCR                       |
| `USE_LANGCHAIN_AND_TESSERACT` | Hybrid: LangChain for text layers, Tesseract for embedded images inside (esp. PDFs) |
| `USE_GPT_VISION`              | Screenshot page images → GPT-4o Vision for dense extraction (slow, but **wow**)     |

Built-in File Extensions:

| Extension | Loader (LangChain)                                 | Custom Fallback                         |
| --------- | -------------------------------------------------- | --------------------------------------- |
| `pdf`     | `PyPDFLoader` (with/without `extract_images`)      | Parallel page OCR via `Loader.load_pdf` |
| `docx`    | `Docx2txtLoader`                                   | Inline image OCR                        |
| `xlsx`    | `UnstructuredExcelLoader`                          | DataFrame → one row per `Document`      |
| `pptx`    | `UnstructuredPowerPointLoader`                     | Shape & image walkers                   |
| `md`      | `UnstructuredMarkdownLoader`                       | Markdown2 → HTML                        |
| `txt`     | `TextLoader`                                       | –                                       |
| `json`    | Custom JSON flatteners (`load_json`, `load_json2`) |                                         |
| `jpg/png` | `UnstructuredImageLoader`                          | GPT-4o Vision alt path                  |
URLs are fetched & chunked by the **URL Loader** actor (see below).


## Asynchronous Task Pipeline

The heavy lifting is executed by **Dramatiq** workers.

1. `loader_move_document_forward` – single public entry. Looks up the **state machine** mapping & reroutes.

2. `load_document` – performs:

    - disk read / OCR

    - `Chunker.split_documents`

    - pushes `Document` chunks to Redis (`core.load_item`)

    - DB status → `LOADING`

3. `mark_document_as_ready_to_be_indexed` – finalises:

    - DB status → `READY_TO_BE_INDEXED`

    - emits `embedding_request` message to Indexer queue

4. `mark_document_as_failed` – DB status → `FAILED`; no downstream messages.


> **URL ingest** mirrors the same pattern (`load_url`, `move_url_forward`, …).

Task reliability:

- **Retries** – `max_retries` configurable per actor

- **Time limits** – kills hung OCR/vision jobs (`time_limit_ms`)

- **Max age** – prevents zombie messages dequeued after redeploy (`max_age_ms`)


Logs contain `dramatiq_task_logger` correlation IDs so uploads can be traced end-to-end.




## Data Lifecycle & State Machine & Tasks

| State                 | Triggering Event                | Next Actor / Transition                                     |
| --------------------- | ------------------------------- | ----------------------------------------------------------- |
| `UPLOADED` (initial)  | REST `POST /document`           | `load_document` (`event="load"`)                            |
| `LOADING`             | Loader parsing                  | `loading_finished` → `mark_document_as_ready_to_be_indexed` |
| `READY_TO_BE_INDEXED` | DB update + RabbitMQ push       | (handled by **Indexer** service)                            |
| `FAILED`              | Any exception in actor pipeline | Retry allowed via `/reload`                                 |
|                       |                                 |                                                             |


The Loader service off-loads heavy work—parsing, chunking, OCR, etc.—to
background **Dramatiq workers**.
Two nearly-identical pipelines exist:

| Item type    | Public entry actor             | Load actor      | Success actor                          | Failure actor             |
| ------------ | ------------------------------ | --------------- | -------------------------------------- | ------------------------- |
| **Document** | `loader_move_document_forward` | `load_document` | `mark_document_as_ready_to_be_indexed` | `mark_document_as_failed` |
| **URL**      | `move_url_forward`             | `load_url`      | `mark_url_as_ready_to_be_indexed`      | `mark_url_as_failed`      |
|              |                                |                 |                                        |                           |

#### Life-of-a-document

```text
REST  ➜  RabbitMQ  ➜  Dramatiq worker  ➜  Redis / RabbitMQ
POST   "load_request"  load_document       - store N chunks
/doc   ────────────►  (status=LOADING) ──► - enqueue embedding_request
```

1. **Upload**
    `POST /collection/{id}/document` creates a DB row (`status=ADDED`) and
    publishes **`load_request`** via `loader_move_document_forward`.

2. **Loading** (`load_document`)

    - Reads file from disk.

    - Runs OCR / LangChain loaders (`Loader.load_documents`).

    - Splits into chunks with `Chunker.split_documents`.

    - Stores chunks in Redis (`{document_id}_split_*`).

    - Emits **`load_finished`** or **`load_failed`**.

3. **Ready → Indexer**
    `mark_document_as_ready_to_be_indexed` sets
    `status=READY_TO_BE_INDEXED` and pushes **`embedding_request`** to the
    Indexer queue.

4. **Failures & Retries**
    Any raised exception triggers `mark_document_as_failed`
    (`status=FAILED`). A user can call `POST …/reload` to re-emit
    `load_request`.


The URL flow is identical, except:

- Actors are prefixed with **`url_`**.

- The helper `get_documents_from_item` turns scraped HTML/text into one big
    LangChain `Document` which the `Chunker` then splits.


#### Finite-state machine

(Implemented in `tasks/core.state_mapping()` + `move_item_forward()`)

|Current status|Incoming event|Next actor|New status|
|---|---|---|---|
|`ADDED`|`load_request`|`load_*`|`LOADING`|
|`LOADING`|`load_finished`|`mark_*_as_ready_to_be_indexed`|`READY_TO_BE_INDEXED`|
|`LOADING`|`load_failed`|`mark_*_as_failed`|`FAILED`|
|`FAILED`|`load_request`|`load_*`|`LOADING`|

#### Chunk strategy per collection

Before splitting, `core.load_item()` looks up
`collection.custom_config`:

```jsonc
{
  "chunk_size": 2100,
  "chunk_overlap": 200,
  "chunking_strategy": "HEADLINE"
}
```

If present, those values override the global defaults when the
`Chunker` instance is built.

#### Cancellation safety

Long-running tasks (e.g. huge PDFs) check `redis:get("cancel_task_<id>")`
via `is_task_canceled()`.
An admin UI or another worker can set that key to stop processing early.

---

**Key take-aways**

- **Dramatiq** keeps CPU-heavy parsing off the request path.

- **Redis** is used only for transient chunk storage.

- A single **state machine** drives both Documents and URLs; only the
    actor names differ.

- The pipeline is **idempotent**—any `FAILED` item can be retried.

- Chunk parameters can be tuned _per collection_ without redeploying code.



## Running Locally

> Requirements: Docker (24+), Python 3.10+, Make (optional)

 1. Clone and bootstrap
git clone https://github.com/your-org/loader-service.git
cd loader-service
python -m venv .venv && source .venv/bin/activate
pip install -r requirements.txt

2. Spin up the full stack
docker compose up -d  rabbitmq redis postgres_loader postgres_indexer postgres_query

3. Run database migrations (creates tables)
python -m loader_app.database.migrate

4. Start Dramatiq workers
dramatiq loader_app.tasks --processes 2 --threads 4 &

5. Launch REST API
uvicorn loader_app.main:app --reload --port 8002


Open **[http://localhost:8002/docs](http://localhost:8002/docs)** and authenticate via Azure AD (PKCE flow) to explore endpoints.



Using `docker compose` only:

`docker compose up -d loader loader-worker indexer indexer-worker query admin-ui`

- **Volumes** – temp files land in `./data/loader/tmp`, PDFs for demos in `./data/pdfs`.

- **Ports**

    - Loader API – **8002**

    - Indexer API – **8001**

    - Query API – **8003**

    - Admin UI – **8501**


To run every service

`docker compose up --build`

Stop everything:

`docker compose down -v`



# Loader Service API Reference

_(Loader endpoints– v1)_

---

## Table of Contents

1. [Authentication]()
    1.1 [`GET /login-url`]()
    1.2 [`GET /callback`]()
    1.3 [`GET /logout`]()

2. [Collections](https://chatgpt.com/c/6839ba27-ed10-8004-9eb4-bb413ef8e585#collections)
    2.1 [`GET /collections`]()
    2.2 [`POST /collection`]()
    2.3 [`GET /collection`]()
    2.4 [`PATCH /collection/{collection_id}`]()
    2.5 [`DELETE /collection/{collection_id}`]()
    2.6 [`GET /document/{document_id}/collection`]()

3. Document endpoints

4. URL endpoints




---

## Authentication

All Loader Service routes—except those listed here—require an **Azure AD** bearer token:

```text
Authorization: Bearer <access_token>
```

### 1.1 GET `/login-url`

|Field|Value / Description|
|---|---|
|**Purpose**|Generate the Azure AD sign-in URL.|
|**Returns**|`200 OK`  → `{ "login_url": "<URL>" }`|
|**Auth**|None|
|**Notes**|Front-end should redirect the browser to `login_url`; Azure AD will bounce back to **`/callback`**.|

---

### 1.2 GET `/callback`

|Field|Value / Description|
|---|---|
|**Query param**|`code` (string, **required**) – authorization code from Azure AD|
|**Success**|`302` redirect → `<CLIENT_UI>?access_token=<jwt>`|
|**Error codes**|`400` (code missing) · Upstream HTTP codes if token exchange fails|

---

### 1.3 GET `/logout`

|Field|Value / Description|
|---|---|
|**Purpose**|Sign the user out of Azure AD and return them to the Client UI.|
|**Redirect**|`302` → Microsoft logout then `<CLIENT_UI>`|
|**Auth**|None|

---

## Collections

A **collection** groups documents that share the same embedding space and configuration (chunk sizes, LLM model, web-search flags, etc.).

### 2.1 GET `/collections`

| Field       | Value / Description               |     |
| ----------- | --------------------------------- | --- |
| **Returns** | `200 OK` → GetCollectionsResponse |     |
| **Auth**    | Required                          |     |

#### Example response

```jsonc
{
  "collections": [
    {
      "id": "3f9b1502-…",
      "name": "customer_faq",
      "description": "FAQ docs",
      "custom_config": { "chunk_size": 1800 },
      "created_at": "2025-05-29T14:54:23Z",
      "last_updated": "2025-05-30T08:11:02Z"
    }
  ],
  "user_info": {
    "upn": "<EMAIL>",
    "tenant": "contoso.onmicrosoft.com"
  }
}
```

---

### 2.2 POST `/collection`

| Field       | Value / Description                                              |
| ----------- | ---------------------------------------------------------------- |
| **Body**    | CollectionRequest                                                |
| **Returns** | `201 Created` → `{ "collection_id": "<uuid>", "message": "ok" }` |
| **Errors**  | `400` – duplicate name or invalid payload                        |

#### Payload model

```jsonc
{
  "name": "marketing_deck",
  "description": "Slide decks 2024–2025",
  "custom_config": {
    "llm_model": "gpt-4o",
    "chunk_size": 1800
  }
}
```

---

### 2.3 GET `/collection`

Retrieve a single collection by **ID** _or_ **name** (provide exactly one).

|Query param|Type|Notes|
|---|---|---|
|`collection_id`|UUID|optional (mutually exclusive)|
|`collection_name`|string|optional (mutually exclusive)|

**Returns**

- `200 OK` → `CollectionResponseItem`

- `400` – both params supplied

- `404` – not found


---

### 2.4 PATCH `/collection/{collection_id}`

| Field       | Value / Description                                     |
| ----------- | ------------------------------------------------------- |
| **Body**    | UpdateCollectionRequest (any subset of fields)          |
| **Returns** | `200 OK` → `{ "message": "ok" }`                        |
| **Notes**   | Renaming triggers a background rename in the Vector DB. |
| **Errors**  | `400` empty body · `404` unknown ID                     |

#### Sample payload

```jsonc
{
  "chunk_size": 1024,
  "chunk_overlap": 100,
  "is_web_search_on": true,
  "web_search_llm_model": "gpt35turbo"
}
```

---

### 2.5 DELETE `/collection/{collection_id}`

|Field|Value / Description|
|---|---|
|**Returns**|`200 OK` → `{ "message": "Collection deleted" }`|
|**Errors**|`404` not found · `500` database error|
|**Side-effect**|All documents inside the collection are removed.|

---

### 2.6 GET `/document/{document_id}/collection`

|Field|Value / Description|
|---|---|
|**Purpose**|Resolve the **collection** that owns a document.|
|**Returns**|`200 OK` → `CollectionResponseItem`|
|**Errors**|`404` document or collection missing|

---

## Document endpoints


|#|Endpoint|Verb|Brief|
|---|---|---|---|
|3.1|`/collection/{collection_id}/document`|**POST**|Upload one file|
|3.2|`/collection/{collection_id}/document/batch`|**POST**|Upload all files in a folder|
|3.3|`/collection/{collection_id}/document/{document_id}/reload`|**POST**|Re-queue a doc for parsing / indexing|
|3.4|`/collection/{collection_id}/document/{document_id}/status`|**GET**|Current processing status|
|3.5|`/collection/{collection_id}/document/{document_id}`|**GET**|Fetch doc (with collection guard)|
|3.6|`/document/{document_id}`|**GET**|Fetch doc (no collection guard)|
|3.7|`/collection/{collection_id}/documents`|**GET**|List all docs in a collection|
|3.8|`/collection/{collection_name}/document_ids`|**GET**|List doc IDs by collection **name**|
|3.9|`/get_content_from_file`|**POST**|Quick preview of uploaded file(s)|
|3.10|`/document/{document_id}`|**PATCH**|Update metadata / status|
|3.11|`/document/{document_id}`|**DELETE**|Delete a document|

---

### 3.1 POST `/collection/{collection_id}/document`

Upload a single file to a collection.

|Field|Details|
|---|---|
|**Form field**|`file` – `multipart/form-data`, required|
|**Query params**|`load_method` (optional) – `USE_LANGCHAIN` (default) · `USE_TESSERACT` · `USE_LANGCHAIN_AND_TESSERACT` · `USE_GPT_VISION`|
|**Body field**|`document_metadata` (stringified JSON, optional) – matches `UploadDocumentRequestMetadata`|
|**Returns**|`200 OK` → `{ "document_id": "<uuid>", "status": "ADDED" }`|
|**Errors**|`400` invalid payload · `404` collection not found · `500` storage/DB error|

---

### 3.2 POST `/collection/{collection_id}/document/batch`

Upload **all** files under a given folder path.

```jsonc
{
  "folder_path": "/data/invoices/*.pdf"
}
```

|Field|Details|
|---|---|
|**Returns**|`200 OK` → `{ "document_ids": ["…"], "message": "3 file(s) queued for load documents." }`|
|**Errors**|`400` invalid path · `404` no files · `500` DB error|

---

### 3.3 POST `/collection/{collection_id}/document/{document_id}/reload`

Re-queue a document for parsing or embedding.

|Field|Details|
|---|---|
|**Query param**|`load_method` (same values as upload)|
|**Returns**|`200 OK` → `{ "status": "queued" }`|
|**Errors**|`404` document not found · `400` status not reloadable|

---

### 3.4 GET `/collection/{collection_id}/document/{document_id}/status`

|Field|Details|
|---|---|
|**Returns**|`200 OK` → `{ "status": "READY_TO_BE_INDEXED" }` _(enum)_|
|**Errors**|`404` document or collection missing|

---

### 3.5 GET `/collection/{collection_id}/document/{document_id}`

### 3.6 GET `/document/{document_id}`

Fetch a document (the second route skips the collection check).

|Field|Details|
|---|---|
|**Returns**|`200 OK` → `GetDocumentResponse`|
|**Errors**|`404` not found|

`GetDocumentResponse` example:

```jsonc
{
  "document": {
    "id": "6a12…",
    "collection_id": "3f9b…",
    "file_path": "/data/…/invoice23.pdf",
    "document_metadata": { "source": "SAP" },
    "status": "READY_TO_BE_INDEXED",
    "created_at": "2025-05-28T11:07:01Z",
    "last_updated": "2025-05-28T11:09:55Z"
  }
}
```

---

### 3.7 GET `/collection/{collection_id}/documents`

List every document inside a collection.

|Field|Details|
|---|---|
|**Returns**|`200 OK` → `GetDocumentsResponse` (`documents` is an array of `DocumentResponseItem`)|

---

### 3.8 GET `/collection/{collection_name}/document_ids`

Quick lookup of document IDs by _collection name_ (no numeric metadata).

|Field|Details|
|---|---|
|**Returns**|`200 OK` → `{ "document_ids": ["…", "…"] }`|
|**Errors**|`404` name not found|

---

### 3.9 POST `/get_content_from_file`

Lightweight preview of uploaded file(s) — returns the first few pages /
rows as plain text.

|Field|Details|
|---|---|
|**Form field**|`files` – list of one or more `UploadFile` items|
|**Returns**|`200 OK` → `{ "message": "<concatenated preview text>" }`|
|**Errors**|`400` unsupported type / PDF page limit|

---

### 3.10 PATCH `/document/{document_id}`

Update metadata or status **after** upload.

_Only fields provided in the JSON body are updated._

```jsonc
{
  "status": "FAILED",
  "document_metadata": {
    "criticality": true,
    "summary": "Hand-written note, low OCR quality"
  }
}
```

|Field|Details|
|---|---|
|**Returns**|`200 OK` → `{ "message": "Document updated successfully" }`|
|**Errors**|`404` unknown ID · `500` DB error|

---

### 3.11 DELETE `/document/{document_id}`

Remove a document (and its chunks) from the system.

|Field|Details|
|---|---|
|**Optional query**|`collection_id` – extra guard; if supplied and mismatched the call fails|
|**Returns**|`200 OK` → `{ "message": "Document deleted successfully" }`|
|**Errors**|`404` unknown ID · `500` DB error|

---

**Enum reference**

|Enum|Possible values|
|---|---|
|`DocumentStatusEnum`|`ADDED`, `LOADING`, `READY_TO_BE_INDEXED`, `FAILED`|
|`LoadMethodEnum`|`USE_LANGCHAIN`, `USE_TESSERACT`, `USE_LANGCHAIN_AND_TESSERACT`, `USE_GPT_VISION`|

---

Paste this block under the previous Auth & Collection section and update the
main table of contents numbers if needed.

---

## URL endpoints


|#|Endpoint|Verb|Purpose|
|---|---|---|---|
|4.1|`/collection/{collection_id}/url`|**POST**|Upload one URL|
|4.2|`/collection/{collection_id}/url/batch`|**POST**|Upload multiple URLs|
|4.3|`/collection/{collection_id}/url/{url_id}/status`|**GET**|Check processing status|
|4.4|`/collection/{collection_id}/url/{url_id}`|**GET**|Fetch URL (with collection guard)|
|4.5|`/url/{url_id}`|**GET**|Fetch URL (no guard)|
|4.6|`/collection/by_name/{collection_name}/url/{url_id}`|**GET**|Fetch URL by collection **name**|
|4.7|`/collection/{collection_id}/urls`|**GET**|List all URLs in a collection|
|4.8|`/collection/{collection_name}/url_ids`|**GET**|List URL IDs by collection **name**|
|4.9|`/url/{url_id}/collection`|**GET**|Get owning collection of a URL|
|4.10|`/collection/{collection_id}/url/{url_id}`|**PATCH**|Update URL (guarded)|
|4.11|`/url/{url_id}`|**PATCH**|Update URL (unguarded)|
|4.12|`/collection/{collection_id}/url/{url_id}`|**DELETE**|Delete URL (guarded)|
|4.13|`/url/{url_id}`|**DELETE**|Delete URL (unguarded)|
|4.14|`/collection/{collection_id}/urls`|**DELETE**|Delete **all** URLs in a collection|

---

### 4.1 POST `/collection/{collection_id}/url`

Upload a single URL for scraping and indexing.

|Field|Details|
|---|---|
|**Body**|`{ "url": "<https://example.com/article>" }`|
|**Returns**|`200 OK` → `{ "url_id": "<uuid>", "status": "ADDED" }`|
|**Errors**|`400` scrape failed · `404` unknown collection · `500` DB error|

---

### 4.2 POST `/collection/{collection_id}/url/batch`

```jsonc
{
  "urls": [
    "https://blog.acme.com/post/1",
    "https://docs.acme.com/guide"
  ]
}
```

|Field|Details|
|---|---|
|**Returns**|`200 OK` → `{ "url_ids": ["…","…"], "message": "2 URL(s) uploaded." }`|
|**Errors**|`500` any scrape/store failure|

---

### 4.3 GET `/collection/{collection_id}/url/{url_id}/status`

|Field|Details|
|---|---|
|**Returns**|`200 OK` → `{ "status": "READY_TO_BE_INDEXED" }`|
|**Errors**|`404` not found|

---

### 4.4 GET `/collection/{collection_id}/url/{url_id}`

### 4.5 GET `/url/{url_id}`

### 4.6 GET `/collection/by_name/{collection_name}/url/{url_id}`

Fetch a stored URL and its scraped text.

|Field|Details|
|---|---|
|**Returns**|`200 OK` → `UrlItem`|
|**Errors**|`404` unknown URL|

`UrlItem` example (content truncated by service for >1000 chars):

```jsonc
{
  "id": "8d72…",
  "collection_id": "3f9b…",
  "url": "https://docs.acme.com/guide",
  "content": "This guide explains …",
  "status": "READY_TO_BE_INDEXED",
  "created_at": "2025-05-30T09:21:00Z",
  "last_updated": "2025-05-30T09:23:41Z"
}
```

---

### 4.7 GET `/collection/{collection_id}/urls`

Returns every URL (truncated content) inside a collection.

|Field|Details|
|---|---|
|**Returns**|`200 OK` → `GetUrlsResponse`|

---

### 4.8 GET `/collection/{collection_name}/url_ids`

|Field|Details|
|---|---|
|**Returns**|`200 OK` → `{ "url_ids": ["…","…"] }`|
|**Errors**|`404` collection name not found|

---

### 4.9 GET `/url/{url_id}/collection`

Resolve the collection that owns a URL.

|Field|Details|
|---|---|
|**Returns**|`200 OK` → `CollectionResponseItem`|
|**Errors**|`404` unknown URL / collection|

---

### 4.10 PATCH `/collection/{collection_id}/url/{url_id}`

### 4.11 PATCH `/url/{url_id}`

Update URL status or content.

```jsonc
{
  "status": "FAILED",
  "content": "Manual override text"
}
```

|Field|Details|
|---|---|
|**Returns**|`200 OK` → `{ "message": "updated" }`|
|**Errors**|`404` unknown URL · `500` DB error|

---

### 4.12 DELETE `/collection/{collection_id}/url/{url_id}`

### 4.13 DELETE `/url/{url_id}`

|Field|Details|
|---|---|
|**Returns**|`200 OK` → `{ "message": "ok" }`|
|**Errors**|`404` not found|

The service enqueues a delete event; the record disappears when the worker
confirms removal.

---

### 4.14 DELETE `/collection/{collection_id}/urls`

Remove **every** URL in a collection.

|Field|Details|
|---|---|
|**Returns**|`200 OK` → `{ "message": "All URLs for collection <id> deleted successfully" }`|
|**Errors**|`500` DB error|

---

### Enum Cheat-Sheet

|Enum|Values|
|---|---|
|`DocumentStatusEnum`|`ADDED`, `LOADING`, `READY_TO_BE_INDEXED`, `FAILED`|

---

© 2025 Fbeta
