# Core Dependencies
wheel==0.45.1        # A package for building and distributing Python wheels (binary distributions).
setuptools==76.0.0   # A package management tool for installing, upgrading, and managing Python packages.


# Database and ORM
sqlalchemy==2.0.39            # SQL toolkit and ORM for Python, used for interacting with databases.
sqlalchemy-utils==0.41.2      # Additional utilities and extensions for SQLAlchemy, such as custom data types and validation.
databases[postgresql]==0.9.0  # Async database library for PostgreSQL, compatible with SQLAlchemy.
pgvector==0.3.6               # PostgreSQL extension for storing and querying vector embeddings.
redis==5.2.1                  # Redis client for Python, used for caching and real-time data processing.
psycopg[binary]==3.2.4      # PostgreSQL database adapter for Python, used to connect and interact with PostgreSQL.
alembic==1.13.1               # Database migration tool for SQLAlchemy.




# Web Frameworks and API
fastapi[all]==0.115.11         # High-performance web framework for building APIs with Python.
starlette==0.46.1              # Lightweight ASGI framework used by FastAPI for request handling.
uvicorn==0.34.0                # ASGI server for running FastAPI applications.
fastapi-azure-auth==5.1.0      # Azure AD authentication integration for FastAPI.
starlette-context==0.4.0       # Middleware for managing request-scoped context in Starlette/FastAPI.
requests==2.32.3               # Popular HTTP library for making API requests in Python.
httpx==0.28.1                  # Asynchronous HTTP client for Python, supporting HTTP/2 and connection pooling.


# Logging and Background Tasks
aiologger==0.7.0                 # Asynchronous logging library for Python.
dramatiq[rabbitmq, watch]==1.17.1 # Distributed task processing library with support for RabbitMQ.
apscheduler==3.11.0               # Advanced scheduling library for running background dramatiq_tasks.


# Data Processing and Machine Learning
pandas==2.2.3            # Data analysis and manipulation library for structured data.
numpy==1.26.4            # Numerical computing library for arrays, matrices, and mathematical operations.
scikit-learn==1.6.1      # Machine learning library for classification, regression, and clustering.
networkx[all]==3.4.2     # Library for creating, analyzing, and visualizing graphs and networks.
mlflow==2.20.3           # Machine learning lifecycle management platform for tracking and deploying models.
transformers==4.49.0     # Hugging Face library for NLP models like BERT, GPT, and T5.
torch==2.2.2             # Deep learning framework for building and training neural networks (PyTorch).


# Natural Language Processing (NLP)
#spacy==3.8.3                   # NLP library for tokenization, named entity recognition, and linguistic processing.
nltk==3.8.1                     # Natural language processing toolkit for tokenization, stemming, and parsing.
azure-ai-textanalytics==5.3.0   # Microsoft Azure Text Analytics API client for NLP tasks like sentiment analysis and entity recognition.


# LangChain and AI-related Libraries
langchain==0.3.25            # Main LangChain package for building LLM-based applications.
langchain-openai==0.3.10      # OpenAI integration for LangChain, supporting LLMs and embeddings.
langchain-community==0.3.21  # Community modules for LangChain, including integrations with databases and vector stores.
langchain-core==0.3.58       # Core components of LangChain for chaining operations and data handling.
langchain-experimental==0.3.4 # Experimental features and early-stage integrations for LangChain.
langchain-postgres==0.0.14   # PostgreSQL integration for LangChain, enabling vector storage and retrieval.
openai==1.78.1            # OpenAI API client for accessing GPT-4, GPT-3.5, DALL·E, and other services.
tiktoken==0.9.0              # OpenAI's tokenizer library for counting tokens and estimating costs.

# File and Document Processing
bs4==0.0.2                      # Wrapper for BeautifulSoup, used for web scraping and parsing HTML/XML.
pypdf==5.3.1                    # Library for reading, merging, and modifying PDF files.
docx2txt==0.8                   # Extracts text from .docx files while preserving formatting.
python-docx==1.1.2              # Library for creating, modifying, and reading Microsoft Word (.docx) documents.
unstructured[all-docs]==0.16.25 # Library for parsing and processing various document formats (PDF, DOCX, etc.).
unstructured-inference==0.8.9   # Extracts structured information from unstructured documents using AI.
pdf2image==1.17.0               # Converts PDF pages into images using poppler.
pdfminer.six==20231228          # Extracts text and metadata from PDFs with better layout preservation.
pillow_heif==0.9.0            # HEIF/HEIC image support for Pillow, used for handling Apple image formats.
opencv_python==*********        # OpenCV library for computer vision, image processing, and video analysis.
pytesseract==0.3.13             # OCR (Optical Character Recognition) tool that uses Google's Tesseract engine.


# Visualization and Plotting
matplotlib==3.10.1   # Visualization library for creating static, animated, and interactive plots.
plotly==6.0.0        # Interactive graphing library for creating web-based plots and dashboards.
scipy==1.15.2        # Scientific computing library for optimization, signal processing, and statistics.
pyvis==0.3.2         # Library for creating and visualizing network graphs in an interactive way.


# OCR and Image Processing
rapidocr-onnxruntime==1.4.4  # OCR engine based on ONNX Runtime for fast text extraction from images.
pdfplumber==0.11.5           # PDF processing library for extracting text, tables, and metadata from PDFs.
fpdf==1.7.2                  # Lightweight library for generating PDF documents in Python.
Pillow==10.3.0                  # Python Imaging Library (PIL) fork for image processing and manipulation.


# Markdown and Document Generation
markdown2==2.5.3     # Library for converting Markdown text to HTML.
python-pptx==1.0.2   # Library for creating, modifying, and reading PowerPoint (.pptx) presentations.
PyMuPDF==1.25.3      # PDF and image processing library with fast text extraction and rendering capabilities.


# Web Scraping and Automation
playwright==1.50.0         # Web automation library for browser interaction, web scraping, and testing.
beautifulsoup4==4.13.3     # HTML and XML parsing library for web scraping and data extraction.


# Testing and Code Quality
pytest==8.3.5                 # Testing framework for writing and executing unit tests in Python.
pytest-cov==6.0.0             # Pytest plugin for measuring test coverage using coverage.py.
pytest_mock==3.14.0           # Pytest plugin for easily using unittest.mock in tests.
pytest_postgresql==7.0.0      # Pytest plugin for setting up and managing PostgreSQL databases in tests.
ruff==0.9.10                  # Fast Python linter and formatter for code quality checks.
mypy==1.15.0                  # Static type checker for Python to enforce type annotations.
mypy-extensions==1.0.0        # Additional extensions and utilities for mypy type checking.
types-requests==2.32.0.20250306 # Type hints for the requests library, useful for mypy static analysis.


# Time and Scheduling
pytz==2025.1  # Library for handling and converting time zones in Python.

asyncpg>=0.29
aio-pika>=9.4
msal
