# Postgres details
POSTGRES_HOST_EMBEDDING=postgres_loader
POSTGRES_PORT=5432
POSTGRES_DB=postgres_loader
POSTGRES_USER=postgres_loader
POSTGRES_PASSWORD=postgres_loader
DB_URL=postgresql+psycopg://postgres_loader:postgres_loader@postgres_loader:5432/postgres_loader
POSTGRES_HOST_AUTH_METHOD=trust

# Local dev settings
LOCAL_DEV=True
IS_DEBUG=False
IS_ENTRA_ID_AUTH_ON=True

# App, Redis, Postgres, and Notebook details
API_PORT=8001
LOG_LEVEL=ERROR
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_URL=redis://redis:6379
REDIS_DRAMATIQ_URL=redis://redis:6379/0
REDIS_EMBEDDING_URL=redis://redis:6379/1
REDIS_QUERY_HISTORY_URL=redis://redis:6379/2

# TMP
PDFS_DATA_DIR="/data/tmp"


# Chat&Embedding APIs
#OPENAI_API_TYPE="azure"
OPENAI_API_VERSION="2023-12-01-preview"
AZURE_OPENAI_ENDPOINT=""
OPENAI_API_KEY=''
OPENSOURCE_LLM_API_BASE=""
OPENSOURCE_LLM_API_KEY=""

# Azure User Auth
APP_CLIENT_ID=
APP_CLIENT_SECRET=
TENANT_ID=
OPENAPI_CLIENT_ID=

# Azure Service Auth
SERVICE_APP_CLIENT_ID=
SERVICE_APP_CLIENT_SECRET=
SERVICE_TENANT_ID=

# rabbitmq
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
DRAMATIQ_QUEUE_PREFIX=local

# Sharepoint
AZURE_TENANT_ID=""
SHAREPOINT_RESOURCE_URL=""
SHAREPOINT_CLIENT_ID=""
SHAREPOINT_CLIENT_SECRET=""
