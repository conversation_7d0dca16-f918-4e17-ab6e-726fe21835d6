############################
# 1. Builder stage
############################
FROM python:3.11-slim AS builder
WORKDIR /src

# System dependencies required only while building wheels
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential gcc \
 && rm -rf /var/lib/apt/lists/*

COPY LoaderService/requirements.txt .
RUN --mount=type=cache,target=/root/.cache/pip \
    pip wheel -r requirements.txt --wheel-dir /wheels

############################
# 2. Runtime stage
############################
FROM python:3.11-slim AS runtime
WORKDIR /app

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    UVICORN_ACCESS_LOG=false \
    PYTHONPATH=/app

# System dependencies including Playwright requirements
RUN apt-get update \
 && apt-get install -y \
    curl ca-certificates gnupg \
    libnss3 libatk1.0-0 libatk-bridge2.0-0 libcups2 libxkbcommon0 \
    libxcomposite1 libxdamage1 libxrandr2 libgbm1 libasound2 \
    libpangocairo-1.0-0 libgtk-3-0 poppler-utils tesseract-ocr \
    libmagic-dev libmupdf-dev libreoffice pandoc \
    libxml2-dev libxslt1-dev libpq-dev libgeos-dev \
    libjpeg-dev libpng-dev zlib1g-dev libfreetype6-dev libgeos-c1v5 \
    # Additional dependencies for Playwright/Chromium
    fonts-liberation libappindicator3-1 libnspr4 libnss3 \
    xdg-utils libxss1 libxtst6 xvfb \
 && apt-get clean \
 && rm -rf /var/lib/apt/lists/*

# Python dependencies
COPY --from=builder /wheels /wheels
RUN pip install --no-index --find-links=/wheels /wheels/* \
 && pip install --no-cache-dir playwright \
 && rm -rf /wheels \
 && if [ -d /usr/local/lib/python3.11/site-packages/mlflow/server/js/build/static/js ]; then \
      find /usr/local/lib/python3.11/site-packages/mlflow/server/js/build/static/js \
           -type f -name '*.map' -delete; \
    fi

# In order to run application as non-root user
RUN useradd -m appuser

# Download Chromium for appuser without additional dependencies
RUN mkdir -p /home/<USER>/.cache/ms-playwright \
 && chown -R appuser:appuser /home/<USER>/.cache \
 && HOME=/home/<USER>/home/<USER>/.cache/ms-playwright \
    playwright install chromium

# Copy application code
COPY --chown=appuser LoaderService /app

# Go to non-root user
USER appuser

# Port
EXPOSE 8002

# Healthcheck
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s \
  CMD curl -f http://localhost:8002/health || exit 1

# Uvicorn server command
CMD ["sh", "-c", "if [ \"$LOCAL_DEV\" = \"True\" ]; then \
                    uvicorn main:app --host 0.0.0.0 --port 8002 --reload; \
                  else \
                    uvicorn main:app --host 0.0.0.0 --port 8002; \
                  fi"]
