# LoaderService/tests/conftest.py

import sys
from types import ModuleType
from unittest.mock import MagicMock

import pytest


@pytest.fixture(autouse=True, scope="session")
def patch_message_broker_modules():
    """
    Automatically patch `loader_app.tasks` and submodules to avoid Redis/RabbitMQ init during test discovery.
    """
    dummy_tasks_module = ModuleType("loader_app.tasks")
    dummy_tasks_module.rabbitmq_broker = MagicMock()
    dummy_tasks_module.redis_conn = MagicMock()
    dummy_tasks_module.QUEUE_PREFIX = "test"
    dummy_tasks_module.message_broker = MagicMock()

    sys.modules["loader_app.tasks"] = dummy_tasks_module
    sys.modules["loader_app.tasks.rabbitmq_broker"] = dummy_tasks_module.rabbitmq_broker
    sys.modules["loader_app.tasks.url"] = MagicMock()
    sys.modules["loader_app.tasks.document"] = MagicMock()

    yield

    # Cleanup after test session
    for mod in [
        "loader_app.tasks",
        "loader_app.tasks.rabbitmq_broker",
        "loader_app.tasks.url",
        "loader_app.tasks.document",
    ]:
        sys.modules.pop(mod, None)
