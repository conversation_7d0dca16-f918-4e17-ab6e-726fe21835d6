import importlib
from io import BytesIO
from unittest.mock import MagicMock, mock_open, patch
from uuid import uuid4

import pytest
from fastapi import UploadFile
from loader_app.api.models.request import UploadDocumentRequestMetadata
from loader_app.api.utils.exceptions import (
    FileStorageError,
    InvalidFolderPathError,
    NoFilesFoundError,
)
from loader_app.enums.loader import LoadMethodEnum


class TestProcessAndStoreDocument:
    """
    Unit tests for the `process_and_store_document` function.
    External services like Redis and RabbitMQ are mocked using patching.
    """

    @pytest.mark.asyncio
    @patch("loader_app.crud.document.insert_document")
    @patch("loader_app.api.core.document.loader_move_document_forward.send")
    @patch("loader_app.api.core.document.os.path.exists", return_value=True)
    @patch("loader_app.api.core.document.open", new_callable=mock_open)
    @patch("loader_app.api.core.document.os.makedirs")
    async def test_should_process_and_store_document(
        self,
        mock_makedirs,
        mock_file_open,
        mock_exists,
        mock_send,
        mock_insert_document,
    ):
        """It should process a document and return the inserted document ID."""
        import loader_app.api.core.document as document_core

        importlib.reload(document_core)

        collection_id = uuid4()
        document_id = uuid4()
        metadata = UploadDocumentRequestMetadata()
        auth = {"user": "test"}
        load_method = LoadMethodEnum.USE_LANGCHAIN

        mock_insert_document.return_value = document_id

        upload_file = UploadFile(file=BytesIO(b"Test file content"), filename="test.docx")
        db_mock = MagicMock()

        result = await document_core.process_and_store_document(
            db=db_mock,
            collection_id=collection_id,
            file=upload_file,
            metadata=metadata,
            auth=auth,
            load_method=load_method,
        )

        assert result == document_id
        mock_insert_document.assert_called_once()
        mock_send.assert_called_once()

    @pytest.mark.asyncio
    @patch("loader_app.api.core.document.insert_document")
    @patch("loader_app.api.core.document.loader_move_document_forward.send")
    @patch("loader_app.api.core.document.os.path.exists", return_value=False)
    @patch("loader_app.api.core.document.open", new_callable=mock_open)
    @patch("loader_app.api.core.document.os.makedirs")
    async def test_should_raise_error_when_file_not_saved(
        self,
        mock_makedirs,
        mock_file_open,
        mock_exists,
        mock_send,
        mock_insert_document,
    ):
        """It should raise FileStorageError if file save fails or doesn't exist."""
        import loader_app.api.core.document as document_core

        importlib.reload(document_core)

        # Simulate file write returning None but path.exists is False
        mock_file_open.return_value.__enter__.return_value.write.return_value = None

        collection_id = uuid4()
        metadata = UploadDocumentRequestMetadata()
        auth = {"user": "test"}
        load_method = LoadMethodEnum.USE_LANGCHAIN

        upload_file = UploadFile(file=BytesIO(b"dummy content"), filename="fail.pdf")
        db_mock = MagicMock()

        with pytest.raises(FileStorageError):
            await document_core.process_and_store_document(
                db=db_mock,
                collection_id=collection_id,
                file=upload_file,
                metadata=metadata,
                auth=auth,
                load_method=load_method,
            )


class TestProcessBatchDocuments:
    """Unit tests for `process_batch_documents`."""

    @pytest.mark.asyncio
    @patch("loader_app.api.core.document.process_and_store_document")
    @patch("loader_app.api.core.document.open", new_callable=mock_open, read_data=b"test content")
    @patch("loader_app.api.core.document.os.path.isfile", return_value=True)
    @patch("loader_app.api.core.document.glob", return_value=["/some/path/file1.pdf", "/some/path/file2.pdf"])
    async def test_should_process_files_in_folder(
        self,
        mock_glob,
        mock_isfile,
        mock_open_file,
        mock_process_doc,
    ):
        """It should process multiple valid files from the folder."""
        import loader_app.api.core.document as document_core

        db_mock = MagicMock()
        auth = {"user": "test"}
        collection_id = uuid4()
        fake_ids = [uuid4(), uuid4()]
        mock_process_doc.side_effect = fake_ids

        result = await document_core.process_batch_documents(
            db=db_mock,
            auth=auth,
            collection_id=collection_id,
            folder_path="/some/path/*.pdf",
        )

        assert result == fake_ids
        assert mock_process_doc.call_count == 2

    @pytest.mark.asyncio
    @patch("loader_app.api.core.document.glob", side_effect=Exception("fail"))
    async def test_should_raise_invalid_folder_path(self, mock_glob):
        """It should raise InvalidFolderPathError on bad glob pattern."""
        import loader_app.api.core.document as document_core

        with pytest.raises(InvalidFolderPathError):
            await document_core.process_batch_documents(
                db=MagicMock(),
                auth={},
                collection_id=uuid4(),
                folder_path="bad[pattern",
            )

    @pytest.mark.asyncio
    @patch("loader_app.api.core.document.glob", return_value=[])
    async def test_should_raise_no_files_found(self, mock_glob):
        """It should raise NoFilesFoundError when folder is empty."""
        import loader_app.api.core.document as document_core

        with pytest.raises(NoFilesFoundError):
            await document_core.process_batch_documents(
                db=MagicMock(),
                auth={},
                collection_id=uuid4(),
                folder_path="/empty/folder/*.pdf",
            )
