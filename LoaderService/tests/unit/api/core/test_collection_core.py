from unittest.mock import patch
from uuid import uuid4

import pytest
from fastapi import HTT<PERSON>Exception
from loader_app.api.core.collection import verify_collection_exists
from loader_app.database.models import Collection, Document


class TestVerifyCollectionExists:
    """
    Unit tests for the `verify_collection_exists` function using patching of CRUD functions.
    """

    @patch("loader_app.api.core.collection.get_collection")
    def test_should_return_collection_by_id(self, mock_get_collection):
        """It should return the correct collection when queried by collection_id."""
        collection_id = uuid4()
        mock_get_collection.return_value = Collection(id=collection_id, name="mock")

        result = verify_collection_exists(db=None, collection_id=collection_id)

        assert result.id == collection_id

    @patch("loader_app.api.core.collection.get_collection")
    def test_should_return_collection_by_name(self, mock_get_collection):
        """It should return the correct collection when queried by collection_name."""
        mock_get_collection.return_value = Collection(id=uuid4(), name="my-collection")

        result = verify_collection_exists(db=None, collection_name="my-collection")

        assert result.name == "my-collection"

    @patch("loader_app.api.core.collection.get_collection")
    @patch("loader_app.api.core.collection.get_document")
    def test_should_return_collection_by_document_id(self, mock_get_document, mock_get_collection):
        """It should return the parent collection when queried by document_id."""
        collection_id = uuid4()
        document_id = uuid4()

        mock_get_document.return_value = Document(id=document_id, collection_id=collection_id)
        mock_get_collection.return_value = Collection(id=collection_id, name="linked")

        result = verify_collection_exists(db=None, document_id=document_id)

        assert result.id == collection_id

    def test_should_raise_404_when_no_identifier_provided(self):
        """It should raise a 404 error if no identifiers are given."""
        with pytest.raises(HTTPException) as exc_info:
            verify_collection_exists(db=None)

        assert exc_info.value.status_code == 404
        assert "Collection not found" in str(exc_info.value.detail)

    @patch("loader_app.api.core.collection.get_document")
    def test_should_raise_404_for_invalid_document(self, mock_get_document):
        """It should raise a 404 error if document_id is provided but document is not found."""
        mock_get_document.return_value = None

        with pytest.raises(HTTPException) as exc_info:
            verify_collection_exists(db=None, document_id=uuid4())

        assert exc_info.value.status_code == 404
        assert "Collection not found" in str(exc_info.value.detail)
