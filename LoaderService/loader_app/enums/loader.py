from enum import Enum


class LoadMethodEnum(str, Enum):
    """Enum for different document loading methods."""

    USE_TESSERACT = "use_tesseract"
    USE_LANGCHAIN_AND_TESSERACT = "use_langchain_and_tesseract"
    USE_LANGCHAIN = "use_langchain"
    USE_GPT_VISION = "use_gpt_vision"


class ChunkingStrategy(str, Enum):
    """Supported chunking strategies (exported as a `str` subclass for JSON-ability)."""

    RECURSIVE = "recursive"
    TOKEN = "token"
    HEADLINE = "headline"
    MARKDOWN = "markdown"
