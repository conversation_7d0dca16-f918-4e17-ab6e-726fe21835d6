from enum import Enum

# ------------------------------------------------------------------
# Document Enums
# ------------------------------------------------------------------


class DocumentTypeEnum(str, Enum):
    """Enum for different types of documents that can be loaded."""

    DOCUMENT = "document"
    URL = "url"


class DocumentStatusEnum(str, Enum):
    """Enum for different statuses of a document during the loading and indexing process."""

    ADDED = "added"
    LOADING = "loading"
    READY_TO_BE_INDEXED = "ready_to_be_indexed"
    EXTRACTING_EMBEDDINGS = "extracting_embeddings"
    INDEXED = "indexed"
    FAILED = "failed"
    DELETED = "deleted"


class DocumentEventsEnum(str, Enum):
    """Enum for different events related to document processing."""

    DELETE_REQUEST = "delete_request"
    LOAD_REQUEST = "loading_request"
    LOAD_FINISHED = "loading_finished"
    LOAD_FAILED = "loading_failed"
    EMBEDDING_REQUEST = "embedding_request"
    EMBEDDING_FINISHED = "embedding_finished"
    EMBEDDING_FAILED = "embedding_failed"
