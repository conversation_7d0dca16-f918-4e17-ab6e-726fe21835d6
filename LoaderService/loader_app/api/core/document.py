from __future__ import annotations

import csv
import io
import logging
import os
import re
from glob import glob
from typing import Any
from uuid import UUID

from docx import Document as DocxDocument
from dramatiq import Message
from fastapi import UploadFile
from loader_app.api.models.request import UploadDocumentRequestMetadata
from loader_app.api.utils.exceptions import (
    DocumentNotFoundError,
    FileStorageError,
    InvalidDocumentStatusError,
    InvalidFolderPathError,
    NoFilesFoundError,
    PDFPageLimitExceededError,
    UnsupportedFileTypeError,
)
from loader_app.api.utils.helpers import create_headers
from loader_app.crud.document import get_document, insert_document
from loader_app.enums.document import DocumentEventsEnum, DocumentStatusEnum
from loader_app.enums.loader import LoadMethodEnum
from loader_app.settings import settings
from loader_app.tasks import rabbitmq_broker
from loader_app.tasks.document import loader_move_document_forward
from openpyxl import load_workbook
from pypdf import PdfReader
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


async def process_and_store_document(
    db: Session,
    collection_id: UUID,
    file: UploadFile,
    metadata: UploadDocumentRequestMetadata,
    auth: Any,
    load_method: LoadMethodEnum,
) -> UUID:
    """Processes a document file, saves it, and initiates processing pipeline."""

    # File
    base_dir = os.path.join(settings.pdfs_data_dir, str(collection_id))
    os.makedirs(base_dir, exist_ok=True)
    logger.info("*****loader service base_dir: %s", base_dir)

    file_path = os.path.join(base_dir, file.filename)
    logger.info("*****loader service file_path: %s", file_path)

    try:
        # Reading process is from starting point
        await file.seek(0)

        # File is saved
        with open(file_path, "wb") as fh:
            fh.write(await file.read())

        # Be sure that file exist
        if not os.path.exists(file_path):
            raise FileStorageError("File not found after save")

        logger.info("*****loader service File successfully written: %s", file_path)
        logger.info("*****loader service File size: %d bytes", os.path.getsize(file_path))

    except Exception as e:
        logger.exception("*****loader service File write failed")
        raise FileStorageError("File write failed") from e

    # Record it to db
    doc_id = insert_document(
        db,
        collection_id=collection_id,
        file_path=file_path,
        document_metadata=metadata.model_dump(),
    )

    # Dramatiq task
    loader_move_document_forward.send(
        str(doc_id),
        DocumentEventsEnum.LOAD_REQUEST.value,
        load_method=load_method,
        headers=create_headers(auth),
    )

    return doc_id


async def process_batch_documents(
    db: Session,
    auth: Any,
    collection_id: UUID,
    folder_path: str,
) -> list[UUID]:
    """Processes all files in a given folder, extracts their content, and stores them in the database."""
    try:
        paths = glob(folder_path)
    except Exception:
        raise InvalidFolderPathError("Invalid folder path")

    if not paths:
        raise NoFilesFoundError("No files found in given folder")

    document_ids: list[UUID] = []

    for path in paths:
        if not os.path.isfile(path):
            logger.warning(f"Skipping non-file: {path}")
            continue

        filename = os.path.basename(path)
        try:
            with open(path, "rb") as f:
                upload_file = UploadFile(filename=filename, file=io.BytesIO(f.read()))
        except Exception as e:
            logger.warning(f"Unable to read file {path}: {e}")
            continue

        try:
            document_id = await process_and_store_document(
                db=db,
                collection_id=collection_id,
                file=upload_file,
                metadata=UploadDocumentRequestMetadata(),
                auth=auth,
                load_method=LoadMethodEnum.USE_LANGCHAIN,
            )
            document_ids.append(document_id)
        except Exception as e:
            logger.exception(f"Failed to process {path}: {e}")

    return document_ids


async def process_reload_document(
    db: Session,
    auth: Any,
    collection_id: UUID,
    document_id: UUID,
    load_method: LoadMethodEnum,
) -> None:
    """Reloads a document by checking its status and sending it to the appropriate task."""

    doc = get_document(db, collection_id=collection_id, document_id=document_id)
    if not doc:
        raise DocumentNotFoundError()

    status = doc.status

    if status in {DocumentStatusEnum.ADDED.value, DocumentStatusEnum.FAILED.value}:
        loader_move_document_forward.send(
            str(document_id),
            DocumentEventsEnum.LOAD_REQUEST.value,
            load_method=load_method,
            headers=create_headers(auth),
        )
    elif status == DocumentStatusEnum.READY_TO_BE_INDEXED.value:
        push_to_indexer(document_id, DocumentEventsEnum.EMBEDDING_REQUEST.value)
    else:
        raise InvalidDocumentStatusError("Document not in a reloadable status")


def push_to_indexer(document_id: UUID, event: str) -> None:
    """Publish a message to the Indexer queue without importing its code."""
    rabbitmq_broker.enqueue(
        Message(
            queue_name=f"{settings.dramatiq_queue_prefix}.indexer.document_move_queue",
            actor_name="indexer_move_document_forward",
            args=(str(document_id), event),
            kwargs={},
            options={},
        )
    )

    logger.info("Sent %s event for document %s to Indexer queue", event, document_id)


async def parse_file(file: UploadFile) -> str:
    """Parses the content of a file based on its extension and returns the text content."""
    filename = file.filename
    ext = os.path.splitext(filename)[1].lower()
    data = await file.read()
    stream = io.BytesIO(data)

    if ext == ".pdf":
        reader = PdfReader(stream)
        if len(reader.pages) > 5:
            raise PDFPageLimitExceededError("Too many pages in PDF file")
        return "".join(re.sub(r"\s+", " ", page.extract_text() or "") for page in reader.pages)

    elif ext == ".docx":
        doc = DocxDocument(stream)
        return "\n".join(p.text for p in doc.paragraphs if p.text.strip())

    elif ext == ".xlsx":
        wb = load_workbook(stream, read_only=True)
        rows = []
        for sheet in wb.worksheets:
            for row in sheet.iter_rows(values_only=True):
                rows.append(" ".join(str(cell) for cell in row if cell is not None))
        return " ".join(rows)

    elif ext == ".csv":
        txt = data.decode("utf-8", errors="ignore")
        reader = csv.reader(io.StringIO(txt))
        rows = [" ,".join(r) for r in reader]
        return " ".join(rows)

    raise UnsupportedFileTypeError(f"Unsupported file format for {filename}")
