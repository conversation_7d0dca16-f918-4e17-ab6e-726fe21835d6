"""
Collection access helpers.

* ``verify_collection_exists`` – pure 404 check.
* ``verify_collection_access`` – 404 + RBAC check, with optional “empty-list” mode.
"""

from __future__ import annotations

import logging
from typing import Any, Optional
from uuid import UUID

from fastapi import HTTPException
from loader_app.crud.collection import get_collection
from loader_app.crud.document import get_document
from loader_app.database.models import Collection
from loader_app.services.rbac import (
    PRIORITY,
    Role,
    can_user_see,
    get_entra_auth_user_info,
)
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


# ---------------------------------------------------------------------------#
# Existence check (404 only)
# ---------------------------------------------------------------------------#
def verify_collection_exists(
    db: Session,
    collection_id: Optional[UUID] = None,
    collection_name: Optional[str] = None,
    document_id: Optional[UUID] = None,
) -> Collection:
    """Return a collection or raise **404** if it cannot be found."""
    if collection_name:
        coll = get_collection(db, collection_name=collection_name)
    elif collection_id:
        coll = get_collection(db, collection_id=collection_id)
    elif document_id:
        doc = get_document(db, document_id=document_id)
        coll = get_collection(db, collection_id=doc.collection_id) if doc else None
    else:
        raise HTTPException(status_code=404, detail="Collection not found.")

    if not coll:
        raise HTTPException(status_code=404, detail="Collection not found.")

    return coll


# ---------------------------------------------------------------------------#
# Existence + RBAC
# ---------------------------------------------------------------------------#
def verify_collection_access(
    db: Session,
    auth: Any,
    *,
    # pass-through identifiers
    collection_id: Optional[UUID] = None,
    collection_name: Optional[str] = None,
    document_id: Optional[UUID] = None,
    # behaviour
    on_forbidden: str = "raise",  # "raise" | "empty"
) -> Optional[Collection]:
    """
    Ensure the authenticated user (*auth*) may access the collection.

    Parameters
    ----------
    on_forbidden
        ``"raise"``  → insufficient role triggers **HTTP 403**
        ``"empty"``  → return ``None`` so the caller can respond
        with an *empty list* (e.g. documents endpoint).
    db: Session
        Database session.
    auth: Any
        Authentication object, usually containing the JWT token.
    collection_id: Optional[UUID]
        Collection ID to check.
    collection_name: Optional[str]
        Collection name to check.
    document_id: Optional[UUID]
        Document ID to check (used to find the collection).

    Returns
    -------
    *Collection* when access is permitted.
    ``None`` only when ``on_forbidden == "empty"`` **and** the user lacks access.

    Raises
    ------
    HTTPException(404)
        The collection does not exist.
    HTTPException(403)
        The user’s role is too low and *on_forbidden == "raise"*.
    """
    # 1) Existence check (404)
    coll = verify_collection_exists(
        db,
        collection_id=collection_id,
        collection_name=collection_name,
        document_id=document_id,
    )

    # 2) Determine user’s highest role
    user_info, _ = get_entra_auth_user_info(auth)
    roles = user_info.get("roles", ["basic"])
    user_role: str = max(roles, key=lambda r: PRIORITY.get(Role.from_str(r), 1))

    # 3) RBAC evaluation
    coll_role = (coll.custom_config or {}).get("role")  # None ⇒ treated as "basic"
    logger.info(f"user_role: {user_role}")
    logger.info(f"coll_role: {coll_role}")
    logger.info(f"can user see: {can_user_see(user_role, coll_role)}")
    if can_user_see(user_role, coll_role):
        return coll  # Access granted

    # Access denied
    if on_forbidden == "empty":
        logger.info(
            "Access denied – returning None " "(user_role=%s, coll_role=%s, collection_id=%s)",
            user_role,
            coll_role,
            coll.id,
        )
        return None

    raise HTTPException(
        status_code=403,
        detail="Not enough privileges to access this collection.",
    )
