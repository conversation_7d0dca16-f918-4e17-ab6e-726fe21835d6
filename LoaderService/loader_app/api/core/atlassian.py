"""Core utilities for Atlassian Jira and Confluence integration."""

import logging
from typing import Dict, List, Optional

from loader_app.services.atlassian import atlassian_service

logger = logging.getLogger(__name__)


async def authenticate_atlassian(base_url: str, username: str, api_token: str) -> bool:
    """
    Authenticate with Atlassian.

    Args:
        base_url: Atlassian instance base URL
        username: User email address
        api_token: API token from Atlassian

    Returns:
        bool: True if authentication successful, False otherwise
    """
    try:
        success = await atlassian_service.authenticate(base_url, username, api_token)
        if success:
            logger.info(f"Successfully authenticated with Atlassian: {base_url}")
        else:
            logger.error(f"Failed to authenticate with Atlassian: {base_url}")
        return success
    except Exception as e:
        logger.exception(f"Error during Atlassian authentication: {e}")
        return False


async def get_jira_projects() -> List[Dict]:
    """
    Get all accessible Jira projects.

    Returns:
        List of project dictionaries
    """
    try:
        projects = await atlassian_service.get_jira_projects()
        logger.info(f"Retrieved {len(projects)} Jira projects")
        return projects
    except Exception as e:
        logger.exception(f"Error fetching Jira projects: {e}")
        return []


async def get_jira_issues(project_key: str, max_results: int = 10000, issue_keys: Optional[List[str]] = None) -> List[Dict]:
    """
    Get issues from a specific Jira project.

    Args:
        project_key: Project key (e.g., 'PROJ')
        max_results: Maximum number of issues to return, defaults to 10000 to fetch all
        issue_keys: Specific issue keys to fetch (optional)

    Returns:
        List of issue dictionaries
    """
    try:
        if issue_keys:
            # If specific issue keys are provided, fetch each one individually
            issues = []
            for issue_key in issue_keys:
                issue_content = await atlassian_service.get_jira_issue_content(issue_key)
                if issue_content:
                    # Parse the content to extract issue details
                    lines = issue_content.split("\n")
                    issue_data = {"key": issue_key, "content": issue_content, "project_key": project_key}

                    # Extract basic info from formatted content
                    for line in lines:
                        if line.startswith("Summary: "):
                            issue_data["summary"] = line.replace("Summary: ", "")
                        elif line.startswith("Status: "):
                            issue_data["status"] = line.replace("Status: ", "")
                        elif line.startswith("Type: "):
                            issue_data["issue_type"] = line.replace("Type: ", "")

                    issues.append(issue_data)
            return issues
        else:
            # Fetch all issues from the project
            issues = await atlassian_service.get_jira_issues(project_key, max_results)
            logger.info(f"Retrieved {len(issues)} Jira issues from project {project_key}")
            return issues
    except Exception as e:
        logger.exception(f"Error fetching Jira issues for project {project_key}: {e}")
        return []


async def get_confluence_spaces() -> List[Dict]:
    """
    Get all accessible Confluence spaces.

    Returns:
        List of space dictionaries
    """
    try:
        spaces = await atlassian_service.get_confluence_spaces()
        logger.info(f"Retrieved {len(spaces)} Confluence spaces")
        return spaces
    except Exception as e:
        logger.exception(f"Error fetching Confluence spaces: {e}")
        return []


async def get_confluence_pages(space_key: str, max_results: int = 100, page_ids: Optional[List[str]] = None) -> List[Dict]:
    """
    Get pages from a specific Confluence space.

    Args:
        space_key: Space key
        max_results: Maximum number of pages to return
        page_ids: Specific page IDs to fetch (optional)

    Returns:
        List of page dictionaries
    """
    try:
        if page_ids:
            # If specific page IDs are provided, fetch each one individually
            pages = []
            for page_id in page_ids:
                page_content = await atlassian_service.get_confluence_page_content(page_id)
                if page_content:
                    # Parse the content to extract page details
                    lines = page_content.split("\n")
                    page_data = {"id": page_id, "content": page_content, "space_key": space_key}

                    # Extract basic info from formatted content
                    for line in lines:
                        if line.startswith("Page: "):
                            page_data["title"] = line.replace("Page: ", "")
                        elif line.startswith("Space: "):
                            page_data["space_name"] = line.replace("Space: ", "")

                    pages.append(page_data)
            return pages
        else:
            # Fetch all pages from the space
            pages = await atlassian_service.get_confluence_pages(space_key, max_results)
            return pages
    except Exception as e:
        logger.exception(f"Error fetching Confluence pages for space {space_key}: {e}")
        return []


async def get_atlassian_content_for_indexing(
    content_type: str, project_key: Optional[str] = None, space_key: Optional[str] = None, item_ids: Optional[List[str]] = None
) -> List[Dict]:
    """
    Get formatted content for indexing from Atlassian.

    Args:
        content_type: Type of content ('jira_issues' or 'confluence_pages')
        project_key: Jira project key (required for jira_issues)
        space_key: Confluence space key (required for confluence_pages)
        item_ids: Specific item IDs/keys to fetch

    Returns:
        List of content dictionaries ready for indexing
    """
    try:
        content_items = []

        if content_type == "jira_issues":
            if not project_key:
                raise ValueError("project_key is required for jira_issues")

            if item_ids:
                # Fetch specific issues
                for issue_key in item_ids:
                    issue_content = await atlassian_service.get_jira_issue_content(issue_key)
                    if issue_content:
                        content_items.append(
                            {
                                "id": issue_key,
                                "title": f"Jira Issue: {issue_key}",
                                "content": issue_content,
                                "source": f"jira_issue_{issue_key}",
                                "metadata": {"content_type": "jira_issue", "project_key": project_key, "issue_key": issue_key},
                            }
                        )
            else:
                # Fetch all issues from project
                issues = await get_jira_issues(project_key)
                for issue in issues:
                    issue_content = await atlassian_service.get_jira_issue_content(issue["key"])
                    if issue_content:
                        content_items.append(
                            {
                                "id": issue["key"],
                                "title": f"Jira Issue: {issue['key']} - {issue.get('summary', '')}",
                                "content": issue_content,
                                "source": f"jira_issue_{issue['key']}",
                                "metadata": {
                                    "content_type": "jira_issue",
                                    "project_key": project_key,
                                    "issue_key": issue["key"],
                                    "summary": issue.get("summary", ""),
                                    "status": issue.get("status", ""),
                                },
                            }
                        )

        elif content_type == "confluence_pages":
            if not space_key:
                raise ValueError("space_key is required for confluence_pages")

            if item_ids:
                # Fetch specific pages
                for page_id in item_ids:
                    page_content = await atlassian_service.get_confluence_page_content(page_id)
                    if page_content:
                        # Extract title from content
                        lines = page_content.split("\n")
                        title = page_id
                        for line in lines:
                            if line.startswith("Page: "):
                                title = line.replace("Page: ", "")
                                break

                        content_items.append(
                            {
                                "id": page_id,
                                "title": f"Confluence Page: {title}",
                                "content": page_content,
                                "source": f"confluence_page_{page_id}",
                                "metadata": {"content_type": "confluence_page", "space_key": space_key, "page_id": page_id, "title": title},
                            }
                        )
            else:
                # Fetch all pages from space
                pages = await get_confluence_pages(space_key)
                for page in pages:
                    page_content = await atlassian_service.get_confluence_page_content(page["id"])
                    if page_content:
                        content_items.append(
                            {
                                "id": page["id"],
                                "title": f"Confluence Page: {page.get('title', page['id'])}",
                                "content": page_content,
                                "source": f"confluence_page_{page['id']}",
                                "metadata": {
                                    "content_type": "confluence_page",
                                    "space_key": space_key,
                                    "page_id": page["id"],
                                    "title": page.get("title", ""),
                                    "space_name": page.get("space_name", ""),
                                },
                            }
                        )
        else:
            raise ValueError(f"Unsupported content_type: {content_type}")

        logger.info(f"Retrieved {len(content_items)} {content_type} items for indexing")
        return content_items

    except Exception as e:
        logger.exception(f"Error getting Atlassian content for indexing: {e}")
        return []


async def close_atlassian_session():
    """Close the Atlassian service session."""
    try:
        await atlassian_service.close_session()
        logger.info("Closed Atlassian service session")
    except Exception as e:
        logger.exception(f"Error closing Atlassian session: {e}")
