"""Core utilities for URL scraping using <PERSON><PERSON> and BeautifulSoup."""

import logging

from bs4 import BeautifulSoup
from playwright.async_api import async_playwright


async def scrape_text_from_url(url: str) -> str:
    """Scrape textual content from a given URL using <PERSON>wright and BeautifulSoup.

    This function launches a headless Chromium browser to render the page content,
    then extracts visible text from common HTML tags such as <p>, <li>, <div>, <a>, and <span>.

    Args:
        url (str): The URL of the web page to scrape.

    Returns:
        str: Concatenated text content extracted from the web page.

    Raises:
        ValueError: If the scraped text content is empty.
        Exception: If any other error occurs during scraping.
    """
    try:
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True, args=["--no-sandbox"])
            page = await browser.new_page()
            await page.goto(url, timeout=10_000)
            content = await page.content()
            await browser.close()

        soup = BeautifulSoup(content, "html.parser")
        text_content = " ".join(tag.get_text(strip=True) for tag in soup.find_all(["p", "li", "div", "a", "span"])).strip()

        if not text_content:
            raise ValueError("Scraped content is empty.")

        return text_content

    except Exception as e:
        logging.exception(f"Failed to scrape URL: {url}", exc_info=e)
        raise
