from __future__ import annotations

import logging

import tik<PERSON>en
from fastapi import <PERSON><PERSON>P<PERSON>x<PERSON>
from langchain_openai.llms.azure import AzureOpenAI
from langchain_openai.llms.base import OpenAI

logger = logging.getLogger(__name__)


def create_headers(auth):
    """Create headers for API requests based on authentication details."""
    if isinstance(auth, dict):
        is_admin = auth.get("user") == "admin" and auth.get("role") == "admin_ui"
        local_dev = auth.get("user") == "developer" and auth.get("role") == "dev_local"
        headers = {"X-Admin-UI": "true"} if is_admin or local_dev else {}
        return headers
    elif not isinstance(auth, dict) and not auth.access_token:
        raise HTTPException(status_code=401, detail="Unauthorized")
    else:
        return {"Authorization": f"Bearer {auth.access_token}"}


def calculate_token_usage_final(
    system_prompt: str,
    llm_model: str,
    top_k: int,
    chunk_size: int,
    chunk_overlap: int = 0,
    chunking_strategy: str = "recursive",
) -> dict:
    """Calculate token usage for a given LLM model and chunking strategy."""
    context_limit = None
    try:
        context_limit = OpenAI.modelname_to_contextsize(llm_model)
    except Exception as e:
        if context_limit is None:
            try:
                context_limit = AzureOpenAI.modelname_to_contextsize(llm_model)
            except Exception as e:
                context_limit = None

    MODEL_CONTEXT_LIMITS = {
        "gpt-4": 8192,
        "gpt-4o": 128000,
        "gpt-4o-mini": 128000,
        "Meta-Llama-3-70B-Instruct": 8192,
        "Phi-4": 8192,
        "Meta-Llama-3.1-70B-Instruct": 8192,
    }

    context_limit = context_limit or MODEL_CONTEXT_LIMITS.get(llm_model, 8192)

    try:
        encoding = tiktoken.encoding_for_model(llm_model)
    except KeyError:
        encoding = tiktoken.get_encoding("cl100k_base")

    # Calculate the number of tokens in the system prompt
    system_prompt_tokens = len(encoding.encode(system_prompt))

    # Embedding tokens
    if chunking_strategy == "token":
        estimated_tokens_per_chunk = chunk_size
    else:
        dummy_chunk = "a" * chunk_size
        estimated_tokens_per_chunk = len(encoding.encode(dummy_chunk))

    # Calculate the total number of tokens used by the chunks
    chunk_tokens_total = top_k * (estimated_tokens_per_chunk + chunk_overlap)
    total_used = system_prompt_tokens + chunk_tokens_total

    # Available tokens for input
    available = context_limit - total_used

    return {
        "llm_model": llm_model,
        "system_prompt_tokens": system_prompt_tokens,
        "estimated_tokens_per_chunk": estimated_tokens_per_chunk,
        "top_k": top_k,
        "chunk_size": chunk_size,
        "chunk_tokens_total": chunk_tokens_total,
        "context_limit": context_limit,
        "total_used": total_used,
        "available_for_input": max(0, available),
    }
