class FileStorageError(Exception):
    """Custom exception for file storage errors."""

    pass


class DatabaseInsertError(Exception):
    """Custom exception for database insertion errors."""

    pass


class InvalidFolderPathError(Exception):
    """Custom exception for invalid folder path errors."""

    pass


class NoFilesFoundError(Exception):
    """Custom exception for cases where no files are found in a directory."""

    pass


class DocumentNotFoundError(Exception):
    """Custom exception for when a document is not found."""

    pass


class InvalidDocumentStatusError(Exception):
    """Custom exception for invalid document status errors."""

    pass


class UnsupportedFileTypeError(Exception):
    """Custom exception for unsupported file type errors."""

    pass


class PDFPageLimitExceededError(Exception):
    """Custom exception for when the number of pages in a PDF exceeds the allowed limit."""

    pass
