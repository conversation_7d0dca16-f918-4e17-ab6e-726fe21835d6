"""Response models for document, collection, URL, and token-related API endpoints."""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from loader_app.enums.document import DocumentStatusEnum
from pydantic import BaseModel, Field

# -------- DOCUMENTS --------


class DocumentResponse(BaseModel):
    """Base response model with optional message."""

    message: Optional[str]


class UploadDocumentResponse(BaseModel):
    """Response after uploading a single document."""

    document_id: UUID
    status: DocumentStatusEnum


class UploadBatchDocumentResponse(BaseModel):
    """Response after uploading multiple documents in batch."""

    document_ids: List[UUID]
    message: str


class GetDocumentStatusResponse(BaseModel):
    """Response model to represent the current status of a document."""

    status: DocumentStatusEnum


class ProcessDocumentResponse(BaseModel):
    """Response model for document processing completion status."""

    status: str


class GetDocumentsResponse(BaseModel):
    """Response model containing a list of all document records."""

    documents: list


class GetDocumentsByCollectionNameResponse(BaseModel):
    """Response model for fetching document IDs by collection name."""

    document_ids: List[str]


class DocumentResponseItem(BaseModel):
    """Detailed document item with metadata and timestamps."""

    id: UUID
    collection_id: Optional[UUID]
    file_path: str
    document_metadata: dict
    status: DocumentStatusEnum
    created_at: datetime
    last_updated: datetime

    class Config:
        """Pydantic configuration to support ORM mode."""

        from_attributes = True


class GetDocumentResponse(BaseModel):
    """Response model wrapping a single document object."""

    document: DocumentResponseItem


# -- COLLECTIONS --


class MessageResponse(BaseModel):
    """Simple response model containing a message."""

    message: Optional[str]


class CreateCollectionResponse(BaseModel):
    """Response after creating a new document collection."""

    collection_id: UUID
    message: str


class CollectionResponseItem(BaseModel):
    """Details of a document collection."""

    id: UUID
    name: str
    description: Optional[str]
    custom_config: Optional[dict]
    created_at: datetime
    last_updated: datetime

    class Config:
        """Pydantic config for ORM support."""

        from_attributes = True


class GetCollectionsResponse(BaseModel):
    """Response containing all collections and optional user info."""

    collections: List[CollectionResponseItem]
    user_info: Optional[dict] = Field(None, description="user information")


# -- URLS --


class UrlItem(BaseModel):
    """Metadata for a stored URL and its contents."""

    id: UUID
    collection_id: UUID
    url: str
    content: str
    status: Optional[str]
    created_at: datetime
    last_updated: datetime

    class Config:
        """Pydantic configuration for ORM mode."""

        from_attributes = True


class UrlResponse(BaseModel):
    """Response model with an optional message for URL operations."""

    message: Optional[str]


class UploadUrlResponse(BaseModel):
    """Response after uploading a single URL for processing."""

    url_id: UUID
    status: DocumentStatusEnum


class UploadBatchUrlResponse(BaseModel):
    """Response after uploading multiple URLs in a single request."""

    url_ids: List[str]
    message: str


class GetUrlStatusResponse(BaseModel):
    """Response model for checking the current status of a URL."""

    status: DocumentStatusEnum


class ProcessUrlResponse(BaseModel):
    """Response model for indicating completion of URL processing."""

    status: str


class GetUrlsResponse(BaseModel):
    """Response containing a list of all stored URLs."""

    urls: List[UrlItem]


class GetUrlIdsByCollectionNameResponse(BaseModel):
    """Response with list of URL IDs belonging to a given collection."""

    url_ids: List[UUID]


# -- TOKEN --


class GetTokenResponse(BaseModel):
    """Response showing the number of tokens used."""

    tokens_used: float


class GetTokenCostResponse(BaseModel):
    """Response indicating the cost associated with token usage."""

    cost: float


class CollectionTokenUsageResponse(BaseModel):
    """Response model for token usage statistics of a collection."""

    llm_model: str
    system_prompt_tokens: int
    estimated_tokens_per_chunk: int
    top_k: int
    chunk_size: int
    chunk_tokens_total: int
    context_limit: int
    total_used: int
    available_for_input: int


# -------- ATLASSIAN --------


class AtlassianAuthResponse(BaseModel):
    """Response after Atlassian authentication."""

    success: bool
    message: str
    user_info: Optional[dict] = None


class AtlassianProjectItem(BaseModel):
    """Individual Jira project item."""

    id: str
    key: str
    name: str
    description: str = ""
    project_type: str = ""
    lead: str = ""


class AtlassianProjectsResponse(BaseModel):
    """Response containing Jira projects."""

    projects: List[AtlassianProjectItem]


class AtlassianIssueItem(BaseModel):
    """Individual Jira issue item."""

    id: str
    key: str
    summary: str
    description: str = ""
    status: str
    assignee: str = ""
    creator: str
    created: str
    updated: str
    priority: str = ""
    issue_type: str
    labels: List[str] = []
    project_key: str

    # Extended fields for detailed content
    task_number: str = ""  # e.g., PLD-256
    subtask_numbers: List[str] = []  # e.g., [PLD-314, PLD-315]
    parent_task: str = ""  # Parent task if this is a subtask
    activities: List[str] = []  # Comments and activity logs
    connected_work_items: List[str] = []  # Linked issues


class AtlassianIssuesResponse(BaseModel):
    """Response containing Jira issues."""

    issues: List[AtlassianIssueItem]


class AtlassianSpaceItem(BaseModel):
    """Individual Confluence space item."""

    id: str
    key: str
    name: str
    description: str = ""
    type: str = ""
    status: str = ""


class AtlassianSpacesResponse(BaseModel):
    """Response containing Confluence spaces."""

    spaces: List[AtlassianSpaceItem]


class AtlassianPageItem(BaseModel):
    """Individual Confluence page item."""

    id: str
    title: str
    content: str = ""
    space_key: str
    space_name: str = ""
    created: str = ""
    creator: str = ""
    status: str = ""
    type: str = ""


class AtlassianPagesResponse(BaseModel):
    """Response containing Confluence pages."""

    pages: List[AtlassianPageItem]


class IndexAtlassianContentResponse(BaseModel):
    """Response after indexing Atlassian content."""

    success: bool
    message: str
    indexed_count: int
    failed_items: List[str] = []
