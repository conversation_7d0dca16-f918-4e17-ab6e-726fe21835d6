from datetime import date
from typing import Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator

# -- DOCUMENTS --


class UploadDocumentRequestMetadata(BaseModel):
    """Request model for uploading a document with metadata."""

    source: Optional[str] = Field(description="source (system name)", max_length=100, default=None)
    accountable_person: Optional[str] = Field(
        description="accountable person (name and surname)",
        max_length=100,
        default=None,
    )
    use_case: Optional[str] = Field(description="use case", max_length=100, default=None)
    summary: Optional[str] = Field(
        description="summary (1-3 sentences of the content)",
        max_length=300,
        default=None,
    )
    relevancy: Optional[int] = Field(description="relevancy", max_length=100, default=None)
    criticality: Optional[bool] = Field(description="criticality (internal or external usage)", default=False)
    input_file_name: Optional[str] = Field(description="input file name", default=None)
    domain: Optional[str] = Field(description="description of the knowledge domain", max_length=100, default=None)
    sub_domain: Optional[str] = Field(
        description="description of the subdomain of the knowledge",
        max_length=100,
        default=None,
    )
    responsible: Optional[str] = Field(
        description="person who is responsible for the files",
        max_length=100,
        default=None,
    )
    model: Optional[str] = Field(description="model used for embedding", max_length=100, default=None)
    compliance: Optional[bool] = Field(description="compliance with GDPR or not", default=False)
    version: Optional[date] = Field(description="last update of the file", default=None)


class UploadBatchDocumentRequest(BaseModel):
    """Request model for uploading a batch of documents."""

    folder_path: str = Field(..., description="Filesystem path to the folder containing documents")


# -- COLLECTIONS --
class CollectionRequest(BaseModel):
    """Request model for creating a new collection."""

    name: str
    description: Optional[str] = Field(None, description="collection description")
    custom_config: Optional[dict] = Field(None, description="collection customizations")

    @validator("name")
    def name_validator(cls, value):
        """Validate the collection name."""
        if " " in value:
            raise ValueError("name should not contain blank characters")
        return value


class UpdateCollectionRequest(BaseModel):
    """Request model for updating an existing collection."""

    name: Optional[str] = Field(None, description="collection name")
    llm_model: Optional[str] = Field(None, description="llm_model")
    system_prompt: Optional[str] = Field(None, description="custom prompt")
    chunk_size: Optional[int] = Field(None, description="chunk size")
    chunk_overlap: Optional[int] = Field(None, description="chunk overlap")
    is_memory_on: Optional[bool] = Field(None, description="enable or disable memory")
    query_history_ttl: Optional[int] = Field(None, description="chat history TTL in seconds")
    reranking_on: Optional[bool] = Field(None, description="enable or disable reranking")
    reranker_small_llm_model: Optional[str] = Field(None, description="small llm model for reranking")
    is_web_search_on: Optional[bool] = Field(None, description="enable or disable web search")
    web_search_llm_model: Optional[str] = Field(None, description="llm model for web search")
    is_slash_commands_on: Optional[bool] = Field(None, description="enable or disable slash commands")
    chunking_strategy: Optional[str] = Field(None, description="chunking strategy (e.g., 'recursive', 'headline')")
    system_prompt_length_limit: Optional[int] = Field(None, description="system prompt length limit")
    role: Optional[str] = Field(None, pattern="^(basic|expert|admin)$", description="Visibility role of the collection")
    collection_search_method: Optional[str] = Field(None, description="collection search method")


class UpdateDocumentRequest(BaseModel):
    """Request model for updating an existing document."""

    collection_id: Optional[UUID] = None
    file_path: Optional[str] = None
    status: Optional[str] = None
    document_metadata: Optional[Dict] = None


# --- URLS ---
class UploadUrlRequest(BaseModel):
    """Request model for uploading a URL."""

    url: str


class UploadBatchUrlRequest(BaseModel):
    """Request model for uploading a batch of URLs."""

    urls: List[str]


class UpdateUrlRequest(BaseModel):
    """Request model for updating an existing URL."""

    status: Optional[str] = None
    content: Optional[str] = None


# --- ATLASSIAN ---
class AtlassianAuthRequest(BaseModel):
    """Request model for Atlassian authentication."""

    base_url: str = Field(description="Atlassian instance base URL (e.g., https://yourcompany.atlassian.net)")
    username: str = Field(description="User email address")
    api_token: str = Field(description="API token from Atlassian")


class AtlassianProjectRequest(BaseModel):
    """Request model for getting Jira projects."""

    project_keys: Optional[List[str]] = Field(default=None, description="Specific project keys to fetch (optional)")


class AtlassianIssuesRequest(BaseModel):
    """Request model for getting Jira issues."""

    project_key: str = Field(description="Project key (e.g., 'PROJ')")
    max_results: Optional[int] = Field(default=100, description="Maximum number of issues to return")
    issue_keys: Optional[List[str]] = Field(default=None, description="Specific issue keys to fetch (optional)")


class AtlassianSpacesRequest(BaseModel):
    """Request model for getting Confluence spaces."""

    space_keys: Optional[List[str]] = Field(default=None, description="Specific space keys to fetch (optional)")


class AtlassianPagesRequest(BaseModel):
    """Request model for getting Confluence pages."""

    space_key: str = Field(description="Space key")
    max_results: Optional[int] = Field(default=100, description="Maximum number of pages to return")
    page_ids: Optional[List[str]] = Field(default=None, description="Specific page IDs to fetch (optional)")


class IndexJiraIssuesRequest(BaseModel):
    """Request model for indexing selected Jira issues."""

    project_key: str = Field(description="Jira project key")
    issue_keys: List[str] = Field(description="List of issue keys to index")
    collection_id: UUID = Field(description="Collection ID to index documents into")


class IndexConfluencePagesRequest(BaseModel):
    """Request model for indexing selected Confluence pages."""

    space_key: str = Field(description="Confluence space key")
    page_ids: List[str] = Field(description="List of page IDs to index")
    collection_id: UUID = Field(description="Collection ID to index documents into")
