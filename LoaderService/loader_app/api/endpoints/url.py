import logging
from typing import Any, Union
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from loader_app.api.core.collection import verify_collection_access
from loader_app.api.handlers.url import (
    handle_batch_upload_url,
    handle_delete_url,
    handle_delete_urls_by_collection,
    handle_get_collection_by_url_id,
    handle_get_url,
    handle_get_url_ids_by_collection_name,
    handle_get_urls,
    handle_update_url,
    handle_upload_url,
)
from loader_app.api.models.request import (
    UpdateUrlRequest,
    UploadBatchUrlRequest,
    UploadUrlRequest,
)
from loader_app.api.models.response import (
    CollectionResponseItem,
    GetUrlIdsByCollectionNameResponse,
    GetUrlsResponse,
    GetUrlStatusResponse,
    MessageResponse,
    UploadBatchUrlResponse,
    UploadUrlResponse,
    UrlItem,
    UrlResponse,
)
from loader_app.crud.url import get_url
from loader_app.database.session import get_db
from loader_app.enums.document import DocumentStatusEnum
from loader_app.services.auth import get_auth
from loader_app.services.rbac import roles_required
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)
router = APIRouter()


@router.delete(
    "/collection/{collection_id}/urls",
    response_model=MessageResponse,
    responses={
        500: {"model": MessageResponse},
    },
)
async def api_delete_urls_by_collection(
    collection_id: UUID,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> MessageResponse:
    """Delete all URLs in a specific collection."""
    return await handle_delete_urls_by_collection(db, auth, collection_id)


@router.post(
    "/collection/{collection_id}/url",
    response_model=UploadUrlResponse,
    responses={
        400: {"model": UrlResponse},
        404: {"model": UrlResponse},
        500: {"model": UrlResponse},
    },
)
async def api_upload_url(
    collection_id: UUID,
    url_request: UploadUrlRequest,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> UploadUrlResponse:
    """Upload a URL to a specific collection."""
    return await handle_upload_url(db, auth, collection_id, url_request)


@router.post(
    "/collection/{collection_id}/url/batch",
    response_model=UploadBatchUrlResponse,
    responses={
        500: {"model": MessageResponse},
    },
)
async def api_upload_batch_url(
    collection_id: UUID,
    upload_batch_request: UploadBatchUrlRequest,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> UploadBatchUrlResponse:
    """Upload a batch of URLs to a specific collection."""
    return await handle_batch_upload_url(db, auth, collection_id, upload_batch_request.urls)


@router.get(
    "/collection/{collection_id}/url/{url_id}/status",
    response_model=Union[GetUrlStatusResponse, MessageResponse],
    responses={
        404: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_get_url_status(
    collection_id: UUID,
    url_id: UUID,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> GetUrlStatusResponse:
    """Get the status of a URL in a specific collection."""
    verify_collection_access(
        db,
        auth,
        collection_id=collection_id,
        on_forbidden="raise",
    )
    url = get_url(db, item_id=url_id, collection_id=collection_id)
    if not url:
        raise HTTPException(status_code=404, detail="URL not found")

    return GetUrlStatusResponse(status=DocumentStatusEnum(url.status))


@router.delete(
    "/collection/{collection_id}/url/{url_id}",
    response_model=MessageResponse,
    responses={
        404: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_delete_url_by_collection_id(
    collection_id: UUID,
    url_id: UUID,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> MessageResponse:
    """Delete a URL by its ID in a specific collection."""
    return await handle_delete_url(db, auth, url_id, collection_id)


@router.delete(
    "/url/{url_id}",
    response_model=MessageResponse,
    responses={
        404: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_delete_url(
    url_id: UUID,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> MessageResponse:
    """Delete a URL by its ID."""
    return await handle_delete_url(db, auth, url_id)


@router.patch(
    "/collection/{collection_id}/url/{url_id}",
    response_model=MessageResponse,
    responses={
        404: {"model": MessageResponse},
        400: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_update_url(
    collection_id: UUID,
    url_id: UUID,
    update_data: UpdateUrlRequest,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> MessageResponse:
    """Update a URL in a specific collection."""
    print("logging update_url request:", collection_id, url_id, update_data)
    return await handle_update_url(db, auth, url_id, update_data, collection_id=collection_id)


@router.patch(
    "/url/{url_id}",
    response_model=MessageResponse,
    responses={
        404: {"model": MessageResponse},
        400: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_update_url_by_id(
    url_id: UUID,
    update_data: UpdateUrlRequest,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> MessageResponse:
    """Update a URL by its ID."""
    return await handle_update_url(db, auth, url_id=url_id, update_data=update_data)


@router.get(
    "/collection/{collection_id}/url/{url_id}",
    response_model=UrlItem,
    responses={
        404: {"model": MessageResponse},
        400: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_get_url(
    collection_id: UUID,
    url_id: UUID,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> UrlItem:
    """Get a URL by its ID in a specific collection."""
    return await handle_get_url(db, auth, url_id=url_id, collection_id=collection_id)


@router.get("/url/{url_id}", response_model=Union[UrlResponse, dict])
async def api_get_url_by_id(url_id: UUID, db_session: Session = Depends(get_db), auth: Any = Depends(roles_required({"admin", "expert", "service_access"}))):
    """Get a URL by its ID."""
    url = get_url(db_session, item_id=url_id)
    verify_collection_access(
        db_session,
        auth,
        collection_id=url.collection.id,
        on_forbidden="raise",
    )
    if not url:
        return JSONResponse(content=jsonable_encoder(UrlResponse(message="not found")), status_code=404)
    return JSONResponse(content=jsonable_encoder(url.to_dict()), status_code=200)


@router.get(
    "/collection/by_name/{collection_name}/url/{url_id}",
    response_model=UrlItem,
    responses={
        404: {"model": MessageResponse},
        400: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_get_url_by_collection_name(
    collection_name: str,
    url_id: UUID,
    db: Session = Depends(get_db),
    auth=Depends(get_auth),
) -> UrlItem:
    """Get a URL by its ID in a specific collection identified by name."""
    return await handle_get_url(db, auth, url_id=url_id, collection_name=collection_name)


@router.get(
    "/collection/{collection_id}/urls",
    response_model=GetUrlsResponse,
    responses={
        404: {"model": MessageResponse},
        400: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_get_urls(
    collection_id: UUID,
    db_session: Session = Depends(get_db),
    auth=Depends(get_auth),
) -> GetUrlsResponse:
    """Get all URLs in a specific collection."""
    return await handle_get_urls(db_session, auth, collection_id=collection_id)


@router.get(
    "/collection/{collection_name}/url_ids",
    response_model=GetUrlIdsByCollectionNameResponse,
    responses={
        404: {"model": MessageResponse},
        400: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_get_url_ids_by_collection_name(
    collection_name: str,
    db_session: Session = Depends(get_db),
    auth=Depends(get_auth),
) -> GetUrlIdsByCollectionNameResponse:
    """Get all URL IDs in a specific collection by name."""
    return await handle_get_url_ids_by_collection_name(db_session, auth, collection_name)


@router.get(
    "/url/{url_id}/collection",
    response_model=CollectionResponseItem,
    responses={
        404: {"model": MessageResponse},
        400: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_get_collection_by_url_id(
    url_id: UUID,
    db_session: Session = Depends(get_db),
    auth=Depends(get_auth),
) -> CollectionResponseItem:
    """Get the collection associated with a specific URL by its ID."""
    return await handle_get_collection_by_url_id(db_session, auth, url_id)
