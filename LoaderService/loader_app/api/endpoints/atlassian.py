"""Atlassian API endpoints for Jira and Confluence integration."""

import logging
from typing import Any

from fastapi import APIRouter, Depends, HTTPException
from loader_app.api.models.request import (
    AtlassianAuthRequest,
    IndexConfluencePagesRequest,
    IndexJiraIssuesRequest,
)
from loader_app.api.models.response import (
    AtlassianAuthResponse,
    AtlassianIssuesResponse,
    AtlassianPagesResponse,
    AtlassianProjectsResponse,
    AtlassianSpacesResponse,
    IndexAtlassianContentResponse,
    MessageResponse,
)
from loader_app.crud.atlassian import (
    authenticate_with_atlassian,
    cleanup_atlassian_session,
    fetch_confluence_pages,
    fetch_confluence_spaces,
    fetch_jira_issues,
    fetch_jira_projects,
)
from loader_app.database.session import get_db
from loader_app.services.rbac import roles_required
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)
router = APIRouter()

# ─────────────────────────────────────────────────────────────────────
# Legacy API Token Authentication (Keep for backward compatibility)
# ─────────────────────────────────────────────────────────────────────


@router.post(
    "/auth",
    response_model=AtlassianAuthResponse,
    responses={
        400: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def authenticate_atlassian(
    auth_request: AtlassianAuthRequest,
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
):
    """Authenticate with Atlassian using API token (legacy method)."""
    try:
        success = await authenticate_with_atlassian(auth_request.base_url, auth_request.username, auth_request.api_token)

        if success:
            return AtlassianAuthResponse(success=True, message="Successfully authenticated with Atlassian")
        else:
            raise HTTPException(status_code=400, detail="Authentication failed. Please check your credentials.")

    except Exception as e:
        logger.exception(f"Error authenticating with Atlassian: {e}")
        raise HTTPException(status_code=500, detail="Internal server error during authentication")


@router.post("/cleanup")
async def cleanup_atlassian():
    """Clean up Atlassian session."""
    try:
        await cleanup_atlassian_session()
        return {"success": True, "message": "Session cleaned up successfully"}
    except Exception as e:
        logger.exception(f"Error cleaning up Atlassian session: {e}")
        raise HTTPException(status_code=500, detail="Failed to cleanup session")


# ─────────────────────────────────────────────────────────────────────
# Jira Endpoints
# ─────────────────────────────────────────────────────────────────────


@router.get(
    "/jira/projects/simple",
    response_model=AtlassianProjectsResponse,
    responses={
        400: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def get_jira_projects_simple(
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
):
    """Get all accessible Jira projects (simplified view)."""
    try:
        projects = await fetch_jira_projects()
        return AtlassianProjectsResponse(projects=projects)
    except Exception as e:
        logger.exception(f"Error fetching Jira projects: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch Jira projects")


@router.get(
    "/jira/projects/{project_key}/issues",
    response_model=AtlassianIssuesResponse,
    responses={
        400: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def get_jira_issues(
    project_key: str,
    max_results: int = 10000,
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
):
    """Get issues from a specific Jira project."""
    try:
        issues = await fetch_jira_issues(project_key, max_results)
        return AtlassianIssuesResponse(issues=issues)
    except Exception as e:
        logger.exception(f"Error fetching Jira issues for project {project_key}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch issues for project {project_key}")


@router.post(
    "/jira/projects/{project_key}/index",
    response_model=IndexAtlassianContentResponse,
    responses={
        400: {"model": MessageResponse},
        404: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def index_selected_jira_issues(
    project_key: str,
    request_body: IndexJiraIssuesRequest,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
):
    """Index selected Jira issues into embeddings using the existing URL pipeline."""
    from loader_app.api.core.collection import verify_collection_access
    from loader_app.api.utils.helpers import create_headers
    from loader_app.crud.url import insert_or_update_url, update_url
    from loader_app.database.models import URL
    from loader_app.enums.document import DocumentEventsEnum
    from loader_app.services.atlassian import atlassian_service
    from loader_app.tasks.url import move_url_forward

    try:
        # 1. Verify collection access
        verify_collection_access(db, auth, collection_id=request_body.collection_id, on_forbidden="raise")

        # 2. Process issues in batches to avoid timeout
        BATCH_SIZE = 50
        issue_keys = request_body.issue_keys
        total_issues = len(issue_keys)
        processed = 0
        failed_items = []
        headers = create_headers(auth)

        logger.info(f"Processing {total_issues} issues from project {project_key} in batches of {BATCH_SIZE}")

        for i in range(0, total_issues, BATCH_SIZE):
            batch = issue_keys[i : i + BATCH_SIZE]
            logger.info(f"Processing batch {i // BATCH_SIZE + 1}: issues {i + 1}-{min(i + BATCH_SIZE, total_issues)}")

            for issue_key in batch:
                try:
                    # 3. Get comprehensive issue content from Jira
                    issue_content = await atlassian_service.get_jira_issue_content(issue_key)
                    if not issue_content:
                        logger.warning(f"No content found for issue {issue_key}")
                        failed_items.append(f"{issue_key}: No content found")
                        continue

                    # 4. Create a pseudo-URL for this issue
                    pseudo_url = f"jira://{project_key}/{issue_key}"

                    # 5. Insert or update in URL table
                    try:
                        url_id = insert_or_update_url(db, request_body.collection_id, pseudo_url, issue_content)
                        logger.info(f"Created new URL record for {issue_key} with ID {url_id}")
                    except ValueError as ve:
                        if str(ve) == "url_already_exists":
                            # Find existing record and update content
                            existing_url = db.query(URL).filter_by(collection_id=request_body.collection_id, url=pseudo_url).first()
                            if existing_url:
                                update_url(db, item_id=existing_url.id, content=issue_content)
                                url_id = existing_url.id
                                logger.info(f"Updated existing URL record for {issue_key} with ID {url_id}")
                            else:
                                logger.error(f"URL {pseudo_url} reported as existing but not found")
                                failed_items.append(f"{issue_key}: Database inconsistency")
                                continue
                        else:
                            logger.exception(f"Error inserting URL for {issue_key}: {ve}")
                            failed_items.append(f"{issue_key}: Database error")
                            continue

                    # 6. Trigger the existing URL pipeline
                    move_url_forward.send(
                        str(url_id),
                        DocumentEventsEnum.LOAD_REQUEST.value,
                        headers=headers,
                    )
                    processed += 1
                    logger.info(f"Triggered pipeline for {issue_key} (URL ID: {url_id})")

                except Exception as e:
                    logger.exception(f"Error processing issue {issue_key}: {e}")
                    failed_items.append(f"{issue_key}: {str(e)}")
                    continue

        success_count = processed
        logger.info(f"Successfully processed {success_count}/{total_issues} issues")

        return IndexAtlassianContentResponse(
            success=True,
            message=f"Successfully queued {success_count} issues for indexing",
            indexed_count=success_count,
            failed_items=failed_items,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error indexing Jira issues for project {project_key}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to index issues for project {project_key}: {str(e)}")


# ─────────────────────────────────────────────────────────────────────
# Confluence Endpoints
# ─────────────────────────────────────────────────────────────────────


@router.get(
    "/confluence/spaces/simple",
    response_model=AtlassianSpacesResponse,
    responses={
        400: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def get_confluence_spaces_simple(
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
):
    """Get all accessible Confluence spaces (simplified view)."""
    try:
        spaces = await fetch_confluence_spaces()
        return AtlassianSpacesResponse(spaces=spaces)
    except Exception as e:
        logger.exception(f"Error fetching Confluence spaces: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch Confluence spaces")


@router.get(
    "/confluence/spaces/{space_key}/pages",
    response_model=AtlassianPagesResponse,
    responses={
        400: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def get_confluence_pages(
    space_key: str,
    max_results: int = 10000,
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
):
    """Get pages from a specific Confluence space with content (supports pagination to get all pages)."""
    try:
        pages = await fetch_confluence_pages(space_key, max_results)
        return AtlassianPagesResponse(pages=pages)
    except Exception as e:
        logger.exception(f"Error fetching Confluence pages for space {space_key}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch pages for space {space_key}")


@router.post(
    "/confluence/spaces/{space_key}/index",
    response_model=IndexAtlassianContentResponse,
    responses={
        400: {"model": MessageResponse},
        404: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def index_selected_confluence_pages(
    space_key: str,
    request_body: IndexConfluencePagesRequest,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
):
    """Index selected Confluence pages into embeddings using the existing URL pipeline."""
    from loader_app.api.core.collection import verify_collection_access
    from loader_app.api.utils.helpers import create_headers
    from loader_app.crud.url import insert_or_update_url, update_url
    from loader_app.database.models import URL
    from loader_app.enums.document import DocumentEventsEnum
    from loader_app.services.atlassian import atlassian_service
    from loader_app.tasks.url import move_url_forward

    try:
        # 1. Verify collection access
        verify_collection_access(db, auth, collection_id=request_body.collection_id, on_forbidden="raise")

        # 2. Process pages in batches to avoid timeout
        BATCH_SIZE = 50
        page_ids = request_body.page_ids
        total_pages = len(page_ids)
        processed = 0
        failed_items = []
        headers = create_headers(auth)

        logger.info(f"Processing {total_pages} pages from space {space_key} in batches of {BATCH_SIZE}")

        for i in range(0, total_pages, BATCH_SIZE):
            batch = page_ids[i : i + BATCH_SIZE]
            logger.info(f"Processing batch {i // BATCH_SIZE + 1}: pages {i + 1}-{min(i + BATCH_SIZE, total_pages)}")

            for page_id in batch:
                try:
                    # 3. Get comprehensive page content from Confluence
                    logger.info(f"🔍 Fetching content for Confluence page {page_id} from space {space_key}")
                    page_content = await atlassian_service.get_confluence_page_content(page_id)
                    if not page_content:
                        logger.warning(f"❌ No content found for page {page_id}")
                        failed_items.append(f"{page_id}: No content found")
                        continue
                    
                    # Log content details for debugging
                    content_lines = page_content.split('\n')
                    logger.info(f"✅ Page {page_id} content retrieved - {len(content_lines)} lines, {len(page_content)} chars")
                    logger.info(f"📄 Content preview (first 200 chars): {page_content[:200]}...")
                    
                    # Log title if extractable
                    for line in content_lines[:5]:  # Check first 5 lines for title
                        if line.startswith("Page: "):
                            logger.info(f"📝 Page title: {line}")
                            break

                    # 4. Create a pseudo-URL for this page
                    pseudo_url = f"confluence://{space_key}/{page_id}"
                    logger.info(f"🔗 Created pseudo-URL: {pseudo_url}")

                    # 5. Insert or update in URL table
                    try:
                        logger.info(f"💾 Inserting URL into database - Collection: {request_body.collection_id}, URL: {pseudo_url}")
                        url_id = insert_or_update_url(db, request_body.collection_id, pseudo_url, page_content)
                        logger.info(f"✅ Created new URL record for page {page_id} with ID {url_id}")
                    except ValueError as ve:
                        if str(ve) == "url_already_exists":
                            # Find existing record and update content
                            logger.info(f"🔄 URL already exists, updating content for page {page_id}")
                            existing_url = db.query(URL).filter_by(collection_id=request_body.collection_id, url=pseudo_url).first()
                            if existing_url:
                                update_url(db, item_id=existing_url.id, content=page_content)
                                url_id = existing_url.id
                                logger.info(f"✅ Updated existing URL record for page {page_id} with ID {url_id}")
                            else:
                                logger.error(f"❌ URL {pseudo_url} reported as existing but not found")
                                failed_items.append(f"{page_id}: Database inconsistency")
                                continue
                        else:
                            logger.exception(f"❌ Error inserting URL for page {page_id}: {ve}")
                            failed_items.append(f"{page_id}: Database error")
                            continue

                    # 6. Trigger the existing URL pipeline
                    logger.info(f"🚀 Triggering URL pipeline for page {page_id} (URL ID: {url_id}) with event: {DocumentEventsEnum.LOAD_REQUEST.value}")
                    move_url_forward.send(
                        str(url_id),
                        DocumentEventsEnum.LOAD_REQUEST.value,
                        headers=headers,
                    )
                    processed += 1
                    logger.info(f"✅ Pipeline triggered for page {page_id} (URL ID: {url_id})")

                except Exception as e:
                    logger.exception(f"Error processing page {page_id}: {e}")
                    failed_items.append(f"{page_id}: {str(e)}")
                    continue

        success_count = processed
        logger.info(f"Successfully processed {success_count}/{total_pages} pages")

        return IndexAtlassianContentResponse(
            success=True,
            message=f"Successfully queued {success_count} pages for indexing",
            indexed_count=success_count,
            failed_items=failed_items,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error indexing Confluence pages for space {space_key}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to index pages for space {space_key}: {str(e)}")


@router.get(
    "/confluence/spaces/{space_key}/tree",
    response_model=AtlassianPagesResponse,
    responses={
        400: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def get_confluence_space_tree(
    space_key: str,
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
):
    """Get complete site tree for a Confluence space."""
    try:
        from loader_app.services.atlassian import atlassian_service

        pages = await atlassian_service.get_confluence_space_tree(space_key)
        return AtlassianPagesResponse(pages=pages)
    except Exception as e:
        logger.exception(f"Error fetching Confluence space tree for {space_key}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch space tree for {space_key}")
