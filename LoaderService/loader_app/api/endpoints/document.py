"""Endpoints for handling document-related operations in the Loader service."""

from __future__ import annotations

import logging
from typing import Any, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, File, Query, UploadFile
from loader_app.api.handlers.document import (
    handle_batch_upload,
    handle_delete_document,
    handle_get_content_from_file,
    handle_get_document,
    handle_get_document_status,
    handle_list_document_ids,
    handle_list_documents,
    handle_reload_document,
    handle_update_document,
    handle_upload_document,
)
from loader_app.api.models.request import (
    UpdateDocumentRequest,
    UploadBatchDocumentRequest,
)
from loader_app.api.models.response import (
    GetDocumentResponse,
    GetDocumentsByCollectionNameResponse,
    GetDocumentsResponse,
    GetDocumentStatusResponse,
    MessageResponse,
    ProcessDocumentResponse,
    UploadBatchDocumentResponse,
    UploadDocumentResponse,
)
from loader_app.database.session import get_db
from loader_app.enums.loader import LoadMethodEnum
from loader_app.services.rbac import roles_required
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)
router = APIRouter()


# --------------------------------------------------------------------------- #
#    Upload a single document                                                 #
# --------------------------------------------------------------------------- #
@router.post(
    "/collection/{collection_id}/document",
    response_model=UploadDocumentResponse,
    responses={
        400: {"model": MessageResponse},
        404: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_upload_document(
    collection_id: UUID,
    file: UploadFile = File(...),
    document_metadata: Optional[str] = None,
    load_method: LoadMethodEnum = LoadMethodEnum.USE_LANGCHAIN,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> UploadDocumentResponse:
    """Upload a single document to a given collection."""
    return await handle_upload_document(db, auth, collection_id, file, document_metadata, load_method)


# --------------------------------------------------------------------------- #
#   Batch upload                                                              #
# --------------------------------------------------------------------------- #
@router.post(
    "/collection/{collection_id}/document/batch",
    response_model=UploadBatchDocumentResponse,
    responses={
        400: {"model": MessageResponse},
        404: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_batch_upload(
    collection_id: UUID,
    body: UploadBatchDocumentRequest,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> UploadBatchDocumentResponse:
    """Upload multiple documents in a single batch to a given collection."""
    return await handle_batch_upload(db, auth, collection_id, body)


# --------------------------------------------------------------------------- #
#  Reload / retry processing                                                  #
# --------------------------------------------------------------------------- #
@router.post(
    "/collection/{collection_id}/document/{document_id}/reload",
    response_model=ProcessDocumentResponse,
    responses={
        400: {"model": MessageResponse},
        404: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_reload_document(
    collection_id: UUID,
    document_id: UUID,
    load_method: LoadMethodEnum = LoadMethodEnum.USE_LANGCHAIN,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> ProcessDocumentResponse:
    """Upload multiple documents in a single batch to a given collection."""
    return await handle_reload_document(db, auth, collection_id, document_id, load_method)


# --------------------------------------------------------------------------- #
#  Get document status                                                        #
# --------------------------------------------------------------------------- #
@router.get(
    "/collection/{collection_id}/document/{document_id}/status",
    response_model=GetDocumentStatusResponse,
    responses={
        404: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_get_document_status(
    collection_id: UUID,
    document_id: UUID,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access", "basic"})),
) -> GetDocumentStatusResponse:
    """Get the current processing status of a specific document."""
    return await handle_get_document_status(db, auth, collection_id, document_id)


# --------------------------------------------------------------------------- #
# Retrieve a document with collection check                                   #
# --------------------------------------------------------------------------- #
@router.get(
    "/collection/{collection_id}/document/{document_id}",
    response_model=GetDocumentResponse,
    responses={
        404: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_get_document_with_collection(
    collection_id: UUID,
    document_id: UUID,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> GetDocumentResponse:
    """Retrieve a document using both its ID and collection context."""
    return await handle_get_document(db, auth, collection_id, document_id)


# --------------------------------------------------------------------------- #
# Retrieve a document without collection check                                #
# --------------------------------------------------------------------------- #
@router.get(
    "/document/{document_id}",
    response_model=GetDocumentResponse,
    responses={
        404: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_get_document_without_collection(
    document_id: UUID,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> GetDocumentResponse:
    """Fetch a document by its ID without requiring collection context."""
    return await handle_get_document(db, auth, None, document_id)


# --------------------------------------------------------------------------- #
# List all documents in a collection                                          #
# --------------------------------------------------------------------------- #
@router.get(
    "/collection/{collection_id}/documents",
    response_model=GetDocumentsResponse,
    responses={
        500: {"model": MessageResponse},
    },
)
async def api_list_documents(
    collection_id: UUID,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> GetDocumentsResponse:
    """List all documents within a given collection."""
    return await handle_list_documents(db, auth, collection_id)


# --------------------------------------------------------------------------- #
# List document IDs by collection name                                        #
# --------------------------------------------------------------------------- #
@router.get(
    "/collection/{collection_name}/document_ids",
    response_model=GetDocumentsByCollectionNameResponse,
    responses={
        404: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_list_document_ids_by_collection_name(
    collection_name: str,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> GetDocumentsByCollectionNameResponse:
    """Retrieve all document IDs belonging to a collection by its name."""
    return await handle_list_document_ids(db, auth, collection_name)


# --------------------------------------------------------------------------- #
#  Lightweight content preview from uploaded files                            #
# --------------------------------------------------------------------------- #
@router.post(
    "/get_content_from_file",
    response_model=MessageResponse,
    responses={
        400: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_get_content_from_file(
    files: List[UploadFile] = File(...),
) -> MessageResponse:
    """Extract preview content from uploaded file(s)."""
    return await handle_get_content_from_file(files)


# --------------------------------------------------------------------------- #
#   Update document details                                                   #
# --------------------------------------------------------------------------- #
@router.patch(
    "/document/{document_id}",
    response_model=MessageResponse,
    responses={
        404: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_update_document(
    document_id: UUID,
    body: UpdateDocumentRequest,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> MessageResponse:
    """Update the metadata or content of a specific document."""
    return await handle_update_document(db, document_id, body)


@router.delete(
    "/document/{document_id}",
    response_model=MessageResponse,
    responses={
        404: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_delete_document(
    document_id: UUID,
    collection_id: Optional[UUID] = Query(None),
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> MessageResponse:
    """Delete a specific document from the collection."""
    return await handle_delete_document(db, document_id, collection_id)
