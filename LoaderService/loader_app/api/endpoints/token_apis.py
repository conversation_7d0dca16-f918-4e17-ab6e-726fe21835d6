import logging
from typing import Any, Optional

import redis
from fastapi import APIRouter, Depends, HTTPException, Query
from loader_app.services.auth import get_auth
from loader_app.services.token_limiter import TokenLimiter
from loader_app.settings import settings
from starlette.responses import JSONResponse

logger = logging.getLogger(__name__)
router = APIRouter()
token_limiter = TokenLimiter()
redis_conn = redis.Redis(settings.redis_host, port=settings.redis_port, db=settings.dramatiq_redis_db)


@router.get("/admin/token_usage")
async def get_token_usage(
    # Time filters
    interval_seconds: int = Query(-1, description="Time interval in seconds (-1 for all time)"),
    # Content filters
    user_emails: Optional[str] = Query(None, description="User email(s), comma-separated or single"),
    llm_models: Optional[str] = Query(None, description="LLM model(s), comma-separated or single"),
    # Analysis options
    breakdown: bool = Query(False, description="Return breakdown by time buckets"),
    group_by: str = Query("day", description="Group by 'day' or 'hour' (for breakdown)"),
    tz: str = Query("Europe/London", description="Timezone for grouping"),
    split_by: Optional[str] = Query(None, description="Split by 'email', 'model' or 'email,model'"),
    auth: Any = Depends(get_auth),
) -> JSONResponse:
    """
    Universal token usage endpoint with comprehensive filtering and analysis options.

    Examples:
    - /admin/token_usage -> Total usage across all time
    - /admin/token_usage?interval_seconds=86400 -> Last 24 hours
    - /admin/token_usage?user_emails=<EMAIL> -> Specific user
    - /admin/token_usage?llm_models=gpt-4o,gpt-4o-mini -> Specific models
    - /admin/token_usage?breakdown=true&group_by=day -> Daily breakdown
    - /admin/token_usage?split_by=email -> Split by users
    - /admin/token_usage?split_by=email,model&breakdown=true -> Full breakdown
    """
    try:
        # Parse comma-separated values
        user_emails_list = None
        if user_emails:
            user_emails_list = [email.strip() for email in user_emails.split(",")]

        llm_models_list = None
        if llm_models:
            llm_models_list = [model.strip() for model in llm_models.split(",")]

        split_by_list = None
        if split_by:
            split_by_list = [item.strip() for item in split_by.split(",")]

        result = token_limiter.get_token_usage(
            interval_seconds=interval_seconds,
            is_cost=False,
            user_emails=user_emails_list,
            llm_models=llm_models_list,
            breakdown=breakdown,
            group_by=group_by,
            tz=tz,
            split_by=split_by_list,
        )

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "data": {
                    "result": result,
                    "filters": {
                        "interval_seconds": interval_seconds,
                        "user_emails": user_emails_list,
                        "llm_models": llm_models_list,
                        "breakdown": breakdown,
                        "group_by": group_by if breakdown else None,
                        "tz": tz if breakdown else None,
                        "split_by": split_by_list,
                    },
                    "result_type": "breakdown" if breakdown else "total",
                },
            },
        )
    except Exception as e:
        logger.error(f"Error getting token usage: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting token usage: {str(e)}")


@router.get("/admin/token_cost")
async def get_token_cost(
    # Time filters
    interval_seconds: int = Query(-1, description="Time interval in seconds (-1 for all time)"),
    # Content filters
    user_emails: Optional[str] = Query(None, description="User email(s), comma-separated or single"),
    llm_models: Optional[str] = Query(None, description="LLM model(s), comma-separated or single"),
    # Analysis options
    breakdown: bool = Query(False, description="Return breakdown by time buckets"),
    group_by: str = Query("day", description="Group by 'day' or 'hour' (for breakdown)"),
    tz: str = Query("Europe/London", description="Timezone for grouping"),
    split_by: Optional[str] = Query(None, description="Split by 'email', 'model' or 'email,model'"),
    auth: Any = Depends(get_auth),
) -> JSONResponse:
    """
    Universal token cost endpoint with comprehensive filtering and analysis options.

    Examples:
    - /admin/token_cost -> Total cost across all time
    - /admin/token_cost?interval_seconds=86400 -> Last 24 hours cost
    - /admin/token_cost?user_emails=<EMAIL> -> Specific user cost
    - /admin/token_cost?llm_models=gpt-4o -> Specific model cost
    - /admin/token_cost?breakdown=true&group_by=day -> Daily cost breakdown
    - /admin/token_cost?split_by=email -> Cost split by users
    """
    try:
        # Parse comma-separated values
        user_emails_list = None
        if user_emails:
            user_emails_list = [email.strip() for email in user_emails.split(",")]

        llm_models_list = None
        if llm_models:
            llm_models_list = [model.strip() for model in llm_models.split(",")]

        split_by_list = None
        if split_by:
            split_by_list = [item.strip() for item in split_by.split(",")]

        result = token_limiter.get_token_usage_cost(
            interval_seconds=interval_seconds,
            user_emails=user_emails_list,
            llm_models=llm_models_list,
            breakdown=breakdown,
            group_by=group_by,
            tz=tz,
            split_by=split_by_list,
        )

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "data": {
                    "result": result,
                    "filters": {
                        "interval_seconds": interval_seconds,
                        "user_emails": user_emails_list,
                        "llm_models": llm_models_list,
                        "breakdown": breakdown,
                        "group_by": group_by if breakdown else None,
                        "tz": tz if breakdown else None,
                        "split_by": split_by_list,
                    },
                    "result_type": "breakdown" if breakdown else "total",
                },
            },
        )
    except Exception as e:
        logger.error(f"Error getting token cost: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting token cost: {str(e)}")


@router.get("/admin/emails")
async def list_known_emails(
    auth: Any = Depends(get_auth),
) -> JSONResponse:
    """
    Return distinct user emails observed in Redis usage keys.

    Scans both usage and cost keys with schema:
      - token_usage:{ts}:{email}:{model}
      - token_usage_cost:{ts}:{email}:{model}
    """
    try:
        emails = set()

        # Scan usage keys
        for raw_key in redis_conn.scan_iter(match="token_usage:*:*:*", count=1000):
            key = raw_key.decode() if isinstance(raw_key, bytes) else raw_key
            parts = key.split(":")
            if len(parts) >= 4:
                emails.add(parts[2])

        # Scan cost keys
        for raw_key in redis_conn.scan_iter(match="token_usage_cost:*:*:*", count=1000):
            key = raw_key.decode() if isinstance(raw_key, bytes) else raw_key
            parts = key.split(":")
            if len(parts) >= 4:
                emails.add(parts[2])

        email_list = sorted(emails)
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "data": {
                    "emails": email_list,
                    "total": len(email_list),
                },
            },
        )
    except Exception as e:
        logger.error(f"Error listing emails: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error listing emails: {str(e)}")
