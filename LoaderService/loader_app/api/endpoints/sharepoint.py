"""API endpoints for sharepoint management in the Indexer Service."""

import logging
import os
from io import BytesIO
from typing import Any
from uuid import UUID

import requests
from fastapi import APIRouter, Depends, HTTPException, UploadFile
from fastapi.responses import JSONResponse
from loader_app.api.endpoints.document import api_upload_document
from loader_app.database.session import get_db
from loader_app.service_clients.sharepoint_client.sharepoint_client import get_client
from loader_app.services.rbac import roles_required
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)
router = APIRouter()


SHAREPOINT_NAME = os.getenv("SHAREPOINT_NAME")
SHAREPOINT_PATH = os.getenv("SHAREPOINT_PATH")

client = get_client()


def _create_headers(auth):
    """Create headers for the request based on authentication."""
    if isinstance(auth, dict):
        is_admin = auth.get("user") == "admin" and auth.get("role") == "admin_ui"
        local_dev = auth.get("user") == "developer" and auth.get("role") == "dev_local"
        headers = {"X-Admin-UI": "true"} if is_admin or local_dev else {}
        return headers
    elif not isinstance(auth, dict) and not auth.access_token:
        raise HTTPException(status_code=401, detail="Unauthorized")
    else:
        return {"Authorization": f"Bearer {auth.access_token}"}


# Endpoint to get all Sharpoint Names here


@router.get("/sharepoint/folder/names")
async def get_sharepoint_folder_names(
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> JSONResponse:
    """Get SharePoint folder names for Beratungsanlässe.

    Args:
        collection_id: collection identifier(uuid).
        db_session: database session.
        auth: Azure AD authentication.

    Returns:
        JSONResponse: List of SharePoint names and their IDs.
    """
    headers = _create_headers(auth)

    try:
        site_data = client.get_site_data()
        site_id = client.get_site_id_by_name(site_data=site_data, target_name=SHAREPOINT_NAME)
        folder_ids = client.get_folder_id_by_path(site_id, SHAREPOINT_PATH)
        folder_content = client.get_folder_content(site_id=site_id, drive_id=folder_ids["drive_id"], folder_id=folder_ids["folder_id"])

        print(f"Folder content retrieved: {folder_content}")

        folder_names = []
        for item in folder_content:
            if item.get("is_folder"):
                folder_names.append(item.get("name"))

        return JSONResponse(
            content={"folder_names": folder_names},
            status_code=200,
        )
    except Exception as e:
        logger.error(f"An error occurred while retrieving SharePoint names: {str(e)}")
        return JSONResponse(content={"message": f"An error occurred: {str(e)}"}, status_code=500)


@router.post("/collection/{collection_id}/sharepoint/folder/{folder_name}")
async def upload_sharepoint_folder(
    collection_id: UUID,
    folder_name: str,
    db: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> JSONResponse:
    """Upload SharePoint documents from a specific folder to the embedding service.

    Args:
        collection_id: Collection identifier (UUID) where documents will be stored
        site_name: Name of the SharePoint site to process
        db_session: Database session
        auth: Azure AD authentication

    Returns:
        JSONResponse: Status message and summary of processing results
    """
    try:
        # Get site information
        site_data = client.get_site_data()
        site_id = client.get_site_id_by_name(site_data=site_data, target_name=SHAREPOINT_NAME)
        if not site_id:
            return JSONResponse(content={"message": f"Site '{SHAREPOINT_NAME}' not found"}, status_code=404)
        folder_ids = client.get_folder_id_by_path(site_id, f"{SHAREPOINT_PATH}/{folder_name}")

        if not folder_ids:
            return JSONResponse(content={"message": "No root folder found"}, status_code=404)

        results = await upload_folder_contents(
            site_id=site_id,
            drive_id=folder_ids["drive_id"],
            folder_id=folder_ids["folder_id"],
            collection_id=collection_id,
            db_session=db,
            auth=auth,
        )

        return JSONResponse(
            content={
                "message": "SharePoint site processed successfully",
                "results": {
                    "documents_uploaded": results.get("uploaded", 0),
                    "documents_failed": results.get("failed", 0),
                    "documents_skipped": results.get("skipped", 0),
                    "folders_processed": results.get("folders_processed", 0),
                },
            },
            status_code=200,
        )

    except Exception as e:
        logger.error(f"An error occurred while processing SharePoint site: {str(e)}")
        return JSONResponse(content={"message": f"An error occurred: {str(e)}"}, status_code=500)


async def upload_file(download_url, file_name, metadata=None, collection_id=None, db_session=None, auth=None):
    """
    Downloads a file from a URL and uploads it to the embedding service.
    Args:
        download_url (str): The URL of the file.
        file_name (str): The name of the file.
        metadata (dict, optional): Metadata for the document.
        collection_id (UUID, optional): Collection ID where the document should be uploaded.
        db_session (Session, optional): Database session.
        auth (Any, optional): Authentication object.
    """
    headers = {"Authorization": f"Bearer {client.access_token}"}
    response = requests.get(download_url, headers=headers)
    if response.status_code == 200:
        # Create an in-memory file object
        file_content = BytesIO(response.content)

        # Create a FastAPI UploadFile object
        upload_file = UploadFile(
            filename=file_name,
            file=file_content,
        )

        try:
            # Upload directly to embedding service
            upload_result = await api_upload_document(collection_id=collection_id, file=upload_file, document_metadata=metadata, db=db_session, auth=auth)
            logger.info(f"File uploaded to embedding service: {file_name}")
            return {"status": "success"}
        except Exception as e:
            logger.error(f"Failed to upload {file_name} to embedding service: {str(e)}")
            return {"status": "error", "message": f"Failed to process {file_name}: {str(e)}"}
    else:
        logger.error(f"Failed to download {file_name}: {response.status_code} - {response.reason}")
        return {"status": "error", "message": f"Failed to download {file_name}: {response.status_code}"}


async def upload_folder_contents(site_id, drive_id, folder_id, collection_id, db_session, auth, level=0, max_level=None):
    """
    Recursively downloads all contents from a SharePoint folder and uploads them
    directly to the embedding service.

    Args:
        client: SharePoint client instance
        site_id (str): SharePoint site ID
        drive_id (str): Drive ID
        folder_id (str): Folder ID
        collection_id (UUID): Collection ID where documents should be uploaded
        db_session: Database session
        auth: Authentication object
        level (int): Current recursion depth (default: 0)
        max_level (int, optional): Maximum recursion depth (default: None for unlimited)

    Returns:
        dict: Summary of upload operations
    """
    # Check if we've reached the max recursion level
    if max_level is not None and level > max_level:
        return {"status": "skipped", "reason": "max recursion depth reached"}

    results = {"uploaded": 0, "failed": 0, "skipped": 0, "folders_processed": 0}

    # Fetch folder contents
    folder_contents_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drives/{drive_id}/items/{folder_id}/children"
    contents_headers = {"Authorization": f"Bearer {client.access_token}"}
    contents_response = requests.get(folder_contents_url, headers=contents_headers)

    if contents_response.status_code != 200:
        logger.error(f"Failed to get folder contents: {contents_response.status_code}")
        return {"status": "error", "message": "Failed to retrieve folder contents"}

    folder_contents = contents_response.json()

    if "value" in folder_contents:
        # Process each item in the folder
        for item in folder_contents["value"]:
            # Handle subfolders
            if "folder" in item:
                results["folders_processed"] += 1
                folder_name = item["name"]
                logger.info(f"Processing subfolder: {folder_name} (level {level})")

                # Recursive call for subfolders
                subfolder_results = await upload_folder_contents(
                    site_id=site_id, drive_id=drive_id, folder_id=item["id"], collection_id=collection_id, db_session=db_session, auth=auth, level=level + 1, max_level=max_level
                )

                # Merge results
                for key in results:
                    if key in subfolder_results and isinstance(results[key], int):
                        results[key] += subfolder_results.get(key, 0)

            # Handle files
            elif "file" in item:
                file_name = item["name"]
                file_metadata = {"sourceSystem": "SharePoint", "siteId": site_id, "driveId": drive_id, "itemId": item["id"], "fileName": file_name}

                # Get file download URL
                file_download_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drives/{drive_id}/items/{item['id']}/content"

                # Upload the file directly to embedding service
                upload_result = await upload_file(
                    download_url=file_download_url, file_name=file_name, metadata=file_metadata, db_session=db_session, collection_id=collection_id, auth=auth
                )

                if upload_result.get("status") != "success":
                    results["failed"] += 1
                else:
                    results["uploaded"] += 1

    return results
