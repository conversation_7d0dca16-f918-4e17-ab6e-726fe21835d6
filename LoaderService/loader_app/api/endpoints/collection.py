import logging
from typing import Any, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, Query
from loader_app.api.handlers.collection import (
    handle_delete_collection,
    handle_get_collection,
    handle_get_collection_by_document_id,
    handle_get_collection_token_usage,
    handle_get_collections,
    handle_patch_collection,
    handle_post_collection,
)
from loader_app.api.models.request import CollectionRequest, UpdateCollectionRequest
from loader_app.api.models.response import (
    CollectionResponseItem,
    CollectionTokenUsageResponse,
    CreateCollectionResponse,
    GetCollectionsResponse,
    MessageResponse,
)
from loader_app.database.session import get_db
from loader_app.services.auth import get_auth
from loader_app.services.rbac import roles_required
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/collections", response_model=GetCollectionsResponse)
async def api_get_collections(
    db_session: Session = Depends(get_db),
    auth: Any = Depends(get_auth),
) -> GetCollectionsResponse:
    """Retrieve all collections for the authenticated user."""
    return await handle_get_collections(db_session, auth)


@router.post("/collection", response_model=CreateCollectionResponse)
async def api_post_collection(
    collection_request: CollectionRequest,
    db_session: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> CreateCollectionResponse:
    """Create a new collection."""
    return await handle_post_collection(db_session, collection_request)


@router.get(
    "/collection",
    response_model=CollectionResponseItem,
    responses={404: {"model": MessageResponse}, 400: {"model": MessageResponse}},
)
async def api_get_collection(
    collection_id: Optional[UUID] = Query(default=None),
    collection_name: Optional[str] = Query(default=None),
    db_session: Session = Depends(get_db),
    auth=Depends(get_auth),
) -> CollectionResponseItem:
    """
    Retrieve a collection by its ID or name.
    """
    return await handle_get_collection(db_session, auth, collection_id, collection_name)


@router.delete(
    "/collection/{collection_id}",
    response_model=MessageResponse,
    responses={404: {"model": MessageResponse}, 500: {"model": MessageResponse}},
)
async def api_delete_collection(
    collection_id: UUID,
    db_session: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> MessageResponse:
    """Delete a collection by its ID."""
    return await handle_delete_collection(db_session, collection_id)


@router.patch(
    "/collection/{collection_id}",
    response_model=MessageResponse,
    responses={
        400: {"model": MessageResponse},
        404: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_patch_collection(
    collection_id: UUID,
    update_request: UpdateCollectionRequest,
    db_session: Session = Depends(get_db),
    auth: Any = Depends(roles_required({"admin", "expert", "service_access"})),
) -> MessageResponse:
    """Update properties of an existing collection."""
    return await handle_patch_collection(db_session, update_request, collection_id, auth)


@router.get(
    "/document/{document_id}/collection",
    response_model=CollectionResponseItem,
    responses={404: {"model": MessageResponse}},
)
async def api_get_collection_by_document_id(
    document_id: UUID,
    db_session: Session = Depends(get_db),
    auth=Depends(get_auth),
) -> CollectionResponseItem:
    """Retrieve the collection associated with a specific document ID."""
    return await handle_get_collection_by_document_id(db_session, auth, document_id)


@router.get("/collection/{collection_id}/token-usage", response_model=CollectionTokenUsageResponse)
async def api_get_collection_token_usage(collection_id: UUID, db_session: Session = Depends(get_db), auth=Depends(get_auth)) -> CollectionTokenUsageResponse:
    """
    Return the token usage of the collection based on the configured prompt, chunk size, top_k, and model.
    """
    return await handle_get_collection_token_usage(db_session, collection_id)
