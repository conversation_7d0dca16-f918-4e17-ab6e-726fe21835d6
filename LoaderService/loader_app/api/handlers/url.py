"""Handlers for URL-related operations in the Loader service."""

import logging
from typing import Any, List, Optional
from uuid import UUID

from fastapi import HTTPException
from loader_app.api.core.collection import verify_collection_access
from loader_app.api.core.url import scrape_text_from_url
from loader_app.api.models.request import UpdateUrlRequest, UploadUrlRequest
from loader_app.api.models.response import (
    CollectionResponseItem,
    GetUrlIdsByCollectionNameResponse,
    GetUrlsResponse,
    GetUrlStatusResponse,
    MessageResponse,
    UploadBatchUrlResponse,
    UploadUrlResponse,
    UrlItem,
)
from loader_app.api.utils.helpers import create_headers
from loader_app.crud.url import (
    delete_url,
    delete_urls_by_collection_id,
    get_url,
    get_urls,
    get_urls_ids,
    insert_or_update_url,
    update_url,
)
from loader_app.enums.document import DocumentEventsEnum, DocumentStatusEnum
from loader_app.tasks.url import move_url_forward
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


async def handle_delete_urls_by_collection(db: Session, auth, collection_id: UUID) -> MessageResponse:
    """Delete all URLs belonging to a specific collection."""
    verify_collection_access(
        db,
        auth,
        collection_id=collection_id,
        on_forbidden="raise",
    )
    try:
        delete_urls_by_collection_id(db, collection_id)
    except Exception:
        logger.exception(f"Failed to delete URLs for collection {collection_id}")
        raise HTTPException(status_code=500, detail="Failed to delete URLs for this collection")

    return MessageResponse(message=f"All URLs for collection {collection_id} deleted successfully")


async def handle_upload_url(
    db: Session,
    auth: Any,
    collection_id: UUID,
    url_request: UploadUrlRequest,
) -> UploadUrlResponse:
    """Upload a single URL and dispatch processing."""
    verify_collection_access(
        db,
        auth,
        collection_id=collection_id,
        on_forbidden="raise",
    )
    try:
        text_content = await scrape_text_from_url(url_request.url)
    except Exception:
        logger.exception("Error scraping URL")
        raise HTTPException(status_code=400, detail="Unable to scrape text from URL")

    try:
        url_id = insert_or_update_url(db, collection_id, url_request.url, text_content)
    except ValueError as ve:
        if str(ve) == "url_already_exists":
            raise HTTPException(status_code=409, detail="URL already exists in this collection")
        logger.exception("Duplicate URL error")
        raise HTTPException(status_code=400, detail="Duplicate URL")
    except Exception:
        logger.exception("Error saving URL to database")
        raise HTTPException(status_code=500, detail="Unable to store URL")

    if not url_id:
        raise HTTPException(status_code=400, detail="Unable to upload URL")

    move_url_forward(
        str(url_id),
        DocumentEventsEnum.LOAD_REQUEST.value,
        headers=create_headers(auth),
    )

    return UploadUrlResponse(url_id=url_id, status=DocumentStatusEnum.ADDED)


async def handle_batch_upload_url(
    db: Session,
    auth: Any,
    collection_id: UUID,
    urls: List[str],
) -> UploadBatchUrlResponse:
    """Upload a batch of URLs and trigger background processing."""
    verify_collection_access(
        db,
        auth,
        collection_id=collection_id,
        on_forbidden="raise",
    )

    url_ids: List[str] = []

    for url in urls:
        try:
            text_content = await scrape_text_from_url(url)
            url_id = insert_or_update_url(db, collection_id, url, text_content)
            url_ids.append(str(url_id))

            move_url_forward(
                str(url_id),
                DocumentEventsEnum.LOAD_REQUEST.value,
                headers=create_headers(auth),
            )
        except Exception:
            logger.exception(f"Error processing URL: {url}")
            raise HTTPException(status_code=500, detail="Failed to process one or more URLs")

    return UploadBatchUrlResponse(
        url_ids=url_ids,
        message=f"{len(url_ids)} URL(s) uploaded.",
    )


async def handle_get_url_status(
    db: Session,
    auth: Any,
    collection_id: UUID,
    url_id: UUID,
) -> GetUrlStatusResponse:
    """Return the status of a specific URL by ID and collection."""

    verify_collection_access(
        db,
        auth,
        collection_id=collection_id,
        on_forbidden="raise",
    )

    try:
        url = get_url(db, item_id=url_id, collection_id=collection_id)
    except Exception:
        logger.exception("DB error while fetching URL")
        raise HTTPException(status_code=500, detail="Database error")

    if not url:
        raise HTTPException(status_code=404, detail="URL not found")

    return GetUrlStatusResponse(status=DocumentStatusEnum(url.status))


async def handle_delete_url(
    db: Session,
    auth: Any,
    url_id: UUID,
    collection_id: UUID | None = None,
) -> MessageResponse:
    """Delete a URL by ID, optionally checking collection ownership."""

    verify_collection_access(
        db,
        auth,
        collection_id=collection_id,
        on_forbidden="raise",
    )

    try:
        url = get_url(db, item_id=url_id, collection_id=collection_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid URL ID")

    if not url:
        raise HTTPException(status_code=404, detail="URL not found")

    try:
        delete_url(db, url)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

    return MessageResponse(message="ok")


async def handle_update_url(
    db: Session,
    auth: Any,
    url_id: UUID,
    update_data: UpdateUrlRequest,
    collection_id: Optional[UUID] = None,
) -> MessageResponse:
    """Update the status or content of a specific URL."""

    verify_collection_access(
        db,
        auth,
        collection_id=collection_id,
        on_forbidden="raise",
    )

    try:
        url = get_url(db, item_id=url_id, collection_id=collection_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid URL ID")

    if not url:
        raise HTTPException(status_code=404, detail="URL not found")

    try:
        update_url(
            db,
            item_id=url_id,
            collection_id=collection_id,
            status=update_data.status,
            content=update_data.content,
        )
    except Exception:
        logger.exception("Failed to update URL")
        raise HTTPException(status_code=500, detail="Update failed")

    return MessageResponse(message="updated")


async def handle_get_url(
    db: Session,
    auth: Any,
    url_id: UUID,
    collection_id: Optional[UUID] = None,
    collection_name: Optional[str] = None,
) -> UrlItem:
    """Fetch a URL and return its full metadata."""

    verify_collection_access(
        db,
        auth,
        collection_id=collection_id,
        on_forbidden="raise",
    )

    try:
        url = get_url(db, item_id=url_id, collection_id=collection_id, collection_name=collection_name)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid URL ID")

    if not url:
        raise HTTPException(status_code=404, detail="URL not found")

    return UrlItem.model_validate(url)


async def handle_get_urls(db: Session, auth, collection_id: UUID) -> GetUrlsResponse:
    """Get all URLs for a given collection, with truncated content."""

    verify_collection_access(
        db,
        auth,
        collection_id=collection_id,
        on_forbidden="raise",
    )

    try:
        urls = get_urls(db, collection_id=collection_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid collection ID")
    except Exception:
        logger.exception("failed to fetch urls")
        raise HTTPException(status_code=500, detail="Failed to fetch URLs")

    max_content_length = 1000  # Define a maximum content length for truncation

    url_models = [
        UrlItem.model_validate(
            {
                **url.to_dict(),
                "content": url.content[:max_content_length] + "..." if len(url.content) > max_content_length else url.content,
            }
        )
        for url in urls
    ]

    return GetUrlsResponse(urls=url_models)


async def handle_get_url_ids_by_collection_name(
    db: Session,
    auth: Any,
    collection_name: str,
) -> GetUrlIdsByCollectionNameResponse:
    """Get URL IDs by collection name."""

    verify_collection_access(
        db,
        auth,
        collection_name=collection_name,
        on_forbidden="raise",
    )

    try:
        url_ids = get_urls_ids(db, collection_name=collection_name)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid request")
    except LookupError:
        raise HTTPException(status_code=404, detail="Collection not found")
    except Exception:
        logger.exception("Unexpected error while fetching URL IDs")
        raise HTTPException(status_code=500, detail="Internal server error")

    return GetUrlIdsByCollectionNameResponse(url_ids=url_ids)


async def handle_get_collection_by_url_id(db: Session, auth, url_id: UUID) -> CollectionResponseItem:
    """Return the collection metadata for a given URL ID."""
    try:
        url = get_url(db, item_id=url_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid URL ID")

    verify_collection_access(
        db,
        auth,
        collection_id=url.collection.id,
        on_forbidden="raise",
    )

    if not url or not url.collection:
        raise HTTPException(status_code=404, detail="Collection not found")

    return CollectionResponseItem.model_validate(url.collection)
