import logging
from typing import Any, List, Optional
from uuid import UUID

from fastapi import HTT<PERSON>Exception
from loader_app.api.core.collection import (
    verify_collection_access,
    verify_collection_exists,
)
from loader_app.api.models.request import CollectionRequest, UpdateCollectionRequest
from loader_app.api.models.response import (
    CollectionResponseItem,
    CollectionTokenUsageResponse,
    CreateCollectionResponse,
    GetCollectionsResponse,
    MessageResponse,
)
from loader_app.api.utils.helpers import calculate_token_usage_final, create_headers
from loader_app.crud import collection as crud_collection
from loader_app.service_clients.asynchronous import rename_collection_request
from loader_app.services.chunker import DEFAULTS
from loader_app.services.rbac import (
    PRIORITY,
    Role,
    can_user_see,
    get_entra_auth_user_info,
)
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


async def handle_get_collections(
    db_session: Session,
    auth: Any,
) -> GetCollectionsResponse:
    """
    Return only the collections the authenticated user is allowed to see.

    Visibility rules
    ----------------
    * ``admin``  → sees *admin*, *expert*, *basic*, and *untagged* collections.
    * ``expert`` → sees *expert*, *basic*, and *untagged* collections.
    * ``basic``  → sees *basic* and *untagged* collections.

    A collection **without** ``custom_config["role"]`` is considered *basic*.
    """

    # 1. Fetch everything from DB
    all_collections = crud_collection.get_collections(db_session)

    # 2. Extract user-info and determine the *highest* role the user has
    user_info, _ = get_entra_auth_user_info(auth)
    roles: List[str] = user_info.get("roles", ["basic"])
    user_role: str = max(
        roles,
        key=lambda r: PRIORITY.get(Role.from_str(r), 1),
    )

    # 3. Filter with role hierarchy
    visible_collections = [
        c
        for c in all_collections
        if can_user_see(
            user_role,
            (c.custom_config or {}).get("role"),  # None ⇒ treated as "basic"
        )
    ]

    logger.debug(
        "handle_get_collections – user_role=%s visible=%d/%d",
        user_role,
        len(visible_collections),
        len(all_collections),
    )

    # 4. Build the response
    return GetCollectionsResponse(
        collections=[CollectionResponseItem.model_validate(c) for c in visible_collections],
        user_info=user_info,
    )


async def handle_post_collection(db_session: Session, collection_request: CollectionRequest) -> CreateCollectionResponse:
    """
    Handle the creation of a new collection.
    """
    collection, error_message = crud_collection.insert_collection(db_session, collection_request)
    if error_message:
        raise HTTPException(status_code=400, detail=error_message)

    return CreateCollectionResponse(collection_id=collection.id, message="ok")


async def handle_get_collection(
    db_session: Session,
    auth: Any,
    collection_id: Optional[UUID],
    collection_name: Optional[str],
) -> CollectionResponseItem:
    """
    Return the requested collection **only if** the authenticated user is
    authorised to see it.

    The RBAC / visibility check is delegated to
    :func:`verify_collection_access`.  Untagged collections (no ``role`` in
    ``custom_config``) are treated as **basic**.
    """
    collection = verify_collection_access(
        db_session,
        auth,
        collection_id=collection_id,
        collection_name=collection_name,
        on_forbidden="raise",  # default; may be omitted
    )

    return CollectionResponseItem.model_validate(collection)


async def handle_delete_collection(db_session: Session, collection_id: UUID) -> MessageResponse:
    """
    Handle deletion of a collection by ID.
    """
    success, message = crud_collection.delete_collection(db_session, collection_id)

    if not success:
        if message == "Collection not found":
            raise HTTPException(status_code=404, detail=message)
        raise HTTPException(status_code=500, detail=message)

    return MessageResponse(message=message)


async def handle_patch_collection(
    db_session: Session,
    update_request: UpdateCollectionRequest,
    collection_id: UUID,
    auth: Any,
) -> MessageResponse:
    """
    Handle patching a collection with provided update data.
    """
    verify_collection_exists(db_session, collection_id=collection_id)
    update_data = update_request.model_dump()
    if not update_data:
        raise HTTPException(status_code=400, detail="No update data provided")

    if "name" in update_data:
        await rename_collection_request(collection_id=str(collection_id), new_name=update_data["name"], headers=create_headers(auth=auth))

    crud_collection.update_collection(db_session, collection_id=collection_id, **update_data)

    return MessageResponse(message="ok")


async def handle_get_collection_by_document_id(
    db_session: Session,
    auth: Any,
    document_id: UUID,
) -> CollectionResponseItem:
    """
    Return the collection owning *document_id* **after** RBAC verification.

    Untagged collections (no ``role`` in ``custom_config``) are treated as
    **basic**; the visibility hierarchy is enforced by
    :func:`verify_collection_access`.
    """
    collection = verify_collection_access(
        db_session,
        auth,
        document_id=document_id,
        on_forbidden="raise",
    )

    return CollectionResponseItem.model_validate(collection)


async def handle_get_collection_token_usage(db_session: Session, collection_id: UUID) -> CollectionTokenUsageResponse:
    """Calculate and return token usage for a specific collection."""
    collection_data = verify_collection_exists(db_session, collection_id=collection_id)

    if not collection_data:
        raise HTTPException(status_code=404, detail="Collection not found")

    custom_config = collection_data.custom_config or {}

    chunking_strategy = custom_config.get("chunking_strategy", "recursive")
    chunk_size = custom_config.get("chunk_size")
    top_k = custom_config.get("top_k", 5)
    llm_model = custom_config.get("llm_model", "gpt-4")
    system_prompt = custom_config.get("system_prompt", "")
    chunk_overlap = custom_config.get("chunk_overlap", 0)

    if chunk_size is None:
        chunk_size = DEFAULTS.get(chunking_strategy).get("size")
        chunk_overlap = DEFAULTS.get(chunking_strategy).get("overlap")

    usage_data = calculate_token_usage_final(
        system_prompt=system_prompt,
        llm_model=llm_model,
        top_k=top_k,
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        chunking_strategy=chunking_strategy,
    )

    return CollectionTokenUsageResponse(**usage_data)
