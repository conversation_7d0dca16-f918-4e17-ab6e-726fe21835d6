"""Document API Handlers."""

from __future__ import annotations

import logging
from typing import Any, List, Optional
from uuid import UUID

from fastapi import HTTPException, UploadFile
from loader_app.api.core.collection import (
    verify_collection_access,
    verify_collection_exists,
)
from loader_app.api.core.document import (
    parse_file,
    process_and_store_document,
    process_batch_documents,
    process_reload_document,
)
from loader_app.api.models.request import (
    UpdateDocumentRequest,
    UploadBatchDocumentRequest,
    UploadDocumentRequestMetadata,
)
from loader_app.api.models.response import (
    DocumentResponseItem,
    GetDocumentResponse,
    GetDocumentsByCollectionNameResponse,
    GetDocumentsResponse,
    GetDocumentStatusResponse,
    MessageResponse,
    ProcessDocumentResponse,
    UploadBatchDocumentResponse,
    UploadDocumentResponse,
)
from loader_app.api.utils.exceptions import (
    DatabaseInsertError,
    DocumentNotFoundError,
    FileStorageError,
    InvalidDocumentStatusError,
    InvalidFolderPathError,
    NoFilesFoundError,
    PDFPageLimitExceededError,
    UnsupportedFileTypeError,
)
from loader_app.crud.document import (
    delete_document,
    get_document,
    get_document_ids_by_collection_name,
    get_documents,
    update_document,
)
from loader_app.enums.document import DocumentStatusEnum
from loader_app.enums.loader import LoadMethodEnum
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


async def handle_upload_document(
    db: Session,
    auth: Any,
    collection_id: UUID,
    file: UploadFile,
    document_metadata: Optional[str],
    load_method: LoadMethodEnum,
) -> UploadDocumentResponse:
    """Handles the upload of a single document."""
    verify_collection_exists(db, collection_id=collection_id)

    metadata = UploadDocumentRequestMetadata.model_validate(document_metadata) if isinstance(document_metadata, str) else UploadDocumentRequestMetadata()

    try:
        document_id = await process_and_store_document(db, collection_id, file, metadata, auth, load_method)
    except FileStorageError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except DatabaseInsertError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception:
        logger.exception("Unexpected upload error")
        raise HTTPException(status_code=500, detail="Unexpected error")

    return UploadDocumentResponse(document_id=document_id, status=DocumentStatusEnum.ADDED.value)


async def handle_batch_upload(
    db: Session,
    auth: Any,
    collection_id: UUID,
    body: UploadBatchDocumentRequest,
) -> UploadBatchDocumentResponse:
    """Handles the upload of a batch of documents."""
    verify_collection_exists(db, collection_id=collection_id)

    try:
        document_ids = await process_batch_documents(db, auth, collection_id, body.folder_path)
    except InvalidFolderPathError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NoFilesFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseInsertError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception:
        logger.exception("Unexpected error in batch upload")
        raise HTTPException(status_code=500, detail="Unexpected batch upload error")

    return UploadBatchDocumentResponse(
        document_ids=document_ids,
        message=f"{len(document_ids)} file(s) queued for load documents.",
    )


async def handle_reload_document(
    db: Session,
    auth: Any,
    collection_id: UUID,
    document_id: UUID,
    load_method: LoadMethodEnum,
) -> ProcessDocumentResponse:
    """Handles the reloading of a document."""
    verify_collection_exists(db, collection_id=collection_id)

    try:
        await process_reload_document(
            db=db,
            auth=auth,
            collection_id=collection_id,
            document_id=document_id,
            load_method=load_method,
        )
    except DocumentNotFoundError:
        raise HTTPException(status_code=404, detail="Document not found")
    except InvalidDocumentStatusError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception:
        logger.exception("Unexpected error while reloading document")
        raise HTTPException(status_code=500, detail="Reload event dispatch failed")

    return ProcessDocumentResponse(status="queued")


async def handle_get_document_status(
    db: Session,
    auth: Any,
    collection_id: UUID,
    document_id: UUID,
) -> GetDocumentStatusResponse:
    """Handles fetching the status of a document."""

    verify_collection_access(
        db,
        auth,
        collection_id=collection_id,
        document_id=document_id,
        on_forbidden="raise",
    )

    try:
        doc = get_document(db, collection_id=collection_id, document_id=document_id)
    except Exception:
        logger.exception("Error fetching document from DB")
        raise HTTPException(status_code=500, detail="Database error")

    if not doc:
        raise HTTPException(status_code=404, detail="Document not found")

    return GetDocumentStatusResponse(status=DocumentStatusEnum(doc.status))


async def handle_get_document(
    db: Session,
    auth: Any,
    collection_id: Optional[UUID],
    document_id: UUID,
) -> GetDocumentResponse:
    """Handles fetching a single document by ID."""
    verify_collection_access(
        db,
        auth,
        collection_id=collection_id,
        document_id=document_id,
        on_forbidden="raise",
    )
    try:
        doc = get_document(db_session=db, document_id=document_id, collection_id=collection_id)
    except SQLAlchemyError:
        logger.exception("Database error while fetching document")
        raise HTTPException(status_code=500, detail="Database error")
    except Exception:
        logger.exception("Unexpected error fetching document")
        raise HTTPException(status_code=500, detail="Unexpected error")

    if not doc:
        raise HTTPException(status_code=404, detail="Document not found")

    try:
        return GetDocumentResponse(document=DocumentResponseItem.model_validate(doc))
    except Exception:
        logger.exception("Error serializing document")
        raise HTTPException(status_code=500, detail="Document serialization failed")


async def handle_list_documents(
    db: Session,
    auth: Any,
    collection_id: UUID,
) -> GetDocumentsResponse:
    """Handles fetching all documents in a collection."""
    collection = verify_collection_access(
        db,
        auth,
        collection_id=collection_id,
        on_forbidden="raise",
    )
    if collection is None:
        return GetDocumentsResponse(documents=[])
    try:
        docs = get_documents(db, collection_id=collection_id)
    except SQLAlchemyError:
        logger.exception("Database error fetching documents")
        raise HTTPException(status_code=500, detail="Database error")
    except Exception:
        logger.exception("Unexpected error")
        raise HTTPException(status_code=500, detail="Unexpected error")

    items = [DocumentResponseItem.model_validate(d) for d in docs]
    return GetDocumentsResponse(documents=items)


async def handle_list_document_ids(
    db: Session,
    auth: Any,
    collection_name: str,
) -> GetDocumentsByCollectionNameResponse:
    """Handles fetching document IDs by collection name."""
    collection = verify_collection_access(
        db,
        auth,
        collection_name=collection_name,
        on_forbidden="empty",
    )
    if collection is None:
        return GetDocumentsByCollectionNameResponse(document_ids=[])
    try:
        ids = get_document_ids_by_collection_name(db, collection_name)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception:
        logger.exception("Unhandled error fetching document IDs")
        raise HTTPException(status_code=500, detail="Internal server error")

    return GetDocumentsByCollectionNameResponse(document_ids=ids)


async def handle_get_content_from_file(files: List[UploadFile]) -> MessageResponse:
    """Handles extracting text content from uploaded files."""
    try:
        full_text = ""
        for file in files:
            content = await parse_file(file)
            full_text += f"Content from {file.filename}: {content}\n"

        return MessageResponse(message=full_text)

    except UnsupportedFileTypeError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except PDFPageLimitExceededError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.exception("Unhandled error during file parsing", exc_info=e)
        raise HTTPException(status_code=500, detail="Internal server error")


async def handle_update_document(
    db: Session,
    document_id: UUID,
    body: UpdateDocumentRequest,
) -> MessageResponse:
    """Handles updating a document's metadata and status."""
    try:
        update_document(
            db_session=db,
            document_id=document_id,
            collection_id=body.collection_id,
            file_path=body.file_path,
            status=body.status,
            document_metadata=body.document_metadata,
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception:
        logger.exception("Database error updating document")
        raise HTTPException(status_code=500, detail="Database error")

    return MessageResponse(message="Document updated successfully")


async def handle_delete_document(
    db: Session,
    document_id: UUID,
    collection_id: Optional[UUID],
) -> MessageResponse:
    """Handles deleting a document by ID."""
    try:
        delete_document(
            db_session=db,
            document_id=document_id,
            collection_id=collection_id,
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception:
        logger.exception("Database error deleting document")
        raise HTTPException(status_code=500, detail="Database error")

    return MessageResponse(message="Document deleted successfully")
