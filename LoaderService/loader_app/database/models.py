from loader_app.database import Base
from loader_app.database.utils import generate_uuid
from sqlalchemy import JSO<PERSON>, Column, DateTime, ForeignKey, String, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func


class Collection(Base):
    """Model - collection.

    Attributes:
        id (UUID): collection identifier.
        name (str): collection name.
        description (str): collection description.
        custom_config (dict): collection customizations. system_prompt, llm etc.
        created_at (DateTime): creation timestamp.
        last_updated (DateTime): update timestamp.
    """

    __tablename__ = "collections"

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=generate_uuid,
        unique=True,
        nullable=False,
    )
    name = Column(String, unique=True)
    description = Column(String)
    custom_config = Column(JSON)
    created_at = Column(DateTime, default=func.now())
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    documents = relationship("Document", back_populates="collection", lazy="subquery")
    urls = relationship("URL", back_populates="collection", lazy="subquery")

    def to_dict(self):
        """Convert Collection model to dictionary."""
        return {
            "id": str(self.id),
            "name": self.name,
            "description": self.description,
            "custom_config": self.custom_config,
            "created_at": self.created_at.isoformat(),
            "last_updated": self.last_updated.isoformat(),
        }


class Document(Base):
    """Model - document.

    Attributes:
        id (UUID): document identifier.
        collection_id (UUID, optional): collection identifier.
        file_path (str): file path.
        document_metadata (JSON): document metadata.
        status (str): document status.
        created_at (DateTime): creation timestamp.
        last_updated (DateTime): update timestamp.
    """

    __tablename__ = "documents"

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=generate_uuid,
        unique=True,
        nullable=False,
    )
    collection_id = Column(UUID(as_uuid=True), ForeignKey("collections.id"), unique=False, nullable=True)
    file_path = Column(String)  # TODO: azure blob storage
    document_metadata = Column(JSON)  # approx. 6k
    status = Column(String)
    created_at = Column(DateTime, default=func.now())
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())

    collection = relationship("Collection", back_populates="documents", lazy="subquery")

    def to_dict(self):
        """Convert Document model to dictionary."""
        return {
            "id": str(self.id),
            "collection_id": str(self.collection_id),
            "file_path": self.file_path,
            "document_metadata": self.document_metadata,
            "status": self.status,
            "created_at": self.created_at.isoformat(),
            "last_updated": self.last_updated.isoformat(),
            "collection_name": self.collection.name if self.collection else None,
        }


class URL(Base):
    """Model - URL.
    Attributes:
        id (UUID): URL identifier.
        collection_id (UUID): collection identifier.
        url (str): URL string.
        content (str): content fetched from the URL.
        created_at (DateTime): creation timestamp.
        status (str, optional): status of the URL processing.
        last_updated (DateTime): update timestamp.
    """

    __tablename__ = "urls"

    __table_args__ = (UniqueConstraint("collection_id", "url", name="uq_collection_url"),)

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=generate_uuid,
        unique=True,
        nullable=False,
    )
    collection_id = Column(UUID(as_uuid=True), ForeignKey("collections.id"), unique=False, nullable=False)
    url = Column(String, nullable=False)
    content = Column(String, nullable=False)
    created_at = Column(DateTime, default=func.now())
    status = Column(String, unique=False, nullable=True)
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())

    collection = relationship("Collection", back_populates="urls", lazy="subquery")

    def to_dict(self):
        """Convert URL model to dictionary."""
        return {
            "id": str(self.id),
            "collection_id": str(self.collection_id),
            "url": self.url,
            "content": self.content,
            "status": self.status,
            "created_at": self.created_at.isoformat(),
            "last_updated": self.last_updated.isoformat(),
        }
