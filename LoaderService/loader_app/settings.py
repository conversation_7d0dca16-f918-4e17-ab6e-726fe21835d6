"""Application settings module for LoaderService.

Defines environment-specific configurations and service integration parameters.
Supports OpenAI, Azure, Redis, Postgres SQL, RabbitMQ, chunking behavior, and OpenAPI scopes.
"""

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """App settings."""

    is_debug: bool = False
    is_entra_id_auth_on: bool = True

    app_host: str = "0.0.0.0"
    app_port: int = 8001

    # services
    pdfs_data_dir: str = "/data/tmp"

    # OpenAI
    embedding_ada_model_name: str = "text-embedding-ada-002"
    embedding_ada_deployment_name: str = "ada3"
    gpt4_model_name: str = "gpt-4"
    gpt4_deployment_name: str = "gpt4"
    gpt4o2_model_name: str = "gpt-4o"
    gpt4o2_deployment_name: str = "gpt4o"
    gpt4o_model_name: str = "gpt-4o"
    gpt4o_deployment_name: str = "gpt4o"
    gpt4o_mini_model_name: str = "gpt-4o-mini"
    gpt4o_mini_deployment_name: str = "gpt4o-mini"

    openai_api_type: str = ""
    openai_api_version: str = ""
    azure_openai_endpoint: str = ""
    openai_api_key: str = ""

    # Open Source
    llama3_1_instruct_model_name: str = "Meta-Llama-3.1-70B-Instruct"
    mixtral_model_name: str = "Mistral-8B"
    phi4_model_name: str = "Phi-4"
    bgem3_model_name: str = "bge-m3"
    opensource_llm_api_base: str = ""
    opensource_llm_api_key: str = ""

    # default values
    default_model_temperature: float = 0.7
    default_llm_multi_answer_count: int = 4
    default_embedding_model: str = "text-embedding-ada-002"
    default_llm_model: str = "gpt-4o"
    default_search_method: str = "semantic_search"
    langchain_debug: bool = False
    restricted_indexing: bool = True  # to avoid bottleneck in openai services
    evaluator_log_dir: str = "/data/evaluation_logs/"
    max_token_count: int = 4096
    max_token_count_for_small_models: int = 1024
    semantic_search_top_k: int = 5
    hyde_top_k: int = 5
    hyde_generation_max_token_count: int = 256

    # chunker
    chunk_size: int = 2048
    chunk_overlap: int = 200
    chunking_strategy: str = "recursive"  # Options: "recursive", "token", "headline", "markdown"
    separators: list = [
        "\n\n",
        "\n",
        r"(?<=\. )",  # <--- raw string!
        " ",
        "",
        ".",
        ",",
        "\u200b",  # Zero-width space
        "\uff0c",  # Fullwidth comma
        "\u3001",  # Ideographic comma
        "\uff0e",  # Fullwidth full stop
        "\u3002",  # Ideographic full stop
    ]

    # dramatiq
    dramatiq_name_space: str = "embedding-dramatiq"
    dramatiq_task_time_limit_ms: int = 3600000  # 60 minutes
    dramatiq_task_max_retries: int = 5
    dramatiq_task_max_age_ms: int = 86400000  # 24 hour
    dramatiq_queue_prefix: str = "prod"
    redis_host: str = "redis"
    redis_port: int = 6379
    dramatiq_redis_db: int = 0
    embeddings_redis_db: int = 1
    redis_query_history_db: int = 2
    redis_url: str = f"redis://{redis_host}:{redis_port}"
    redis_dramatiq_url: str = f"redis://{redis_host}:{redis_port}/{dramatiq_redis_db}"
    redis_embeddings_url: str = f"redis://{redis_host}:{redis_port}/{embeddings_redis_db}"
    redis_query_history_url: str = f"redis://{redis_host}:{redis_port}/{redis_query_history_db}"

    # rabbitmq
    rabbitmq_user: str = "guest"
    rabbitmq_password: str = "guest"
    rabbitmq_host: str = "rabbitmq"
    rabbitmq_port: int = 5672
    rabbitmq_url: str = f"amqp://{rabbitmq_user}:{rabbitmq_password}@{rabbitmq_host}:{rabbitmq_port}"
    rabbitmq_vhost: str = "/"

    # postgres
    postgres_host: str = "postgres_loader"
    postgres_port: int = 5432
    postgres_db: str = "postgres_loader"
    postgres_user: str = "postgres_loader"
    postgres_password: str = "postgres_loader"
    db_url: str = f"postgresql+psycopg://{postgres_user}:{postgres_password}@{postgres_host}:{postgres_port}/${postgres_db}"

    # Azure AD
    app_client_id: str = ""
    app_client_secret: str = ""
    tenant_id: str = ""
    openapi_client_id: str = ""
    scope_description: str = "user_impersonation"
    client_ui_url: str = "http://localhost:8501"

    # Service Clients
    indexer_base_url: str = "http://indexer:8001"
    loader_base_url: str = "http://loader:8002"
    query_base_url: str = "http://query:8003"

    @property
    def scope_name(self) -> str:
        """Return the OAuth2 scope name used in OpenAPI and Azure AD."""
        return f"api://{self.app_client_id}/{self.scope_description}"

    @property
    def scopes(self) -> dict:
        """Return a dictionary of scopes with descriptions for OAuth2."""
        return {
            self.scope_name: self.scope_description,
        }

    @property
    def openapi_authorization_url(self) -> str:
        """Return the OAuth2 authorization URL used by OpenAPI Swagger."""
        return f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/authorize"

    @property
    def openapi_token_url(self) -> str:
        """Return the OAuth2 token URL used by OpenAPI Swagger."""
        return f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"

    # Atlassian OAuth Settings
    atlassian_client_id: str = Field(default="", env="ATLASSIAN_CLIENT_ID")
    atlassian_client_secret: str = Field(default="", env="ATLASSIAN_CLIENT_SECRET")

    # Service URLs for OAuth callbacks
    # loader_base_url already defined above

    class Config(object):
        """Config for pydantic base settings."""

        case_sensitive = False


settings = Settings()
