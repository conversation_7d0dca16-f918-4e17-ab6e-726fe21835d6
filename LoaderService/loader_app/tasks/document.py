# loader_app/tasks/document.py
#
# Runs inside the **Loader** worker.
# 1. Receives a newly–uploaded file
# 2. Reads / splits / stores chunks in Redis
# 3. Marks the document READY_TO_BE_INDEXED
# 4. Pushes an *embedding_request* message to the Indexer queue
# ---------------------------------------------------------------------------
import logging
import os
from typing import Any, Optional

import dramatiq
from dramatiq.middleware.current_message import CurrentMessage
from loader_app.crud import document as crud
from loader_app.enums.document import DocumentStatusEnum, DocumentTypeEnum
from loader_app.enums.loader import LoadMethodEnum
from loader_app.services.loader import Loader
from loader_app.settings import settings
from loader_app.tasks import QUEUE_PREFIX, core, rabbitmq_broker
from loader_app.tasks.core import state_mapping
from loader_app.utils.helpers import dramatiq_task_logger

logger = logging.getLogger(__name__)
loader = Loader()


# ────────────────────────────────────────────────────────────────────────────
# ❸ PUBLIC ENTRY – all Loader events come through this actor
# ────────────────────────────────────────────────────────────────────────────
@dramatiq.actor(
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.loader.document_move_queue",
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
)
def loader_move_document_forward(document_id: str, event: str, **kwargs) -> None:
    """Loader entry point for moving documents forward.
    Generic “router” actor.
    Decides which next-actor should run according to the state machine
    above (`state_mapping`).
    """
    msg = CurrentMessage.get_current_message()
    dramatiq_task_logger("loader_move_document_forward", msg)
    core.move_item_forward(
        item_id=document_id,
        event=event,
        state_mapping_func=state_mapping,
        state_mapping_args={"type": DocumentTypeEnum.DOCUMENT.value},
        get_item_func=crud.get_document,
        get_item_args={"document_id": document_id},
        **kwargs,
    )


# ────────────────────────────────────────────────────────────────────────────
# ❷ ACTORS – executed by the *Loader* worker
# ────────────────────────────────────────────────────────────────────────────
@dramatiq.actor(
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.loader.document_load_queue",
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
)
def load_document(item_id: str, load_method: LoadMethodEnum, headers: Optional[dict] = None) -> None:
    """Load a document.
    Read the file, split into chunks, push chunks to Redis, then emit
    “loading_finished”.
    """
    msg = CurrentMessage.get_current_message()
    dramatiq_task_logger("load_document", msg)

    core.load_item(
        item_id=item_id,
        get_item_func=crud.get_document,
        get_item_args={"document_id": item_id},
        update_item_func=crud.update_document,
        update_item_args={
            "document_id": item_id,
            "status": DocumentStatusEnum.LOADING.value,
        },
        state_mapping_func=state_mapping,
        state_mapping_args={"type": DocumentTypeEnum.DOCUMENT.value},
        get_documents_from_item=_get_documents_from_item,
        load_method=load_method,
        **{"headers": headers},
    )


@dramatiq.actor(
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.loader.document_fail_queue",
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
)
def mark_document_as_failed(item_id: str, headers: Optional[dict] = None) -> None:
    """Set status = FAILED."""
    msg = CurrentMessage.get_current_message()
    dramatiq_task_logger("mark_document_as_failed", msg)

    core.mark_as_failed(
        item_id=item_id,
        update_item_func=crud.update_document,
        update_item_args={
            "document_id": item_id,
            "status": DocumentStatusEnum.FAILED.value,
        },
    )


@dramatiq.actor(
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.loader.document_ready_queue",
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
)
def mark_document_as_ready_to_be_indexed(item_id: str, headers: Optional[dict] = None) -> None:
    """Last Loader step.

    1. Update DB status → READY_TO_BE_INDEXED
    2. Publish *embedding_request* to the Indexer queue
    """
    msg = CurrentMessage.get_current_message()
    dramatiq_task_logger("mark_document_as_ready_to_be_indexed", msg)
    core.mark_as_ready_to_be_indexed(
        item_id=item_id,
        update_item_func=crud.update_document,
        update_item_args={
            "document_id": item_id,
            "status": DocumentStatusEnum.READY_TO_BE_INDEXED.value,
        },
        state_mapping_func=state_mapping,
        state_mapping_args={"type": DocumentTypeEnum.DOCUMENT.value},
        get_item_func=crud.get_document,
        get_item_args={"document_id": item_id},
        **{"headers": headers},
    )


# ────────────────────────────────────────────────────────────────────────────
# ❹ HELPERS
# ────────────────────────────────────────────────────────────────────────────
def _get_documents_from_item(item, load_method: LoadMethodEnum) -> Any:
    """Invokes the Loader class to read the physical file from disk."""
    logger.info("Reading file %s", item.file_path)
    logger.info("*****loader worker File size: %d bytes", os.path.getsize(item.file_path))

    documents = loader.load_documents(item.file_path, load_method)

    # Delete the temporary file after loading — unless status is FAILED -- to give opportunity to retry
    if item.status != DocumentStatusEnum.FAILED.value and os.path.exists(item.file_path):
        try:
            os.remove(item.file_path)
            logger.info(f"Temporary file deleted: {item.file_path}")
        except Exception as e:
            logger.warning(f"Could not delete temp file: {e}")
    return documents
