import logging
from typing import Optional

import dramatiq
from loader_app.crud.url import get_url, update_url
from loader_app.enums.document import DocumentStatusEnum, DocumentTypeEnum
from loader_app.services.chunker import Chunker
from loader_app.settings import settings
from loader_app.tasks import QUEUE_PREFIX, core, rabbitmq_broker
from loader_app.tasks.core import state_mapping

logger = logging.getLogger(__name__)

chunker = Chunker()


@dramatiq.actor(
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.loader.url_move_queue",
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
)
def move_url_forward(url_id: str, event: str, **kwargs) -> None:
    """Move a URL item forward in the processing queue."""
    core.move_item_forward(
        item_id=url_id,
        event=event,
        state_mapping_func=state_mapping,
        state_mapping_args={"type": DocumentTypeEnum.URL.value},
        get_item_func=get_url,
        get_item_args={"item_id": url_id},
        **kwargs,
    )


@dramatiq.actor(
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.loader.url_load_queue",
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
)
def load_url(item_id: str, headers: Optional[dict] = None, **kwargs) -> None:
    """Load a URL item and process it to create documents."""
    logger.info(f"🔄 Loading URL item {item_id}")
    core.load_item(
        item_id=item_id,
        get_item_func=get_url,
        get_item_args={"item_id": item_id},
        update_item_func=update_url,
        update_item_args={
            "item_id": item_id,
            "status": DocumentStatusEnum.LOADING.value,
        },
        state_mapping_func=state_mapping,
        state_mapping_args={"type": DocumentTypeEnum.URL.value},
        get_documents_from_item=get_documents_from_item,
        **{"headers": headers},
    )


@dramatiq.actor(
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.loader.url_added_queue",
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
)
def mark_url_as_ready_to_be_indexed(item_id: str, headers: Optional[dict] = None, **kwargs) -> None:
    """Mark a URL item as ready to be indexed after loading."""
    core.mark_as_ready_to_be_indexed(
        item_id=item_id,
        update_item_func=update_url,
        update_item_args={
            "item_id": item_id,
            "status": DocumentStatusEnum.READY_TO_BE_INDEXED.value,
        },
        state_mapping_func=state_mapping,
        state_mapping_args={"type": DocumentTypeEnum.URL.value},
        get_item_func=get_url,
        get_item_args={"item_id": item_id},
        **{"headers": headers},
    )


@dramatiq.actor(
    broker=rabbitmq_broker,
    queue_name=f"{QUEUE_PREFIX}.loader.url_load_failed_queue",
    max_retries=settings.dramatiq_task_max_retries,
    time_limit=settings.dramatiq_task_time_limit_ms,
    max_age=settings.dramatiq_task_max_age_ms,
)
def mark_url_as_failed(item_id: str, headers: Optional[dict] = None) -> None:
    """Mark a URL item as failed if loading fails."""
    core.mark_as_failed(
        item_id=item_id,
        update_item_func=update_url,
        update_item_args={
            "item_id": item_id,
            "status": DocumentStatusEnum.FAILED.value,
        },
    )


def get_documents_from_item(item, **kwargs):
    """Extract documents from a URL item."""
    try:
        logger.info(f"📄 Creating documents from URL item {item.id} - URL: {item.url}")
        
        if not item.content:
            logger.warning(f"⚠️ No content found for URL item {item.id}")
            return []

        documents = chunker.create_documents([item.content])
        logger.info(f"✅ Created {len(documents)} documents from URL item {item.id}")
        
        for doc in documents:
            doc.metadata["source"] = item.url
            doc.metadata["type"] = "web"
        # -------------------------------------------------------------------
        return documents
    except Exception as e:
        logger.exception("❌ Failed to create documents from URL item: %s", e)
        raise ValueError("Failed to create documents from URL item") from e
