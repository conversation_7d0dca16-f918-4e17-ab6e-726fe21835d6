import logging

import dramatiq
import pika
import redis
from dramatiq.brokers.rabbitmq import RabbitmqBroker
from dramatiq.middleware import (
    AgeLimit,
    CurrentMessage,
    Prometheus,
    Retries,
    ShutdownNotifications,
    TimeLimit,
)
from loader_app.settings import settings
from pika.exceptions import AMQPConnectionError, StreamLostError
from redis.exceptions import ConnectionError as RedisConnectionError

logger = logging.getLogger(__name__)


# --------------------------------------------------------------------------- #
# Singleton broker holder
# --------------------------------------------------------------------------- #
class MessageBroker:
    """Singleton class to hold the RabbitMQ broker and Redis client."""

    _instance = None

    def __new__(cls):
        """Ensure only one instance of Message<PERSON>roker exists."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._init()
        return cls._instance

    # --------------- Construction helpers ---------------------------------- #
    def _init(self) -> None:
        """Initialize the message broker and Redis client."""
        self.broker = self._setup_rabbitmq_broker()
        self.redis_client = self._setup_redis()

    # --------------- RabbitMQ ---------------------------------------------- #
    def _setup_rabbitmq_broker(self) -> RabbitmqBroker:
        """Set up the RabbitMQ broker with the specified parameters."""
        creds = pika.PlainCredentials(settings.rabbitmq_user, settings.rabbitmq_password)
        params = {
            "host": settings.rabbitmq_host,
            "port": settings.rabbitmq_port,
            "credentials": creds,
            "virtual_host": settings.rabbitmq_vhost,
            "heartbeat": 120,
            "blocked_connection_timeout": 300,
            "connection_attempts": 10,
            "retry_delay": 5,
            "socket_timeout": 10,
        }

        middleware = [
            Prometheus(),
            AgeLimit(max_age=int(settings.dramatiq_task_max_age_ms)),
            TimeLimit(time_limit=int(settings.dramatiq_task_time_limit_ms)),
            Retries(
                max_retries=int(settings.dramatiq_task_max_retries),
                retry_when=lambda m, exc: isinstance(exc, (ConnectionResetError, StreamLostError)),
            ),
            CurrentMessage(),
            ShutdownNotifications(notify_shutdown=False),
        ]
        try:
            broker = RabbitmqBroker(confirm_delivery=False, middleware=middleware, **params)
            dramatiq.set_broker(broker)
            return broker
        except AMQPConnectionError as e:
            logger.error("rabbitmq-connection-error", exc_info=e)
            raise

    # --------------- Redis ------------------------------------------------- #
    def _setup_redis(self) -> redis.Redis:
        """Set up the Redis client with the specified parameters."""
        pool = redis.ConnectionPool(
            host=settings.redis_host,
            port=settings.redis_port,
            db=settings.embeddings_redis_db,
            max_connections=10,
            socket_timeout=5,
            retry_on_timeout=True,
            health_check_interval=30,
        )
        try:
            client = redis.Redis(connection_pool=pool)
            client.ping()
            return client
        except RedisConnectionError as e:
            logger.error("redis-connection-error", exc_info=e)
            raise

    # --------------- Helpers ---------------------------------------------- #
    def check_broker_health(self) -> bool:
        """Check the health of the RabbitMQ broker by declaring a test queue."""
        try:
            self.broker.declare_queue("health_check")
            return True
        except Exception as e:  # noqa: BLE001
            logger.error("broker-health", exc_info=e)
            return False

    def declare_all_queues(self):
        """Declare all known queues + their .DQ dead-letter queues."""
        declared = []
        for queue_name in self.broker.queues:
            self.broker.declare_queue(queue_name)
            declared.append(queue_name)
            # declare the dead queue (DLQ) explicitly
            dlq_name = f"{queue_name}.DQ"
            self.broker.declare_queue(dlq_name)
            declared.append(dlq_name)


# --------------------------------------------------------------------------- #
# module-level singletons
# --------------------------------------------------------------------------- #
message_broker = MessageBroker()
rabbitmq_broker = message_broker.broker
redis_conn = message_broker.redis_client
QUEUE_PREFIX = settings.dramatiq_queue_prefix

# --------------------------------------------------------------------------- #
# Import actors
# --------------------------------------------------------------------------- #
from . import document, url  # noqa: E402,F401

# --------------------------------------------------------------------------- #
# declare all queues
# --------------------------------------------------------------------------- #
message_broker.declare_all_queues()
