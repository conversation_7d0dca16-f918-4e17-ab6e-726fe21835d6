# loader_app/tasks/services.py
#
# Helper functions that are used by actors in *loader_app.tasks.document*
# ────────────────────────────────────────────────────────────────────────────
import logging
import pickle
from typing import Optional, Tuple, Union

from dramatiq import Message
from loader_app.database.models import URL, Document
from loader_app.database.session import db_context
from loader_app.enums.document import (
    DocumentEventsEnum,
    DocumentStatusEnum,
    DocumentTypeEnum,
)
from loader_app.enums.loader import ChunkingStrategy, LoadMethodEnum
from loader_app.services.chunker import Chunker
from loader_app.tasks import QUEUE_PREFIX, rabbitmq_broker, redis_conn
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


# ────────────────────────────────────────────────────────────────────────────
# ❶ STATE MACHINE – returns real actor objects (not raw strings)
# ────────────────────────────────────────────────────────────────────────────
def state_mapping(doc_type: Union[str, DocumentTypeEnum] = DocumentTypeEnum.DOCUMENT.value) -> dict[str, dict[str, str]]:
    """State mapping for a given document type.
    Mapping:  current_status  →  { event → <actor_name> }

    Only statuses that the Indexer can encounter are included.
    """
    doc_type = doc_type.value if isinstance(doc_type, DocumentTypeEnum) else doc_type
    logger.info(f"State mapping for {doc_type}")

    mapping = {
        DocumentStatusEnum.ADDED.value: {
            DocumentEventsEnum.LOAD_REQUEST.value: f"load_{doc_type}",
        },
        DocumentStatusEnum.LOADING.value: {
            DocumentEventsEnum.LOAD_FINISHED.value: f"mark_{doc_type}_as_ready_to_be_indexed",
            DocumentEventsEnum.LOAD_FAILED.value: f"mark_{doc_type}_as_failed",
        },
        DocumentStatusEnum.FAILED.value: {
            DocumentEventsEnum.LOAD_REQUEST.value: f"load_{doc_type}",
        },
    }

    return mapping


def actor_registry(actor_name):
    """Return the actual actor function based on its name."""
    from loader_app.tasks import document, url

    _mapping = {
        # Document actors
        "load_document": document.load_document,
        "mark_document_as_ready_to_be_indexed": document.mark_document_as_ready_to_be_indexed,
        "mark_document_as_failed": document.mark_document_as_failed,
        "loader_move_document_forward": document.loader_move_document_forward,
        # URL actors
        "load_url": url.load_url,
        "mark_url_as_ready_to_be_indexed": url.mark_url_as_ready_to_be_indexed,
        "mark_url_as_failed": url.mark_url_as_failed,
        "loader_move_url_forward": url.move_url_forward,
    }

    if actor_name not in _mapping:
        raise ValueError(f"Actor {actor_name} not found in registry.")
    return _mapping[actor_name]


# ────────────────────────────────────────────────────────────────────────────
# ❶  Generic helpers
# ────────────────────────────────────────────────────────────────────────────
def is_task_canceled(document_id: str) -> bool:
    """Return True if another worker asked us to cancel processing `document_id`."""
    cancelled = redis_conn.get(f"cancel_task_{document_id}") == b"true"
    logger.info("Cancellation check %s → %s", document_id, cancelled)
    return cancelled


def cancel_document_task(document_id: str) -> None:
    """Mark the current task as cancelled so that long-running actors can stop early."""
    logger.info("Cancelling document task %s", document_id)
    redis_conn.set(f"cancel_task_{document_id}", "true")


# ────────────────────────────────────────────────────────────────────────────
# ❷  Finite-state routing helper
# ────────────────────────────────────────────────────────────────────────────
def move_item_forward(
    item_id: str,
    event: str,
    state_mapping_func,
    state_mapping_args,
    get_item_func,
    get_item_args,
    **kwargs,
) -> None:
    """Central router: decide which next actor should run based on *(current_status, event)* pair."""

    logger.info(f"Moving item {item_id} forward: {event}")
    try:
        with db_context() as db_session:
            item = get_item_func(db_session, **get_item_args)

        if not item:
            return

        status = item.status

        mapping = state_mapping_args.get("type", DocumentTypeEnum.DOCUMENT.value)
        next_actor = actor_registry(state_mapping_func(mapping)[status][event])
        logger.info(f"Transition: status={status}, event={event}")
        logger.info(f"Resolved next actor: {next_actor}")

        if next_actor:
            try:
                next_actor.send(item_id=item_id, **kwargs)
                logger.info(f"Actor {next_actor} triggered for item {item_id}")
            except Exception as e:
                logger.exception("Failed to send actor message", exc_info=e)

    except KeyError:
        logger.exception("Invalid transition for %s : %s + %s", item_id, status, event)


# ────────────────────────────────────────────────────────────────────────────
# ❸  Loader-side “load & anonymise” logic
# ────────────────────────────────────────────────────────────────────────────


def get_chunk_details_from_custom_config(item_id: str, db: Session) -> Tuple[Optional[int], Optional[int], Optional[str]]:
    """Retrieve chunk_size and chunk_overlap from Collection.custom_config based on the given item_id (document_id or url_id).

    Lookup order:
    1. Try item_id as a Document ID
    2. If not found, try as a URL ID
    3. Then lookup the related Collection and return chunk config if available.

    Args:
        item_id (str): str of a Document or URL
        db (Session): SQLAlchemy DB session

    Returns:
        (chunk_size, chunk_overlap): Either (int, int) or (None, None)
    """
    collection = None

    # Try as Document
    document = db.query(Document).filter(Document.id == item_id).first()
    if document and document.collection:
        collection = document.collection
    else:
        # Try as URL
        url = db.query(URL).filter(URL.id == item_id).first()
        if url and url.collection:
            collection = url.collection

    if not collection:
        return None, None, None

    config = collection.custom_config or {}
    chunk_size = config.get("chunk_size")
    chunk_overlap = config.get("chunk_overlap")
    chunking_strategy = config.get("chunking_strategy", ChunkingStrategy.RECURSIVE.value)

    return chunk_size, chunk_overlap, chunking_strategy


def load_item(
    item_id: str,
    get_item_func,
    get_item_args,
    update_item_func,
    update_item_args,
    state_mapping_func,
    state_mapping_args,
    get_documents_from_item,
    load_method: LoadMethodEnum = LoadMethodEnum.USE_LANGCHAIN,
    **kwargs,
) -> None:
    """Load and split a document or URL into chunks.
    1. Reads the physical PDF (via `get_documents_from_item`)
    2. Splits into chunks → Redis
    3. Emits *loading_finished* (or *_failed*)
    """

    if is_task_canceled(item_id):
        logger.info("Task %s was cancelled – aborting.", item_id)
        return

    # Update DB status to “LOADING” and get chunk size
    with db_context() as db_session:
        chunk_size, chunk_overlap, chunk_strategy = get_chunk_details_from_custom_config(item_id, db_session)
        update_item_func(db_session, **update_item_args)
        item = get_item_func(db_session, **get_item_args)

    if not item:
        return

    # -- Load & split
    chunker = Chunker(chunk_size=chunk_size, chunk_overlap=chunk_overlap, strategy=chunk_strategy)
    logger.info(f"📦 Chunker config - size: {chunk_size}, overlap: {chunk_overlap}, strategy: {chunk_strategy}")

    try:
        logger.info(f"📄 Getting documents from item {item_id}")
        docs = get_documents_from_item(item, load_method=load_method)
        logger.info(f"📄 Got {len(docs)} documents from item {item_id}")
        
        logger.info(f"✂️ Splitting documents into chunks for item {item_id}")
        splits = chunker.split_documents(docs)
        logger.info(f"✂️ Created {len(splits)} chunks for item {item_id}")

        logger.info(f"💾 Storing {len(splits)} chunks in Redis for item {item_id}")
        for idx, split in enumerate(splits):
            redis_conn.set(f"{item_id}_split_{idx}", pickle.dumps(split))

        redis_conn.set(f"{item_id}_splits", pickle.dumps(splits))
        logger.info("✅ Stored %d chunks for %s", len(splits), item_id)

    except Exception:
        logger.exception("Failed during load for %s", item_id)
        move_item_forward(
            item_id,
            event=DocumentEventsEnum.LOAD_FAILED.value,
            state_mapping_func=state_mapping_func,
            state_mapping_args=state_mapping_args,
            get_item_func=get_item_func,
            get_item_args=get_item_args,
            **kwargs,
        )
        return

    # Success – trigger next step
    move_item_forward(
        item_id,
        event=DocumentEventsEnum.LOAD_FINISHED.value,
        state_mapping_func=state_mapping_func,
        state_mapping_args=state_mapping_args,
        get_item_func=get_item_func,
        get_item_args=get_item_args,
        **kwargs,
    )


# ────────────────────────────────────────────────────────────────────────────
# ❹  Simple status helpers
# ────────────────────────────────────────────────────────────────────────────
def mark_as_failed(item_id: str, update_item_func, update_item_args):
    """Set DB status = FAILED."""
    with db_context() as db_session:
        update_item_func(db_session, **update_item_args)


def mark_as_ready_to_be_indexed(
    item_id: str,
    update_item_func,
    update_item_args,
    state_mapping_func,
    state_mapping_args,
    get_item_func,
    get_item_args,
    **kwargs,
) -> None:
    """Mark the item as ready to be indexed and enqueue an embedding request.
    1. Update DB status → READY_TO_BE_INDEXED
    2. Push an *embedding_request* message to the **Indexer** queue.
    """
    doc_type = state_mapping_args.get("type", DocumentTypeEnum.DOCUMENT.value)
    logger.info(f"🎯 Marking {doc_type} as ready to be indexed with ID {item_id}")

    with db_context() as db_session:
        logger.info(f"💾 Updating {doc_type} {item_id} status to READY_TO_BE_INDEXED")
        update_item_func(db_session, **update_item_args)
        logger.info(f"✅ Status updated for {doc_type} {item_id}")

    queue_name = f"{QUEUE_PREFIX}.indexer.{doc_type}_move_queue"
    actor_name = f"indexer_move_{doc_type}_forward"
    event = DocumentEventsEnum.EMBEDDING_REQUEST.value
    
    logger.info(f"📨 Sending message to Indexer - Queue: {queue_name}, Actor: {actor_name}, Event: {event}")
    
    rabbitmq_broker.enqueue(
        Message(
            queue_name=queue_name,
            actor_name=actor_name,
            args=(item_id, event),
            kwargs=kwargs,
            options={},
        )
    )

    logger.info(f"✅ Sent embedding_request to Indexer for {doc_type} {item_id}")
