import logging
from typing import List, Optional
from uuid import UUID

import psycopg
from loader_app.api.utils.exceptions import DatabaseInsertError
from loader_app.database import DBSession
from loader_app.database.models import Collection, Document
from loader_app.enums.document import DocumentStatusEnum
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger(__name__)


def insert_document(db_session: DBSession, collection_id: UUID, file_path: str, document_metadata: dict) -> UUID:
    """Insert new document into the database."""
    try:
        document = Document(
            collection_id=collection_id,
            file_path=file_path,
            status=DocumentStatusEnum.ADDED.value,
            document_metadata=document_metadata,
        )
        db_session.add(document)
        db_session.commit()
        db_session.refresh(document)
        return document.id

    except SQLAlchemyError as e:
        db_session.rollback()
        logger.exception("Failed to insert document")
        raise DatabaseInsertError("Database insert failed") from e


def get_document(
    db_session: DBSession,
    document_id: UUID,
    collection_id: Optional[UUID] = None,
) -> Optional[Document]:
    """Fetch a document by its ID and optionally by collection ID."""
    try:
        query = db_session.query(Document)
        if collection_id:
            return query.filter_by(id=document_id, collection_id=collection_id).first()
        return query.filter_by(id=document_id).first()
    except psycopg.errors.InvalidTextRepresentation:
        raise ValueError("Invalid document ID format")


def get_documents(
    db_session: DBSession,
    collection_id: UUID,
) -> List[Document]:
    """Fetch all documents in a specific collection."""
    try:
        return db_session.query(Document).filter_by(collection_id=collection_id).all()
    except psycopg.errors.InvalidTextRepresentation:
        raise ValueError("Invalid collection ID")


def get_document_ids_by_collection_name(
    db_session: DBSession,
    collection_name: str,
) -> List[str]:
    """Fetch document IDs by collection name."""
    try:
        collection = db_session.query(Collection).filter_by(name=collection_name).first()
        if not collection:
            raise ValueError(f"Collection '{collection_name}' not found")

        documents = db_session.query(Document).filter_by(collection_id=collection.id).all()
        return [str(doc.id) for doc in documents]

    except psycopg.errors.InvalidTextRepresentation:
        raise ValueError("Invalid collection name format")
    except Exception as e:
        logger.exception("Unexpected DB error in get_document_ids_by_collection_name")
        raise


def update_document(
    db_session: DBSession,
    document_id: UUID,
    collection_id: Optional[UUID] = None,
    file_path: Optional[str] = None,
    status: Optional[str] = None,
    document_metadata: Optional[dict] = None,
) -> None:
    """Update an existing document's details."""
    try:
        q = db_session.query(Document).filter_by(id=document_id)
        if collection_id:
            q = q.filter_by(collection_id=collection_id)
        document = q.first()
        if not document:
            raise ValueError("Document not found")

        if file_path is not None:
            document.file_path = file_path
        if status is not None:
            document.status = status
        if document_metadata is not None:
            document.document_metadata = document_metadata

        db_session.commit()

    except Exception:
        db_session.rollback()
        raise


def delete_document(
    db_session: DBSession,
    document_id: UUID,
    collection_id: Optional[UUID] = None,
) -> None:
    """Delete a document by its ID and optionally by collection ID."""
    try:
        q = db_session.query(Document).filter_by(id=document_id)
        if collection_id:
            q = q.filter_by(collection_id=collection_id)
        document = q.first()
        if not document:
            raise ValueError("Document not found")

        db_session.delete(document)
        db_session.commit()

    except Exception:
        db_session.rollback()
        raise
