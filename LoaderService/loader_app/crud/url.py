import logging
from datetime import datetime
from typing import List, Optional
from uuid import UUID

import psycopg
from loader_app.crud.collection import get_collection
from loader_app.database import DBSession
from loader_app.database.models import URL, Collection
from loader_app.enums.document import DocumentStatusEnum

logger = logging.getLogger(__name__)


def delete_urls_by_collection_id(db_session: DBSession, collection_id: UUID) -> None:
    """Delete all URLs associated with a specific collection_id."""
    try:
        db_session.query(URL).filter_by(collection_id=collection_id).delete(synchronize_session=False)
        db_session.commit()
    except Exception as e:
        db_session.rollback()
        logger.error(f"Failed to delete URLs for collection_id {collection_id}: {str(e)}")
        raise


def insert_or_update_url(
    db_session: DBSession,
    collection_id: UUID,
    url: str,
    content: str,
) -> UUID:
    """Insert or update a URL in the database."""
    try:
        url_obj = db_session.query(URL).filter_by(url=url, collection_id=collection_id).first()

        if url_obj:
            # If URL already exists and is not deleted, treat as duplicate
            if url_obj.status != DocumentStatusEnum.DELETED:
                raise ValueError("url_already_exists")

            # If previously deleted, allow re-upload by updating the content & status
            url_obj.content = content
            url_obj.status = DocumentStatusEnum.ADDED
            url_obj.last_updated = datetime.now()
            db_session.commit()
            return url_obj.id

        new_url = URL(
            collection_id=collection_id,
            url=url,
            content=content,
            status=DocumentStatusEnum.ADDED,
        )
        db_session.add(new_url)
        db_session.commit()
        db_session.refresh(new_url)
        return new_url.id
    except Exception:
        db_session.rollback()
        raise


def get_urls(db_session: DBSession, collection_id: UUID) -> List[URL]:
    """Get URLs by collection_id."""
    try:
        urls = db_session.query(URL).filter_by(collection_id=collection_id).all()
    except psycopg.errors.InvalidTextRepresentation:
        raise Exception("invalid query request")
    if not urls:
        return []
    return urls


def get_url(
    db_session: DBSession,
    item_id: UUID,
    collection_id: Optional[UUID] = None,
    collection_name: Optional[str] = None,
) -> Optional[URL]:
    """Get URL by item_id and optional collection_id or collection_name."""
    try:
        query = db_session.query(URL)
        if collection_id:
            query = query.filter(URL.id == item_id, URL.collection_id == collection_id)
        elif collection_name:
            query = query.join(URL.collection).filter(URL.id == item_id, Collection.name == collection_name)
        else:
            query = query.filter(URL.id == item_id)
        return query.first()
    except psycopg.errors.InvalidTextRepresentation:
        raise ValueError("Invalid UUID format")


def update_url(
    db_session: DBSession,
    item_id: UUID,
    collection_id: Optional[UUID] = None,
    status: Optional[str] = None,
    content: Optional[str] = None,
) -> None:
    """
    Update URL status/content. Rollbacks on any error.

    Args:
        db_session: SQLAlchemy session.
        item_id: UUID of the URL.
        collection_id: optional filter for scope.
        status: new status (optional).
        content: new content (optional).
    """
    try:
        query = db_session.query(URL).filter(URL.id == item_id)
        if collection_id:
            query = query.filter(URL.collection_id == collection_id)

        url = query.first()
        if not url:
            raise ValueError("URL not found")

        if status is not None:
            url.status = status
        if content is not None:
            url.content = content

        db_session.commit()

    except Exception as e:
        db_session.rollback()
        raise e


def delete_url(db_session: DBSession, url: URL) -> None:
    """Delete a URL record."""
    try:
        db_session.delete(url)
        db_session.commit()
    except Exception as e:
        db_session.rollback()
        raise RuntimeError(f"Failed to delete URL: {str(e)}")


def get_collection_by_url_id(
    db_session: DBSession,
    item_id: str,
) -> Optional[Collection]:
    """Get collection by id.

    Args:
        db_session: SQLAlchemy database session.
        item_id: item identifier.
    """
    try:
        url = db_session.query(URL).filter(URL.id == item_id).first()
    except psycopg.errors.InvalidTextRepresentation:
        raise Exception("invalid collection request")
    if url:
        collection = url.collection
        if collection:
            return collection
    return None  # "Collection not found"


def get_urls_ids(
    db_session: DBSession,
    collection_id: Optional[UUID] = None,
    collection_name: Optional[str] = None,
) -> List[UUID]:
    """Get all URLs IDs by collection_id or collection_name."""
    if collection_name:
        collection = get_collection(db_session, collection_name=collection_name)
        if not collection:
            raise LookupError(f"Collection with name {collection_name} not found")
        collection_id = collection.id

    try:
        urls = db_session.query(URL).filter_by(collection_id=collection_id).all()
        return [url.id for url in urls]
    except psycopg.errors.InvalidTextRepresentation:
        raise ValueError("Invalid UUID format")
