"""CRUD operations for Atlassian content management."""

import logging
from typing import Dict, List, Optional
from uuid import UUID

from loader_app.api.core.atlassian import (
    authenticate_atlassian,
    close_atlassian_session,
    get_atlassian_content_for_indexing,
    get_confluence_pages,
    get_confluence_spaces,
    get_jira_issues,
    get_jira_projects,
)
from loader_app.crud.collection import get_collection
from loader_app.database import DBSession

logger = logging.getLogger(__name__)


async def authenticate_with_atlassian(base_url: str, username: str, api_token: str) -> bool:
    """
    Authenticate with Atlassian instance.

    Args:
        base_url: Atlassian instance base URL
        username: User email address
        api_token: API token from Atlassian

    Returns:
        bool: True if authentication successful, False otherwise
    """
    try:
        return await authenticate_atlassian(base_url, username, api_token)
    except Exception as e:
        logger.exception(f"Error authenticating with Atlassian: {e}")
        return False


async def fetch_jira_projects() -> List[Dict]:
    """
    Fetch all accessible Jira projects.

    Returns:
        List of project dictionaries
    """
    try:
        return await get_jira_projects()
    except Exception as e:
        logger.exception(f"Error fetching Jira projects: {e}")
        return []


async def fetch_jira_issues(project_key: str, max_results: int = 10000, issue_keys: Optional[List[str]] = None) -> List[Dict]:
    """
    Fetch issues from a specific Jira project.

    Args:
        project_key: Project key (e.g., 'PROJ')
        max_results: Maximum number of issues to return, defaults to 10000 to fetch all
        issue_keys: Specific issue keys to fetch (optional)

    Returns:
        List of issue dictionaries
    """
    try:
        return await get_jira_issues(project_key, max_results, issue_keys)
    except Exception as e:
        logger.exception(f"Error fetching Jira issues for project {project_key}: {e}")
        return []


async def fetch_confluence_spaces() -> List[Dict]:
    """
    Fetch all accessible Confluence spaces.

    Returns:
        List of space dictionaries
    """
    try:
        return await get_confluence_spaces()
    except Exception as e:
        logger.exception(f"Error fetching Confluence spaces: {e}")
        return []


async def fetch_confluence_pages(space_key: str, max_results: int = 100, page_ids: Optional[List[str]] = None) -> List[Dict]:
    """
    Fetch pages from a specific Confluence space.

    Args:
        space_key: Space key
        max_results: Maximum number of pages to return
        page_ids: Specific page IDs to fetch (optional)

    Returns:
        List of page dictionaries
    """
    try:
        return await get_confluence_pages(space_key, max_results, page_ids)
    except Exception as e:
        logger.exception(f"Error fetching Confluence pages for space {space_key}: {e}")
        return []


async def prepare_atlassian_content_for_indexing(
    db_session: DBSession, collection_id: UUID, content_type: str, project_key: Optional[str] = None, space_key: Optional[str] = None, item_ids: Optional[List[str]] = None
) -> List[Dict]:
    """
    Prepare Atlassian content for indexing.

    Args:
        db_session: Database session
        collection_id: Collection ID to index content into
        content_type: Type of content ('jira_issues' or 'confluence_pages')
        project_key: Jira project key (required for jira_issues)
        space_key: Confluence space key (required for confluence_pages)
        item_ids: Specific item IDs/keys to fetch

    Returns:
        List of content dictionaries ready for indexing
    """
    try:
        # Verify collection exists
        collection = get_collection(db_session, collection_id)
        if not collection:
            raise ValueError(f"Collection {collection_id} not found")

        # Get content from Atlassian
        content_items = await get_atlassian_content_for_indexing(content_type=content_type, project_key=project_key, space_key=space_key, item_ids=item_ids)

        # Add collection information to each content item
        for item in content_items:
            item["collection_id"] = str(collection_id)
            item["collection_name"] = collection.name

        logger.info(f"Prepared {len(content_items)} {content_type} items for collection {collection.name}")
        return content_items

    except Exception as e:
        logger.exception(f"Error preparing Atlassian content for indexing: {e}")
        return []


async def cleanup_atlassian_session():
    """Clean up Atlassian service session."""
    try:
        await close_atlassian_session()
    except Exception as e:
        logger.exception(f"Error cleaning up Atlassian session: {e}")


def validate_collection_access(db_session: DBSession, collection_id: UUID) -> bool:
    """
    Validate that collection exists and is accessible.

    Args:
        db_session: Database session
        collection_id: Collection ID to validate

    Returns:
        bool: True if collection is valid and accessible
    """
    try:
        collection = get_collection(db_session, collection_id)
        return collection is not None
    except Exception as e:
        logger.exception(f"Error validating collection access: {e}")
        return False
