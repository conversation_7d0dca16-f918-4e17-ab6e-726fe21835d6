import logging
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

import psycopg
from loader_app.api.models.request import CollectionRequest
from loader_app.database import DBSession
from loader_app.database.models import Collection, Document
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy.orm.attributes import flag_modified

logger = logging.getLogger(__name__)


def get_collections(db_session: DBSession) -> List[Collection]:
    """Retrieve all collections from the database."""
    try:
        return db_session.query(Collection).all()
    except psycopg.errors.InvalidTextRepresentation:
        logger.error("Invalid collection request")
        raise Exception("invalid collection request")
    except Exception as e:
        logger.error("Error retrieving collections", exc_info=e)
        raise e


def insert_collection(db_session: DBSession, collection_request: CollectionRequest) -> Tuple[Optional[Collection], Optional[str]]:
    """
    Insert a new collection into the database.

    Args:
        db_session (Session): SQLAlchemy database session.
        collection_request (CollectionRequest): Request payload containing collection details.

    Returns:
        Tuple[Optional[Collection], Optional[str]]: Created Collection object, or error message.
    """
    collection = Collection(
        name=collection_request.name,
        description=collection_request.description,
        custom_config=collection_request.custom_config,
    )
    try:
        db_session.add(collection)
        db_session.commit()
        db_session.refresh(collection)
        return collection, None
    except IntegrityError as e:
        db_session.rollback()
        if "duplicate key" in str(e):
            return None, "collection name must be unique"
        return None, f"collection creation failed: {e}"
    except Exception as e:
        db_session.rollback()
        return None, f"unexpected error: {str(e)}"


def get_collection(db_session: DBSession, collection_id: Optional[UUID] = None, collection_name: Optional[str] = None) -> Optional[Collection]:
    """Get collection by id or name."""

    collection = None

    if collection_name:
        try:
            collection = db_session.query(Collection).filter_by(name=collection_name).first()
        except psycopg.errors.InvalidTextRepresentation:
            logger.error("Invalid collection request")
            raise Exception("Invalid collection request")

    if not collection and collection_id:
        try:
            collection = db_session.query(Collection).filter_by(id=collection_id).first()
        except psycopg.errors.InvalidTextRepresentation:
            logger.error("Invalid collection request")
            raise Exception("Invalid collection request")

    if not collection:
        logger.error("No collection found")
        return None

    return collection


def update_collection(db_session: DBSession, collection_id: UUID, **update_data: Dict[str, Any]) -> str:
    """
    Update collection fields based on the provided update data.

    Args:
        db_session (Session): SQLAlchemy database session.
        collection_id (str): The ID of the collection to update.
        update_data (dict): Fields and values to update.

    Returns:
        str: The ID of the updated collection.

    Raises:
        ValueError: If the collection is not found.
        RuntimeError: If a database error occurs.
    """
    try:
        # Retrieve the existing collection by ID
        collection = db_session.query(Collection).filter_by(id=collection_id).first()
        if not collection:
            logger.error(f"Collection with id {collection_id} not found")
            raise ValueError("Collection not found")

        # Update the collection fields based on the dynamic update_data
        logger.info(f"Received update data: {update_data}")

        # Update fields safely
        for field, value in update_data.items():
            if value is None:
                continue  # Skip null values

            if field == "name":
                collection.name = value
            elif field in (
                "llm_model",
                "system_prompt",
                "chunk_size",
                "chunk_overlap",
                "is_memory_on",
                "query_history_ttl",
                "reranking_on",
                "reranker_small_llm_model",
                "is_web_search_on",
                "web_search_llm_model",
                "is_slash_commands_on",
                "system_prompt_length_limit",
                "chunking_strategy",
                "role",
                "collection_search_method",
            ):
                if not isinstance(collection.custom_config, dict):
                    collection.custom_config = {}
                collection.custom_config[field] = value
            else:
                if not isinstance(collection.custom_config, dict):
                    collection.custom_config = {}
                collection.custom_config[field] = value

        # Mark custom_config as modified
        flag_modified(collection, "custom_config")

        # Commit the changes to the database
        db_session.commit()
        db_session.refresh(collection)

        return str(collection.id)

    except SQLAlchemyError as e:
        db_session.rollback()
        logger.exception("Database error while updating collection")
        raise RuntimeError("Database error occurred") from e

    except Exception as e:
        db_session.rollback()
        logger.exception("Unexpected error while updating collection", exc_info=e)
        raise RuntimeError("Unexpected error occurred") from e


def delete_collection(db_session: DBSession, collection_id: UUID) -> tuple[bool, str]:
    """
    Delete a collection by its ID.

    Args:
        db_session (Session): SQLAlchemy DB session.
        collection_id (str): UUID of the collection.

    Returns:
        tuple: (success: bool, message: str)
    """
    try:
        collection_record = db_session.query(Collection).filter_by(id=collection_id).first()
        if not collection_record:
            return False, "Collection not found"

        db_session.delete(collection_record)
        db_session.commit()
        return True, "Collection deleted successfully"

    except SQLAlchemyError as e:
        logger.error("Error deleting collection", exc_info=e)
        db_session.rollback()
        logger.exception("Error deleting collection")
        return False, "Database error"
    except Exception as e:
        logger.error("Unexpected error deleting collection", exc_info=e)
        db_session.rollback()
        return False, "Unexpected error"


def get_collection_by_document_id(
    db_session: DBSession,
    document_id: UUID,
) -> Optional[Collection]:
    """
    Fetch the collection linked to a specific document.

    Args:
        db_session (Session): SQLAlchemy session.
        document_id (str): Document ID.

    Returns:
        Optional[Collection]: Related collection or None if not found.
    """
    try:
        document = db_session.query(Document).filter_by(id=document_id).first()
        if document and document.collection:
            return document.collection
        return None
    except psycopg.errors.InvalidTextRepresentation:
        logger.error("Invalid document ID format")
        raise Exception("Invalid document ID format")
    except Exception as e:
        logger.error(f"Error fetching collection by document ID: {str(e)}")
        raise
