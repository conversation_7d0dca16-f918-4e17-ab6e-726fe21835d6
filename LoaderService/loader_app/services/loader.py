import io
import json
import logging
import time
from concurrent.futures import ThreadPool<PERSON>xecutor
from typing import Any, List, Optional

import fitz  # PyMuPDF
import markdown2

# ─── Ensure NLTK resources are available ────────────────────────────────────
import nltk
import pandas as pd
from docx import Document as DocxDocument
from langchain_community.document_loaders import (
    Docx2txtLoader,
    PyPDFLoader,
    TextLoader,
    UnstructuredExcelLoader,
    UnstructuredImageLoader,
    UnstructuredMarkdownLoader,
    UnstructuredPowerPointLoader,
)
from langchain_core.documents import Document
from loader_app.enums.loader import LoadMethodEnum
from loader_app.services.doc_analyzer import DocumentChecker
from loader_app.settings import settings
from loader_app.utils.helpers import image_to_base64, local_image_to_base64
from openai.lib.azure import AzureOpenAI
from PIL import Image
from pptx import Presentation


def _ensure_nltk_resource(resource: str):
    """Download the given NLTK *resource* if it isn't already present."""

    try:
        nltk.data.find(resource)
    except LookupError:
        try:
            nltk.download(resource.split("/")[-1])  # download by corpus name
        except Exception as e:
            logger.warning(f"Failed to download NLTK resource '{resource}': {e}")


# The Unstructured library relies on several NLTK resources
_ensure_nltk_resource("tokenizers/punkt")
_ensure_nltk_resource("taggers/averaged_perceptron_tagger")
_ensure_nltk_resource("corpora/stopwords")

logger = logging.getLogger(__name__)


class Loader:
    """Loader class for handling various document types and loading methods."""

    def __init__(self) -> None:
        """Initialize the Loader object."""

        self.loader_mapping_langchain = {
            "pdf": PyPDFLoader,
            "docx": Docx2txtLoader,
            "md": UnstructuredMarkdownLoader,
            "xlsx": UnstructuredExcelLoader,
            "txt": TextLoader,
            "pptx": UnstructuredPowerPointLoader,
            "image": UnstructuredImageLoader,
            "jpg": UnstructuredImageLoader,
            "jpeg": UnstructuredImageLoader,
            "png": UnstructuredImageLoader,
        }
        self.loader_mapping = {
            "pdf": self.load_pdf,
            "docx": self.load_docx,
            "xlsx": self.load_excel,
            "md": self.load_markdown,
            "pptx": self.load_pptx,
            "jpg": self.analyze_image_with_gpt_vision,
            "jpeg": self.analyze_image_with_gpt_vision,
            "png": self.analyze_image_with_gpt_vision,
        }

        self.doc_checker = DocumentChecker(garbled_indicators=["\x01", "\x02", "ddddddddddddd", "\tdd", "�"])

    def load_documents(self, file_path: str, load_method: LoadMethodEnum) -> Any:
        """Load documents based on the specified file path and loading method."""
        _file_path = file_path.lower()
        file_extension = _file_path.split(".")[-1]

        if file_extension == "json":
            try:
                return self.load_json(file_path)
            except Exception as e:
                logger.error(f"Error loading JSON file {file_path}, trying other json uploader 2: {e}")

            try:
                return self.load_json2(file_path)
            except Exception as e:
                logger.error(f"Error loading JSON file {file_path} with json uploader 2: {e}")

        if load_method == LoadMethodEnum.USE_LANGCHAIN_AND_TESSERACT:
            return self._load_with_langchain_and_tesseract(file_path, file_extension)

        if load_method == LoadMethodEnum.USE_LANGCHAIN:
            return self._load_with_langchain(file_path, file_extension)

        if load_method == LoadMethodEnum.USE_TESSERACT:
            return self.loader_mapping[file_extension](file_path)

        if load_method == LoadMethodEnum.USE_GPT_VISION:
            # ── GPT-Vision branch --------------------------------------------------
            if file_extension == "pdf":
                return self.analyze_doc_with_gpt_vision(file_path)
            if file_extension in {"jpg", "jpeg", "png"}:
                return self.analyze_image_with_gpt_vision(file_path)

            raise ValueError(f"Unsupported file type '.{file_extension}' for GPT-Vision load method.")

        raise ValueError(f"Unsupported load method: {load_method.value}")

    def _load_with_langchain_and_tesseract(self, file_path, file_extension):
        """Load documents using Langchain and Tesseract OCR."""
        if file_extension in self.loader_mapping_langchain:
            if file_extension == "pdf":
                pdf_loader = self.loader_mapping_langchain["pdf"](file_path, extract_images=True)
                documents = pdf_loader.load()
                return self.doc_checker.check_documents(documents)
            else:
                return self.loader_mapping_langchain[file_extension](file_path).load()

    def _load_with_langchain(self, file_path, file_extension):
        """Load documents using Langchain without Tesseract OCR."""
        if file_extension in self.loader_mapping_langchain:
            loader = self.loader_mapping_langchain[file_extension]
            logger.info(f"Loading {file_extension} file using langchain - {loader=}")
            try:
                _loader = loader(file_path).load() if file_extension != "pdf" else loader(file_path, extract_images=True).load()
                return _loader
            except Exception as e:
                logger.warning(f"Error loading {file_extension} file using langchain: {e}")
                if file_extension == "pdf":
                    logger.info("Closing extract_images and setting to False")
                    try:
                        _loader = loader(file_path, extract_images=False).load()
                        return _loader
                    except Exception as e2:
                        logger.error(f"Failed to load PDF even without image extraction: {e2}")
                        return self._return_error_document(file_path, file_extension, str(e2))

                # For non-PDF files, try fallback methods
                if file_extension in self.loader_mapping:
                    try:
                        logger.info(f"Trying fallback custom loader for {file_extension}")
                        return self.loader_mapping[file_extension](file_path)
                    except Exception as e2:
                        logger.error(f"Fallback loader also failed for {file_extension}: {e2}")
                        return self._return_error_document(file_path, file_extension, str(e2))

                # No fallback available
                return self._return_error_document(file_path, file_extension, str(e))

        # Extension not supported
        return self._return_error_document(file_path, file_extension, "Unsupported file extension")

    def _return_error_document(self, file_path: str, file_extension: str, error_msg: str) -> List[Document]:
        """Return a Document with error information when loading fails."""
        logger.error(f"Creating error document for {file_path}: {error_msg}")
        return [
            Document(
                page_content=f"Error loading file: {error_msg}",
                metadata={
                    "file_path": file_path,
                    "file_extension": file_extension,
                    "error": error_msg,
                    "type": "error",
                },
            )
        ]

    def load_pdf(self, file_path: str) -> List[Document]:
        """Load a PDF document and extract its text and images.

        Args:
            file_path (str): The path of the PDF file.

        Returns:
            list: List of Document objects, each representing a page's text and OCR text from images.
        """
        start_time = time.time()
        logger.info(f"Opening PDF file.... {file_path}")
        try:
            doc = fitz.open(file_path)
            num_pages = doc.page_count
            doc.close()
        except Exception as e:
            logger.error(f"Error opening PDF file {file_path}: {e}")
            return []

        logger.info(f"Number of pages in PDF file {file_path}: {num_pages}")

        if num_pages == 0:
            return []

        # Process using ThreadPoolExecutor
        documents = []
        logger.info(f"Processing PDF file {file_path} using ThreadPoolExecutor")
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = {executor.submit(self.doc_checker.process_page, page_num, file_path): page_num for page_num in range(num_pages)}
            for future in futures:
                page_num = futures[future]
                try:
                    documents.append(future.result(timeout=500))
                except TimeoutError:
                    logger.error(f"Timeout while processing page {page_num} of {file_path}")
                    documents.append(
                        Document(
                            page_content="",
                            metadata={"page_num": page_num, "error": "Timeout"},
                        )
                    )
                except Exception as e:
                    logger.error(f"Error processing page {page_num} of {file_path}: {e}")
                    documents.append(
                        Document(
                            page_content="",
                            metadata={"page_num": page_num, "error": str(e)},
                        )
                    )

        end_time = time.time()
        logger.info(f"Load PDF took {end_time - start_time} seconds to process {file_path}")
        return documents

    def load_docx(self, file_path: str) -> List[Document]:
        """
        Load a DOCX document and extract its text and images as Document objects.
        """
        documents = []
        try:
            doc = DocxDocument(file_path)
            for idx, paragraph in enumerate(doc.paragraphs):
                text = paragraph.text.strip()
                if text:
                    documents.append(
                        Document(
                            page_content=text,
                            metadata={
                                "file_path": file_path,
                                "paragraph_index": idx,
                                "type": "docx",
                            },
                        )
                    )

            for shape_idx, shape in enumerate(doc.inline_shapes):
                # InlineShapeType.Picture == 3
                if shape.type == 3:
                    image_ref = shape._inline.graphic.graphicData.pic.blipFill.blip.embed
                    image_part = doc.part.related_parts[image_ref]
                    image = Image.open(io.BytesIO(image_part._blob))
                    extracted_text = self.doc_checker.extract_text_from_image(image)

                    if extracted_text.strip():
                        documents.append(
                            Document(
                                page_content=extracted_text,
                                metadata={
                                    "file_path": file_path,
                                    "inline_shape_index": shape_idx,
                                    "type": "docx-image",
                                },
                            )
                        )
        except Exception as e:
            logger.error(f"Error opening DOCX file {file_path}: {e}")
        return documents

    def load_excel(self, file_path: str) -> List[Document]:
        """
        Load an Excel file and return its content as a list of Document objects.
        Each row is converted into a single Document.
        """
        documents = []
        try:
            df = pd.read_excel(file_path)
            for idx, row in df.iterrows():
                row_content = " ".join([str(cell) for cell in row.to_list()])
                if row_content.strip():
                    documents.append(
                        Document(
                            page_content=row_content,
                            metadata={
                                "file_path": file_path,
                                "row_index": idx,
                                "type": "excel",
                            },
                        )
                    )
        except Exception as e:
            logger.error(f"Error reading Excel file {file_path}: {e}")
        return documents

    def load_markdown(self, file_path: str) -> List[Document]:
        """
        Load a Markdown file, convert it to HTML, and return as a single Document.
        """
        documents = []
        try:
            with open(file_path, "r", encoding="utf-8") as file:
                text = file.read()
            html = markdown2.markdown(text)
            documents.append(
                Document(
                    page_content=html,
                    metadata={"file_path": file_path, "type": "markdown"},
                )
            )
        except Exception as e:
            logger.error(f"Error reading Markdown file {file_path}: {e}")
        return documents

    def load_pptx(self, file_path: str) -> List[Document]:
        """
        Load a PowerPoint file and extract its text and images as Document objects.
        """
        documents = []
        try:
            prs = Presentation(file_path)
            for slide_idx, slide in enumerate(prs.slides):
                for shape_idx, shape in enumerate(slide.shapes):
                    if hasattr(shape, "text"):
                        text = shape.text.strip()
                        if text:
                            documents.append(
                                Document(
                                    page_content=text,
                                    metadata={
                                        "file_path": file_path,
                                        "slide_index": slide_idx,
                                        "shape_index": shape_idx,
                                        "type": "pptx",
                                    },
                                )
                            )
                    if shape.shape_type == 13 and hasattr(shape, "image"):
                        try:
                            img = Image.open(io.BytesIO(shape.image.blob))
                            extracted_text = self.doc_checker.extract_text_from_image(img)
                            if extracted_text.strip():
                                documents.append(
                                    Document(
                                        page_content=extracted_text,
                                        metadata={
                                            "file_path": file_path,
                                            "slide_index": slide_idx,
                                            "shape_index": shape_idx,
                                            "type": "pptx-image",
                                        },
                                    )
                                )
                        except Exception as e:
                            logger.error(f"Error extracting text from image in slide {slide_idx}, shape {shape_idx}: {e}")
        except Exception as e:
            logger.error(f"Error reading PowerPoint file {file_path}: {e}")
        return documents

    @staticmethod
    def analyze_image_with_gpt_vision(image_path) -> Optional[List[Document]]:
        """Analyze an image using GPT-4o Vision and return the description as a Document."""
        chat_model = AzureOpenAI(
            api_key=settings.openai_api_key,
            api_version=settings.openai_api_version,
            base_url=f"{settings.azure_openai_endpoint}openai/deployments/gpt4o",
        )

        mime_type, base64_data = local_image_to_base64(image_path)

        try:
            response = chat_model.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "You are a helpful assistant."},
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": "Describe this picture:"},
                            {
                                "type": "image_url",
                                "image_url": {"url": f"data:{mime_type};base64,{base64_data}"},
                            },
                        ],
                    },
                ],
                max_tokens=settings.max_token_count,
            )
            logger.info(f"response: {response.choices[0].message.content}")
            return [
                Document(
                    page_content=response.choices[0].message.content,
                    metadata={"image_path": image_path},
                )
            ]
        except Exception as e:
            logger.error(f"error: {e}")
            return []

    @staticmethod
    def analyze_doc_with_gpt_vision(file_path: str, start_page: Optional[int] = None, end_page: Optional[int] = None) -> List[Document]:
        """Analyze specific pages of a PDF document using GPT-4o Vision. If no range is provided, process all pages."""
        start_time = time.time()
        logger.info(f"Starting GPT-4o Vision analysis for PDF: {file_path}")

        chat_model = AzureOpenAI(
            api_key=settings.openai_api_key,
            api_version=settings.openai_api_version,
            base_url=f"{settings.azure_openai_endpoint}openai/deployments/gpt4o",
        )

        documents = []

        try:
            # Open PDF
            doc = fitz.open(file_path)
            num_pages = doc.page_count

            logger.info(f"Processing pages {start_page} to {end_page}")
            start: int = start_page or 1
            end: int = end_page if end_page is not None and end_page <= num_pages else num_pages

            for page_num in range(start - 1, end):
                page = doc[page_num]
                logger.info(f"Processing page {page_num + 1} of {num_pages}")

                # Get image from page
                img_pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
                img_pil = Image.frombytes("RGB", (img_pix.width, img_pix.height), img_pix.samples)

                # Convert image to base64
                mime_type, base64_data = image_to_base64(img_pil)

                try:
                    # GPT-4o Vision request
                    response = chat_model.chat.completions.create(
                        model="gpt-4o",
                        messages=[
                            {
                                "role": "system",
                                "content": "Du bist ein hilfreicher Assistent, der detaillierte und präzise Analysen durchführt.",
                            },
                            {
                                "role": "user",
                                "content": [
                                    {
                                        "type": "text",
                                        "text": "Extrahiere den gesamten Textinhalt dieser Seite exakt und vollständig, ohne Informationen auszulassen.",
                                    },
                                    {
                                        "type": "text",
                                        "text": "Falls visuelle Elemente wie Bilder, Diagramme oder Tabellen vorhanden sind, beschreibe sie detailliert.",
                                    },
                                    {
                                        "type": "text",
                                        "text": "Falls die Seite strukturierte Informationen enthält (z. B. eine Liste oder Tabelle), stelle sie in einer klaren und geordneten "
                                        "Form dar.",
                                    },
                                    {
                                        "type": "text",
                                        "text": "Falls es Überschriften, Absätze oder markierte Textbereiche gibt, behalte die ursprüngliche Struktur bei.",
                                    },
                                    {
                                        "type": "text",
                                        "text": "Falls die Seite formatierten Text enthält (z. B. fettgedruckt, kursiv, unterstrichen), markiere diese entsprechend.",
                                    },
                                    {
                                        "type": "image_url",
                                        "image_url": {"url": f"data:{mime_type};base64,{base64_data}"},
                                    },
                                ],
                            },
                        ],
                        max_tokens=settings.max_token_count,
                    )

                    # Extracted text
                    extracted_text = response.choices[0].message.content
                    logger.info(f"GPT-4o Vision extracted text from page {page_num + 1}: {extracted_text[:100]}...")

                    # Append to documents
                    documents.append(
                        Document(
                            page_content=extracted_text,
                            metadata={
                                "file_path": file_path,
                                "page_num": page_num + 1,
                                "type": "pdf-screenshot",
                            },
                        )
                    )

                except Exception as e:
                    logger.error(f"Error processing page {page_num + 1}: {e}")

            doc.close()

        except Exception as e:
            logger.error(f"Error processing PDF {file_path}: {e}")

        end_time = time.time()
        logger.info(f"Completed GPT-4o Vision analysis for {file_path} in {end_time - start_time:.2f} seconds.")

        return documents

    def load_json(self, file_path: str) -> Any:
        """Load a JSON file and return its cleaned content.

        Args:
            file_path (str): The path of the JSON file.

        Returns:
            List[Dict[str, Optional[str]]]: Extracted content from the JSON file.
        """

        with open(file_path, "r", encoding="utf-8") as file:
            json_data = json.load(file)
            return self.doc_checker.extract_content_from_json(json_data)

    def load_json2(self, file_path: str) -> List[Document]:
        """
        Load a JSON file and return its content as a list of Document objects.

        if the top-level object is a dict, each key-value pair is a separate Document.
        if the top-level object is a list, each item is a separate Document.
        if the top-level object is a simple type (e.g., int, string), it is returned as a single Document.
        """

        documents = []
        try:
            with open(file_path, "r", encoding="utf-8") as file:
                data = json.load(file)

            # 1) if top-level object is a dict:
            if isinstance(data, dict):
                for key, value in data.items():
                    # convert each key-value pair to a string:
                    value_str = json.dumps(value, ensure_ascii=False, indent=2)
                    doc_text = f"Key: {key}\n\n{value_str}"
                    documents.append(
                        Document(
                            page_content=doc_text,
                            metadata={
                                "file_path": file_path,
                                "json_key": key,
                                "type": "json-dict",
                            },
                        )
                    )

            # 2) if top-level object is a list:
            elif isinstance(data, list):
                for idx, item in enumerate(data):
                    # convert each item to a string:
                    item_str = json.dumps(item, ensure_ascii=False, indent=2)
                    doc_text = f"List Index: {idx}\n\n{item_str}"
                    documents.append(
                        Document(
                            page_content=doc_text,
                            metadata={
                                "file_path": file_path,
                                "json_index": idx,
                                "type": "json-list",
                            },
                        )
                    )
            else:
                # 3) if top-level object is a simple type:
                doc_text = json.dumps(data, ensure_ascii=False, indent=2)
                documents.append(
                    Document(
                        page_content=doc_text,
                        metadata={
                            "file_path": file_path,
                            "type": "json-other",
                        },
                    )
                )

        except Exception as e:
            logger.error(f"Error reading JSON file {file_path}: {e}")

        return documents
