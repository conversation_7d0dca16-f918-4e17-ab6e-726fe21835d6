"""Atlassian Service for Jira and Confluence integration."""

import json
import logging
from typing import Dict, List
from urllib.parse import urlparse

import aiohttp

logger = logging.getLogger(__name__)


class AtlassianService:
    """Service for interacting with Atlassian Jira and Confluence APIs."""

    def __init__(self):
        """Initialize the service."""
        self.base_url = None
        self.username = None
        self.api_token = None
        self.session = None

    async def authenticate(self, base_url: str, username: str, api_token: str) -> bool:
        """
        Authenticate with Atlassian using API token.

        Args:
            base_url: Atlassian instance base URL (e.g., https://yourcompany.atlassian.net)
            username: User email address
            api_token: API token from Atlassian

        Returns:
            bool: True if authentication successful, False otherwise
        """
        try:
            # Normalize base URL (strip any path like /wiki)
            parsed = urlparse(base_url)
            if parsed.scheme and parsed.netloc:
                normalized_base = f"{parsed.scheme}://{parsed.netloc}"
            else:
                normalized_base = base_url.strip().rstrip("/")

            self.base_url = normalized_base
            self.username = username
            self.api_token = api_token

            # Create session with basic auth
            connector = aiohttp.TCPConnector(ssl=False)
            auth = aiohttp.BasicAuth(username, api_token)
            self.session = aiohttp.ClientSession(auth=auth, connector=connector, timeout=aiohttp.ClientTimeout(total=30))

            # Test authentication with a simple request
            test_url = f"{self.base_url}/rest/api/3/myself"
            async with self.session.get(test_url) as response:
                if response.status == 200:
                    return True
                else:
                    logger.error(f"Authentication failed: {response.status}")
                    await self.close_session()
                    return False

        except Exception as e:
            logger.exception(f"Authentication error: {e}")
            await self.close_session()
            return False

    async def close_session(self):
        """Close the aiohttp session."""
        if self.session:
            await self.session.close()
            self.session = None

    async def get_jira_projects(self) -> List[Dict]:
        """
        Get all accessible Jira projects.

        Returns:
            List of project dictionaries with id, key, name, description
        """
        if not self.session:
            raise ValueError("Not authenticated. Call authenticate() first.")

        try:
            # Try API v3 first
            url = f"{self.base_url}/rest/api/3/project"
            async with self.session.get(url) as response:
                if response.status == 200:
                    projects = await response.json()
                    return [
                        {
                            "id": str(project["id"]),  # Convert to string to match Pydantic model
                            "key": project["key"],
                            "name": project["name"],
                            "description": project.get("description", ""),
                            "project_type": project.get("projectTypeKey", ""),
                            "lead": project.get("lead", {}).get("displayName", ""),
                        }
                        for project in projects
                    ]
                else:
                    logger.error(f"Failed to fetch Jira projects: {response.status}")
                    return []
        except Exception as e:
            logger.exception(f"Error fetching Jira projects: {e}")
            return []

    async def get_jira_issues(self, project_key: str, max_results: int = 10000) -> List[Dict]:
        """
        Get issues from a specific Jira project with pagination support.

        Args:
            project_key: Project key (e.g., 'PROJ')
            max_results: Maximum number of issues to return (uses pagination to fetch all)

        Returns:
            List of issue dictionaries
        """
        if not self.session:
            raise ValueError("Not authenticated. Call authenticate() first.")

        try:
            jql = f"project = {project_key} ORDER BY key ASC"
            # Try the standard search endpoint first
            url = f"{self.base_url}/rest/api/3/search"

            # First collect all raw issues from all pages
            raw_issues_dict: dict[str, dict] = {}  # Use dict with issue_id as key to automatically handle duplicates
            start_at = 0
            page_size = 100  # Jira API limit per request

            max_pages = 100  # Safety limit - supports up to 10,000 issues (100 pages x 100 issues/page)
            page_count = 0
            selected_mode = None  # Cache working endpoint/method once found
            previous_first_issue_id = None
            cursor_next_url = None  # If API returns a nextPage cursor URL, prefer it
            last_seen_id = None  # Use id-based pagination to avoid startAt issues

            while len(raw_issues_dict) < max_results and page_count < max_pages:
                # Always use fixed page size of 100 for Jira API
                # Recompute JQL using id-based pagination to avoid startAt issues
                if last_seen_id is None:
                    jql = f"project = {project_key} ORDER BY id ASC"
                else:
                    jql = f"project = {project_key} AND id > {last_seen_id} ORDER BY id ASC"

                # Prepare both query params and JSON body for different endpoint variants
                params = {
                    "jql": jql,
                    "maxResults": page_size,  # Always 100
                    "startAt": 0,
                    "fields": "summary,description,status,assignee,creator,created,updated,priority,issuetype,labels,comment,issuelinks,subtasks,parent,worklog",
                    "expand": "changelog",
                }
                request_body = {
                    "jql": jql,
                    "maxResults": page_size,
                    "startAt": 0,
                    "fields": [
                        "summary",
                        "description",
                        "status",
                        "assignee",
                        "creator",
                        "created",
                        "updated",
                        "priority",
                        "issuetype",
                        "labels",
                        "comment",
                        "issuelinks",
                        "subtasks",
                        "parent",
                        "worklog",
                    ],
                    "expand": ["changelog"],
                }

                page_count += 1
                data = None
                endpoint_used = None

                async def try_request(mode: str, jql_param: str, request_body_param: dict, params_param: dict):
                    headers = {"Accept": "application/json", "Content-Type": "application/json"}
                    if mode == "POST_JQL":
                        url_local = f"{self.base_url}/rest/api/3/search/jql"
                        body = {"queries": [{"jql": jql_param, "startAt": start_at, "maxResults": page_size, "fields": request_body_param["fields"]}]}
                        async with self.session.post(url_local, json=body, headers=headers) as resp:
                            return resp.status, (await resp.text()), dict(resp.headers)
                    if mode == "POST_SEARCH":
                        url_local = f"{self.base_url}/rest/api/3/search"
                        async with self.session.post(url_local, json=request_body_param, headers=headers) as resp:
                            return resp.status, (await resp.text()), dict(resp.headers)
                    if mode == "GET_JQL":
                        url_local = f"{self.base_url}/rest/api/3/search/jql"
                        async with self.session.get(url_local, params=params_param, headers=headers) as resp:
                            return resp.status, (await resp.text()), dict(resp.headers)
                    if mode == "GET_SEARCH":
                        url_local = f"{self.base_url}/rest/api/3/search"
                        async with self.session.get(url_local, params=params_param, headers=headers) as resp:
                            return resp.status, (await resp.text()), dict(resp.headers)
                    return 0, "", {}

                async def perform(mode: str, jql_param: str, request_body_param: dict, params_param: dict):
                    status, text, headers_local = await try_request(mode, jql_param, request_body_param, params_param)
                    if status == 200:
                        try:
                            data_local = json.loads(text)
                            # Parse Link header for next cursor if present
                            link_header = headers_local.get("Link") or headers_local.get("link")
                            if link_header:
                                try:
                                    parts = [p.strip() for p in link_header.split(",")]
                                    for part in parts:
                                        if 'rel="next"' in part:
                                            url_part = part.split(";")[0].strip()
                                            if url_part.startswith("<") and url_part.endswith(">"):
                                                nonlocal cursor_next_url
                                                cursor_next_url = url_part[1:-1]
                                except Exception:
                                    pass
                            return data_local, mode
                        except Exception:
                            logger.error(f"Failed to parse JSON for {mode}")
                            return None, None
                    return None, None

                # If we have a cursor URL and using GET_JQL, fetch directly via cursor
                headers = {"Accept": "application/json"}
                if selected_mode == "GET_JQL" and cursor_next_url:
                    async with self.session.get(cursor_next_url, headers=headers) as resp:
                        if resp.status == 200:
                            try:
                                data = await resp.json()
                                endpoint_used = "GET_JQL_CURSOR"
                            except Exception:
                                data = None
                        else:
                            data = None

                # Decide mode once (first page), then reuse
                if data is None:
                    if selected_mode is None:
                        for mode in ["POST_JQL", "POST_SEARCH", "GET_JQL", "GET_SEARCH"]:
                            data, endpoint_used = await perform(mode, jql, request_body, params)
                            if data is not None:
                                selected_mode = endpoint_used
                                break
                    else:
                        data, endpoint_used = await perform(selected_mode, jql, request_body, params)

                # Fallback if none worked
                if data is None:
                    logger.error("All Jira search endpoint attempts failed for this page. Stopping.")
                    break

                if data is None:
                    logger.error("All Jira search endpoint attempts failed for this page. Stopping.")
                    break

                # Parse issues depending on endpoint format
                if (selected_mode or endpoint_used or "").startswith("POST_JQL") and "results" in data:
                    results_list = data.get("results", [])
                    first_result = results_list[0] if results_list else {}
                    page_issues = first_result.get("issues", [])
                    is_last_flag = first_result.get("isLast", None)
                    # Capture cursor if available
                    cursor_next_url = first_result.get("nextPage") or data.get("nextPage")
                else:
                    page_issues = data.get("issues", [])
                    is_last_flag = data.get("isLast", None)
                    # Capture cursor if available (GET /search/jql often returns nextPage)
                    cursor_next_url = data.get("nextPage")

                if not page_issues:
                    break

                # Add issues to dict (automatically handles duplicates)
                duplicates_in_page = 0
                new_in_page = 0
                for issue in page_issues:
                    issue_id = str(issue["id"])
                    if issue_id in raw_issues_dict:
                        duplicates_in_page += 1
                    else:
                        raw_issues_dict[issue_id] = issue
                        new_in_page += 1

                # Detect if endpoint ignores startAt (same first issue repeatedly)
                current_first_issue_id = str(page_issues[0]["id"]) if page_issues else None
                if previous_first_issue_id and current_first_issue_id == previous_first_issue_id and selected_mode in ("GET_JQL", "GET_SEARCH"):
                    # Force reselect mode next iteration
                    selected_mode = None
                previous_first_issue_id = current_first_issue_id

                # Stop if API indicates last page
                if is_last_flag is True:
                    break

                # If no new issues in this page for 3 consecutive pages, we're done (safety)
                if new_in_page == 0:
                    consecutive_empty_pages = getattr(self, "_consecutive_empty_pages", 0) + 1
                    self._consecutive_empty_pages = consecutive_empty_pages
                    if consecutive_empty_pages >= 3:
                        break
                else:
                    # Reset counter when we find new issues
                    self._consecutive_empty_pages = 0

                # If we got fewer issues than requested, we've reached the end
                if len(page_issues) < page_size:
                    break

                # Advance window using last numeric id
                try:
                    last_seen_id = int(page_issues[-1]["id"]) if page_issues else last_seen_id
                except Exception:
                    last_seen_id = last_seen_id

            # Now process all unique issues
            if page_count >= max_pages:
                logger.warning(f"Reached maximum page limit ({max_pages}) - may not have fetched all issues")

            all_issues = []

            for issue_id, issue in raw_issues_dict.items():
                fields = issue["fields"]

                # Extract description as string from Atlassian Document Format
                description_raw = fields.get("description", "")
                description = ""
                if description_raw:
                    if isinstance(description_raw, dict):
                        # Try to extract text from ADF (Atlassian Document Format)
                        description = self._extract_text_from_adf(description_raw)
                    else:
                        description = str(description_raw)

                # Extract subtasks
                subtask_numbers = []
                if fields.get("subtasks"):
                    subtask_numbers = [subtask.get("key", "") for subtask in fields["subtasks"]]

                # Extract parent task
                parent_task = ""
                if fields.get("parent"):
                    parent_task = fields["parent"].get("key", "")

                # Extract activities (comments)
                activities = []
                if fields.get("comment", {}).get("comments"):
                    for comment in fields["comment"]["comments"]:
                        author = comment.get("author", {}).get("displayName", "Unknown")
                        created_date = comment.get("created", "")[:10] if comment.get("created") else ""
                        body = comment.get("body", "")
                        if isinstance(body, dict):
                            # Extract text from ADF format
                            body = self._extract_text_from_adf(body)
                        activity_text = f"[comment {created_date}] {author}: {body[:500]}{'...' if len(body) > 500 else ''}"
                        activities.append(activity_text)

                # Add worklog entries to activities
                worklog = fields.get("worklog", {})
                for wl in worklog.get("worklogs", []) or []:
                    wl_author = wl.get("author", {}).get("displayName", "Unknown")
                    wl_started = wl.get("started", "")[:10] if wl.get("started") else ""
                    wl_time = wl.get("timeSpent", "")
                    activities.append(f"[worklog {wl_started}] {wl_author}: {wl_time}")

                # Add changelog (history) items to activities (if present on issue object)
                changelog = issue.get("changelog", {})
                for history in changelog.get("histories", []) or []:
                    hist_author = history.get("author", {}).get("displayName", "Unknown")
                    hist_created = history.get("created", "")[:10] if history.get("created") else ""
                    items = history.get("items", []) or []
                    for it in items:
                        field = it.get("field", "field")
                        from_str = str(it.get("fromString", ""))
                        to_str = str(it.get("toString", ""))
                        activities.append(f"[history {hist_created}] {hist_author}: {field} -> '{from_str}' → '{to_str}'")

                # Extract connected work items (linked issues)
                connected_work_items = []
                if fields.get("issuelinks"):
                    for link in fields["issuelinks"]:
                        link_type = link.get("type", {}).get("name", "Related")
                        if "inwardIssue" in link:
                            related_issue = link["inwardIssue"]
                            connected_work_items.append(f"{link_type}: {related_issue['key']} - {related_issue['fields']['summary']}")
                        elif "outwardIssue" in link:
                            related_issue = link["outwardIssue"]
                            connected_work_items.append(f"{link_type}: {related_issue['key']} - {related_issue['fields']['summary']}")

                all_issues.append(
                    {
                        "id": issue_id,
                        "key": issue["key"],
                        "summary": fields.get("summary", ""),
                        "description": description,
                        "status": fields.get("status", {}).get("name", ""),
                        "assignee": fields.get("assignee", {}).get("displayName", "") if fields.get("assignee") else "",
                        "creator": fields.get("creator", {}).get("displayName", ""),
                        "created": fields.get("created", ""),
                        "updated": fields.get("updated", ""),
                        "priority": fields.get("priority", {}).get("name", "") if fields.get("priority") else "",
                        "issue_type": fields.get("issuetype", {}).get("name", ""),
                        "labels": fields.get("labels", []),
                        "project_key": project_key,
                        # Extended fields
                        "task_number": issue["key"],  # Same as key, but explicitly for task number
                        "subtask_numbers": subtask_numbers,
                        "parent_task": parent_task,
                        "activities": activities,
                        "connected_work_items": connected_work_items,
                    }
                )

            return all_issues
        except Exception as e:
            logger.exception(f"Error fetching Jira issues: {e}")
            return []

    async def get_confluence_spaces(self) -> List[Dict]:
        """
        Get all accessible Confluence spaces with pagination support.

        Returns:
            List of space dictionaries
        """
        if not self.session:
            raise ValueError("Not authenticated. Call authenticate() first.")

        try:
            # Use Confluence base URL for Confluence API calls
            base_url = getattr(self, "confluence_base_url", self.base_url)
            url = f"{base_url}/wiki/rest/api/space"

            # Collect all spaces using pagination
            all_spaces = []
            start = 0
            page_size = 100  # Confluence API limit per request

            max_pages = 50  # Safety limit - supports up to 5,000 spaces (50 pages x 100 spaces/page)
            page_count = 0

            while page_count < max_pages:
                page_count += 1

                params = {"start": start, "limit": page_size}

                async with self.session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        space_items = data.get("results", [])

                        if not space_items:
                            break

                        # Process spaces in this batch
                        for space in space_items:
                            all_spaces.append(
                                {
                                    "id": str(space["id"]),  # Convert to string to match Pydantic model
                                    "key": space["key"],
                                    "name": space["name"],
                                    "description": space.get("description", {}).get("plain", {}).get("value", ""),
                                    "type": space.get("type", ""),
                                    "status": space.get("status", ""),
                                }
                            )

                        # If we got fewer spaces than requested, we've reached the end
                        if len(space_items) < page_size:
                            break

                        # Advance to next page
                        start += page_size

                    else:
                        logger.error(f"Failed to fetch Confluence spaces: {response.status}")
                        break

            if page_count >= max_pages:
                logger.warning(f"Reached maximum page limit ({max_pages}) - may not have fetched all spaces")

            return all_spaces

        except Exception as e:
            logger.exception(f"Error fetching Confluence spaces: {e}")
            return []

    async def get_confluence_pages(self, space_key: str, max_results: int = 10000) -> List[Dict]:
        """
        Get pages from a specific Confluence space with pagination support.

        Args:
            space_key: Space key
            max_results: Maximum number of pages to return (uses pagination to fetch all)

        Returns:
            List of page dictionaries
        """
        if not self.session:
            raise ValueError("Not authenticated. Call authenticate() first.")

        try:
            # Use Confluence base URL for Confluence API calls
            base_url = getattr(self, "confluence_base_url", self.base_url)
            url = f"{base_url}/wiki/rest/api/content"

            # Collect all pages from all pages using pagination
            all_pages: list[dict] = []
            start = 0
            page_size = 100  # Confluence API limit per request

            max_pages = 100  # Safety limit - supports up to 10,000 pages (100 pages x 100 pages/page)
            page_count = 0

            while len(all_pages) < max_results and page_count < max_pages:
                page_count += 1

                params = {"spaceKey": space_key, "type": "page", "start": start, "limit": page_size, "expand": "body.storage,version,space,history,metadata.labels,ancestors"}

                async with self.session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        page_items = data.get("results", [])

                        if not page_items:
                            break

                        # Process pages in this batch
                        new_in_page = 0
                        for page in page_items:
                            # Extract plain text from storage format
                            body_content = ""
                            if page.get("body", {}).get("storage", {}).get("value"):
                                # Simple HTML stripping - in production you might want more sophisticated parsing
                                import re

                                html_content = page["body"]["storage"]["value"]
                                body_content = re.sub(r"<[^>]+>", "", html_content)
                                # Clean up excessive whitespace
                                body_content = re.sub(r"\n\s*\n", "\n\n", body_content)
                                body_content = re.sub(r"&[^;]+;", "", body_content)  # Remove HTML entities

                            # Extract labels
                            labels = []
                            metadata = page.get("metadata", {})
                            if metadata and "labels" in metadata:
                                labels = [label.get("name", "") for label in metadata["labels"].get("results", [])]

                            # Extract parent info from ancestors
                            parent = ""
                            ancestors = page.get("ancestors", [])
                            if ancestors:
                                parent = ancestors[-1].get("title", "")

                            # Extract history info
                            history = page.get("history", {})
                            created_by = history.get("createdBy", {})

                            page_data = {
                                "id": str(page["id"]),  # Convert to string to match Pydantic model
                                "title": page["title"],
                                "content": body_content,
                                "space_key": space_key,
                                "space_name": page.get("space", {}).get("name", ""),
                                "created": history.get("createdDate", "") or page.get("version", {}).get("when", ""),
                                "creator": created_by.get("displayName", "") or page.get("version", {}).get("by", {}).get("displayName", ""),
                                "status": page.get("status", ""),
                                "type": page.get("type", ""),
                                "labels": labels,
                                "parent": parent,
                                "url": f"{base_url}/wiki{page.get('_links', {}).get('webui', '')}",
                            }
                            all_pages.append(page_data)
                            new_in_page += 1

                        # If we got fewer pages than requested, we've reached the end
                        if len(page_items) < page_size:
                            break

                        # Advance to next page
                        start += page_size

                    else:
                        logger.error(f"Failed to fetch Confluence pages: {response.status}")
                        break

            if page_count >= max_pages:
                logger.warning(f"Reached maximum page limit ({max_pages}) - may not have fetched all pages")

            return all_pages

        except Exception as e:
            logger.exception(f"Error fetching Confluence pages: {e}")
            return []

    async def get_confluence_space_tree(self, space_key: str, max_depth: int = 3) -> List[Dict]:
        """
        Get complete site tree for a Confluence space.

        Args:
            space_key: Space key
            max_depth: Maximum depth to traverse (default 3)

        Returns:
            List of all pages in the space organized as a tree
        """
        if not self.session:
            raise ValueError("Not authenticated. Call authenticate() first.")

        try:
            all_pages = []

            # First, get all pages in the space
            base_url = getattr(self, "confluence_base_url", self.base_url)
            url = f"{base_url}/wiki/rest/api/content"
            start = 0
            limit = 100

            while True:
                params = {"spaceKey": space_key, "type": "page", "limit": limit, "start": start, "expand": "ancestors,children.page"}

                async with self.session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        pages = data.get("results", [])

                        if not pages:
                            break

                        all_pages.extend(pages)
                        start += limit

                        # Break if we've got all pages
                        if len(pages) < limit:
                            break
                    else:
                        logger.error(f"Failed to fetch pages for space {space_key}: {response.status}")
                        break

            return all_pages

        except Exception as e:
            logger.exception(f"Error fetching Confluence space tree: {e}")
            return []

    async def get_jira_project_content_batch(self, project_key: str, batch_size: int = 10) -> List[Dict]:
        """
        Get all issues from a project in batches for better performance.

        Args:
            project_key: Project key
            batch_size: Number of issues to process concurrently

        Returns:
            List of formatted issue content
        """
        if not self.session:
            raise ValueError("Not authenticated. Call authenticate() first.")

        try:
            # First get all issues
            all_issues = await self.get_jira_issues(project_key, max_results=1000)

            content_items = []

            # Process in batches
            for i in range(0, len(all_issues), batch_size):
                batch = all_issues[i : i + batch_size]

                # Process batch concurrently
                import asyncio

                tasks = [self.get_jira_issue_content(issue["key"]) for issue in batch]

                batch_contents = await asyncio.gather(*tasks, return_exceptions=True)

                for j, content in enumerate(batch_contents):
                    if isinstance(content, Exception):
                        logger.error(f"Failed to get content for issue {batch[j]['key']}: {content}")
                        continue

                    if content:
                        content_items.append(
                            {
                                "id": batch[j]["key"],
                                "title": f"Jira Issue: {batch[j]['key']} - {batch[j].get('summary', '')}",
                                "content": content,
                                "source": f"jira_issue_{batch[j]['key']}",
                                "metadata": {
                                    "content_type": "jira_issue",
                                    "project_key": project_key,
                                    "issue_key": batch[j]["key"],
                                    "summary": batch[j].get("summary", ""),
                                    "status": batch[j].get("status", ""),
                                    "issue_type": batch[j].get("issue_type", ""),
                                    "priority": batch[j].get("priority", ""),
                                },
                            }
                        )

            return content_items

        except Exception as e:
            logger.exception(f"Error getting Jira project content in batch: {e}")
            return []

    async def get_confluence_space_content_batch(self, space_key: str, batch_size: int = 10) -> List[Dict]:
        """
        Get all pages from a space in batches for better performance.

        Args:
            space_key: Space key
            batch_size: Number of pages to process concurrently

        Returns:
            List of formatted page content
        """
        if not self.session:
            raise ValueError("Not authenticated. Call authenticate() first.")

        try:
            # Use the improved get_confluence_pages method which includes full content and pagination
            all_pages = await self.get_confluence_pages(space_key, max_results=10000)

            content_items = []

            # Process pages directly since get_confluence_pages now includes full content
            for page in all_pages:
                try:
                    # Use the content already extracted in get_confluence_pages
                    content = page.get("content", "")

                    # If content is empty, try to get it via detailed page fetch
                    if not content.strip():
                        content = await self.get_confluence_page_content(page["id"])

                    if content and content.strip():
                        content_items.append(
                            {
                                "id": page["id"],
                                "title": f"Confluence Page: {page.get('title', page['id'])}",
                                "content": content,
                                "source": f"confluence_page_{page['id']}",
                                "metadata": {
                                    "content_type": "confluence_page",
                                    "space_key": space_key,
                                    "page_id": page["id"],
                                    "title": page.get("title", ""),
                                    "space_name": page.get("space_name", ""),
                                    "creator": page.get("creator", ""),
                                    "created": page.get("created", ""),
                                    "labels": page.get("labels", []),
                                    "parent": page.get("parent", ""),
                                    "url": page.get("url", ""),
                                },
                            }
                        )

                except Exception as e:
                    logger.error(f"Failed to process page {page.get('id', 'unknown')}: {e}")
                    continue

            return content_items

        except Exception as e:
            logger.exception(f"Error getting Confluence space content in batch: {e}")
            return []

    async def get_jira_issue_content(self, issue_key: str) -> str:
        """
        Get comprehensive formatted content for a specific Jira issue.

        Args:
            issue_key: Issue key (e.g., 'PROJ-123')

        Returns:
            Formatted text content of the issue including comments and links
        """
        if not self.session:
            raise ValueError("Not authenticated. Call authenticate() first.")

        try:
            # Try API v3 first
            url = f"{self.base_url}/rest/api/3/issue/{issue_key}"
            params = {"fields": "summary,description,status,assignee,creator,created,updated,priority,issuetype,labels,comment,issuelinks,attachment,resolution,resolutiondate"}

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    issue = await response.json()
                    fields = issue["fields"]

                    # Format issue content
                    content_parts = [
                        f"Issue: {issue['key']}",
                        f"Summary: {fields.get('summary', '')}",
                        f"Type: {fields.get('issuetype', {}).get('name', '')}",
                        f"Status: {fields.get('status', {}).get('name', '')}",
                        f"Priority: {fields.get('priority', {}).get('name', '') if fields.get('priority') else 'None'}",
                        f"Assignee: {fields.get('assignee', {}).get('displayName', 'Unassigned') if fields.get('assignee') else 'Unassigned'}",
                        f"Creator: {fields.get('creator', {}).get('displayName', '')}",
                        f"Created: {fields.get('created', '')}",
                        f"Updated: {fields.get('updated', '')}",
                        f"Labels: {', '.join(fields.get('labels', []))}",
                    ]

                    # Add resolution information if available
                    if fields.get("resolution"):
                        content_parts.extend([f"Resolution: {fields['resolution'].get('name', '')}", f"Resolution Date: {fields.get('resolutiondate', '')}"])

                    # Handle description (could be ADF format)
                    description_raw = fields.get("description", "")
                    if isinstance(description_raw, dict):
                        description_text = self._extract_text_from_adf(description_raw)
                    else:
                        description_text = str(description_raw) if description_raw else "No description available"

                    content_parts.extend(["", "Description:", description_text, ""])

                    # Add comments
                    comments = fields.get("comment", {}).get("comments", [])
                    if comments:
                        content_parts.append("Comments:")
                        for comment in comments:
                            author = comment.get("author", {}).get("displayName", "Unknown")
                            created = comment.get("created", "")[:10] if comment.get("created") else ""
                            body = comment.get("body", "")

                            # Handle ADF (Atlassian Document Format) or string body
                            if isinstance(body, dict):
                                # Extract text from ADF format
                                body_text = self._extract_text_from_adf(body)
                            else:
                                body_text = str(body) if body else ""

                            # Extract links from comment body
                            import re

                            confluence_links = re.findall(r"https?://[^/\s]+/wiki/spaces/[^\s\]]+", body_text)

                            content_parts.extend(
                                [
                                    f"  - Comment by {author} ({created}):",
                                    f"    {body_text[:500]}{'...' if len(body_text) > 500 else ''}",
                                ]
                            )

                            # Add any Confluence links found in comments
                            if confluence_links:
                                content_parts.append(f"    Confluence links: {', '.join(confluence_links)}")
                        content_parts.append("")

                    # Add issue links
                    issue_links = fields.get("issuelinks", [])
                    if issue_links:
                        content_parts.append("Related Issues:")
                        for link in issue_links:
                            link_type = link.get("type", {}).get("name", "Related")
                            if "inwardIssue" in link:
                                related_issue = link["inwardIssue"]
                                content_parts.append(f"  - {link_type}: {related_issue['key']} - {related_issue['fields']['summary']}")
                            elif "outwardIssue" in link:
                                related_issue = link["outwardIssue"]
                                content_parts.append(f"  - {link_type}: {related_issue['key']} - {related_issue['fields']['summary']}")
                        content_parts.append("")

                    # Add attachments
                    attachments = fields.get("attachment", [])
                    if attachments:
                        content_parts.append("Attachments:")
                        for attachment in attachments:
                            filename = attachment.get("filename", "")
                            author = attachment.get("author", {}).get("displayName", "Unknown")
                            created = attachment.get("created", "")[:10] if attachment.get("created") else ""
                            content_parts.append(f"  - {filename} (uploaded by {author} on {created})")
                        content_parts.append("")

                    return "\n".join(content_parts)
                else:
                    logger.error(f"Failed to fetch Jira issue: {response.status}")
                    return ""
        except Exception as e:
            logger.exception(f"Error fetching Jira issue content: {e}")
            return ""

    async def get_confluence_page_content(self, page_id: str) -> str:
        """
        Get comprehensive formatted content for a specific Confluence page.

        Args:
            page_id: Page ID

        Returns:
            Formatted text content of the page including links and attachments
        """
        if not self.session:
            raise ValueError("Not authenticated. Call authenticate() first.")

        try:
            # Use Confluence base URL for Confluence API calls
            base_url = getattr(self, "confluence_base_url", self.base_url)
            url = f"{base_url}/wiki/rest/api/content/{page_id}"
            params = {"expand": "body.storage,version,space,children.page,ancestors,metadata.labels"}

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    page = await response.json()

                    # Extract and clean content
                    body_content = ""
                    links_found = []
                    if page.get("body", {}).get("storage", {}).get("value"):
                        import re

                        html_content = page["body"]["storage"]["value"]

                        # Extract internal and external links before removing HTML
                        internal_links = re.findall(r'<ac:link><ri:page ri:content-title="([^"]+)"', html_content)
                        external_links = re.findall(r'href="(https?://[^"]+)"', html_content)
                        confluence_page_links = re.findall(r'/wiki/spaces/[^/]+/pages/\d+/[^">\s]+', html_content)

                        # Clean HTML content
                        body_content = re.sub(r"<[^>]+>", "", html_content)
                        body_content = re.sub(r"\n\s*\n", "\n\n", body_content)  # Clean up excessive newlines
                        body_content = re.sub(r"&[^;]+;", "", body_content)  # Remove HTML entities

                        links_found = internal_links + external_links + confluence_page_links

                    # Format page content
                    content_parts = [
                        f"Page: {page['title']}",
                        f"Space: {page.get('space', {}).get('name', '')}",
                        f"Page ID: {page_id}",
                        f"Created: {page.get('version', {}).get('when', '')}",
                        f"Creator: {page.get('version', {}).get('by', {}).get('displayName', '')}",
                        f"Version: {page.get('version', {}).get('number', 1)}",
                    ]

                    # Add labels if available
                    labels = page.get("metadata", {}).get("labels", {}).get("results", [])
                    if labels:
                        label_names = [label.get("name", "") for label in labels]
                        content_parts.append(f"Labels: {', '.join(label_names)}")

                    # Add parent pages (breadcrumb)
                    ancestors = page.get("ancestors", [])
                    if ancestors:
                        parent_titles = [ancestor.get("title", "") for ancestor in ancestors[-3:]]  # Last 3 ancestors
                        content_parts.append(f"Parent Pages: {' > '.join(parent_titles)}")

                    content_parts.extend(["", "Content:", body_content, ""])

                    # Add child pages
                    children = page.get("children", {}).get("page", {}).get("results", [])
                    if children:
                        content_parts.append("Child Pages:")
                        for child in children[:10]:  # Limit to first 10 children
                            content_parts.append(f"  - {child.get('title', '')}")
                        if len(children) > 10:
                            content_parts.append(f"  ... and {len(children) - 10} more")
                        content_parts.append("")

                    # Add links found in content
                    if links_found:
                        content_parts.append("Links in this page:")
                        unique_links = list(set(links_found))[:20]  # Limit to 20 unique links
                        for link in unique_links:
                            content_parts.append(f"  - {link}")
                        if len(links_found) > 20:
                            content_parts.append(f"  ... and {len(links_found) - 20} more links")
                        content_parts.append("")

                    # Get attachments for this page
                    try:
                        base_url = getattr(self, "confluence_base_url", self.base_url)
                        attachments_url = f"{base_url}/wiki/rest/api/content/{page_id}/child/attachment"
                        async with self.session.get(attachments_url) as attach_response:
                            if attach_response.status == 200:
                                attach_data = await attach_response.json()
                                attachments = attach_data.get("results", [])
                                if attachments:
                                    content_parts.append("Attachments:")
                                    for attachment in attachments[:10]:  # Limit to 10 attachments
                                        title = attachment.get("title", "")
                                        size = attachment.get("extensions", {}).get("fileSize", 0)
                                        size_mb = round(size / (1024 * 1024), 2) if size > 0 else 0
                                        content_parts.append(f"  - {title} ({size_mb} MB)")
                                    if len(attachments) > 10:
                                        content_parts.append(f"  ... and {len(attachments) - 10} more attachments")
                                    content_parts.append("")
                    except Exception as e:
                        logger.debug(f"Failed to get attachments for page {page_id}: {e}")

                    return "\n".join(content_parts)
                else:
                    logger.error(f"Failed to fetch Confluence page: {response.status}")
                    return ""
        except Exception as e:
            logger.exception(f"Error fetching Confluence page content: {e}")
            return ""

    def _extract_text_from_adf(self, adf_node):
        """
        Extract plain text from Atlassian Document Format (ADF).

        Args:
            adf_node: ADF node (dict) from Jira API

        Returns:
            str: Plain text content
        """
        if not isinstance(adf_node, dict):
            return str(adf_node) if adf_node else ""

        text_parts = []

        # Extract text content
        if "text" in adf_node:
            text_parts.append(adf_node["text"])

        # Recursively process content nodes
        if "content" in adf_node and isinstance(adf_node["content"], list):
            for child_node in adf_node["content"]:
                child_text = self._extract_text_from_adf(child_node)
                if child_text:
                    text_parts.append(child_text)

        # Add spacing for different node types
        node_type = adf_node.get("type", "")
        if node_type in ["paragraph", "heading"]:
            return " ".join(text_parts) + "\n"
        elif node_type in ["listItem"]:
            return "• " + " ".join(text_parts) + "\n"
        else:
            return " ".join(text_parts)

    def _html_to_text(self, html_content: str) -> str:
        """
        Convert HTML content to plain text.

        Args:
            html_content: HTML content string

        Returns:
            str: Plain text content
        """
        if not html_content:
            return ""

        import re

        # Remove HTML tags
        text = re.sub(r"<[^>]+>", "", html_content)

        # Clean up excessive whitespace
        text = re.sub(r"\n\s*\n", "\n\n", text)
        text = re.sub(r"&[^;]+;", "", text)  # Remove HTML entities
        text = re.sub(r"\s+", " ", text)  # Normalize whitespace

        return text.strip()


# Global instance
atlassian_service = AtlassianService()
