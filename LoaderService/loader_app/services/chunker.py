"""Utility module for splitting documents into smaller, context-preserving chunks.

Strategies
----------
Four strategies are supported:

* **recursive** – `RecursiveCharacterTextSplitter`
  - Recursively breaks the text on a list of separators until every piece fits
    ``chunk_size``.
  - Adds ``chunk_overlap`` **characters** between neighbouring chunks so
    cross-sentence references are not lost.
  - General-purpose; works well on mixed prose or code.

* **token** – `TokenTextSplitter`
  - Splits by *model tokens* rather than characters.
  - Guarantees predictable embedding cost even in multilingual corpora.
  - Pick when cost control or language diversity matters.

* **headline** – `HeadlineTextSplitter` (custom)
  - First pass splits on Markdown headings (``# …`` – ``###### …``).
  - If a section remains > ``chunk_size``, it is *re-split once more* **without**
    internal overlap—avoids "double overlap".
  - Adds a single, consistent ``chunk_overlap`` between *top-level sections only*.
  - Ideal for documentation, notebooks or blog posts already organised by
    headings.

* **markdown** – `MarkdownHeaderTextSplitter`
  - Emits a new chunk at every Markdown heading.
  - No overlap or secondary splitting.
  - Use when you simply want whole sections untouched.

Default parameters
------------------
The table below shows the built-in defaults applied whenever the caller leaves
``chunk_size`` or ``chunk_overlap`` as ``None``.

+-----------+-------------+---------------+
| Strategy  | chunk_size  | chunk_overlap |
+===========+=============+===============+
| recursive | 1 800 chars | 150 chars     |
+-----------+-------------+---------------+
| token     | 512 tokens  | 50 tokens     |
+-----------+-------------+---------------+
| headline  | 2 100 chars | 200 chars     |
+-----------+-------------+---------------+
| markdown  |    None     | 0             |
+-----------+-------------+---------------+

``Enum`` :class:`~loader_app.enums.loader.ChunkingStrategy` provides type-safe
selection of these strategies.
"""

from __future__ import annotations

import re
from typing import Dict, Iterable, List, Sequence, cast

from langchain.text_splitter import (
    MarkdownHeaderTextSplitter,
    RecursiveCharacterTextSplitter,
    TokenTextSplitter,
)
from langchain_core.documents import Document
from loader_app.enums.loader import ChunkingStrategy
from loader_app.settings import settings

__all__ = ["Chunker"]

DEFAULTS: Dict[str, Dict[str, int | None]] = {
    ChunkingStrategy.RECURSIVE.value: {"size": 1800, "overlap": 150},
    ChunkingStrategy.TOKEN.value: {"size": 512, "overlap": 50},
    ChunkingStrategy.HEADLINE.value: {"size": 2100, "overlap": 200},
    ChunkingStrategy.MARKDOWN.value: {"size": None, "overlap": 0},
}

# -----------------------------------------------------------------------------
# Headline splitter
# -----------------------------------------------------------------------------


class HeadlineTextSplitter:
    """Markdown‑headline splitter with single‑layer overlap preservation."""

    _headline_pattern = re.compile(r"(?m)(?=^#{1,6}\s)")

    def __init__(self, *, chunk_size: int | None = None, chunk_overlap: int = 50) -> None:
        self.chunk_size: int | None = chunk_size if chunk_size and chunk_size > 0 else None
        self.chunk_overlap: int = max(chunk_overlap, 0)
        # Secondary splitter **without** its own overlap to avoid double counting
        self._secondary = RecursiveCharacterTextSplitter(chunk_size=self.chunk_size, chunk_overlap=0) if self.chunk_size is not None else None

    # Public helpers -----------------------------------------------------------
    def split_documents(self, docs: Sequence[Document]) -> List[Document]:
        out: List[Document] = []
        for doc in docs:
            for chunk in self._split(doc.page_content):
                if chunk.strip():
                    out.append(Document(page_content=chunk, metadata=dict(doc.metadata)))
        return out

    def create_documents(self, texts: Sequence[str]) -> List[Document]:
        return [Document(page_content=chunk) for text in texts for chunk in self._split(text) if chunk.strip()]

    # Internal -----------------------------------------------------------------
    def _split(self, text: str) -> Iterable[str]:
        parts = self._headline_pattern.split(text)
        prev_chunk: str | None = None

        for part in parts:
            if not part:
                continue

            subparts = (
                self._secondary.split_text(part)  # type: ignore[attr-defined]
                if self._secondary is not None and self.chunk_size is not None and len(part) > self.chunk_size
                else [part]
            )
            for i, sub in enumerate(subparts):
                if i == 0 and prev_chunk is not None and self.chunk_overlap:
                    yield prev_chunk[-self.chunk_overlap :] + sub
                else:
                    yield sub
            prev_chunk = subparts[-1]


# -----------------------------------------------------------------------------
# Main API
# -----------------------------------------------------------------------------


class Chunker:
    """Flexible, strategy‑driven chunker wrapper around multiple splitters."""

    def __init__(
        self,
        *,
        strategy: str = settings.chunking_strategy,
        chunk_size: int | None = None,
        chunk_overlap: int | None = None,
        separators: list[str] | None = None,
    ) -> None:
        """Initialize the chunker with a specific strategy and parameters."""
        self.strategy = strategy
        defaults: Dict[str, int | None] = DEFAULTS.get(strategy, {})
        chunk_size = chunk_size if chunk_size is not None else defaults.get("size")
        chunk_overlap = chunk_overlap if chunk_overlap is not None else defaults.get("overlap", 0)

        if strategy == ChunkingStrategy.RECURSIVE.value:
            self.splitter = RecursiveCharacterTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap,
                separators=separators or settings.separators,
            )
        elif strategy == ChunkingStrategy.TOKEN.value:
            self.splitter = TokenTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap,
            )
        elif strategy == ChunkingStrategy.HEADLINE.value:
            self.splitter = HeadlineTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=cast(int, chunk_overlap if chunk_overlap is not None else 0),
            )
        else:  # MARKDOWN
            self.splitter = MarkdownHeaderTextSplitter(
                headers_to_split_on=[
                    ("#", "h1"),
                    ("##", "h2"),
                    ("###", "h3"),
                    ("####", "h4"),
                    ("#####", "h5"),
                    ("######", "h6"),
                ]
            )

    # ------------------------------------------------------------------
    # Proxy helpers with runtime inspection
    # ------------------------------------------------------------------
    def _wrap_split_text(self, chunks, metadata=None):
        """Ensure *chunks* is a list of Document objects."""
        if not chunks:
            return []
        if isinstance(chunks[0], Document):
            return chunks  # type: ignore[return-value]
        if metadata is None:
            metadata = {}
        return [Document(page_content=str(chunk), metadata=dict(metadata)) for chunk in chunks if str(chunk).strip()]

    def split_documents(self, docs: List[Document]) -> List[Document]:
        """Split documents and preserve metadata."""
        if hasattr(self.splitter, "split_documents"):
            return self.splitter.split_documents(docs)  # type: ignore[attr-defined]

        out: List[Document] = []
        for doc in docs:
            chunks = self.splitter.split_text(doc.page_content)  # type: ignore[attr-defined]
            out.extend(self._wrap_split_text(chunks, metadata=doc.metadata))
        return out

    def create_documents(self, data: List[str]) -> List[Document]:
        """Create :class:`Document` objects from raw strings and split them."""
        if hasattr(self.splitter, "create_documents"):
            return self.splitter.create_documents(data)  # type: ignore[attr-defined]

        out: List[Document] = []
        for text in data:
            chunks = self.splitter.split_text(text)  # type: ignore[attr-defined]
            out.extend(self._wrap_split_text(chunks))
        return out
