"""
Role-based helpers for collection visibility.

This module provides:

* :class:`Role` – the three application roles (*basic*, *expert*, *admin*).
* :data:`PRIORITY` – numeric hierarchy (higher ⇒ more privileges).
* :func:`can_user_see` – runtime check for "may this user see this collection?"
* :func:`get_user_roles` – extract Azure-AD roles from the JWT with LOCAL_DEV support.
* :func:`get_entra_auth_user_info` – build the *user_info* payload for the API.

LOCAL_DEV Support:
* When LOCAL_DEV=True, users with 'dev_local' or 'admin_ui' roles automatically get 'admin' privileges.
* This enables local development without requiring proper Entra ID authentication.

Designed to run on Python ≥ 3.8.  No union-operator (`|`) or other 3.10-only
syntax is used.
"""

from __future__ import annotations  # postponed evaluation of annotations – safe on 3.8+

import logging
from datetime import datetime
from enum import StrEnum
from typing import Any, Dict, List, Optional, Set, Tuple

import pytz
from fastapi import Depends, HTTPException, status
from loader_app.services.auth import get_auth

logger = logging.getLogger(__name__)


# ---------------------------------------------------------------------------#
# Roles & hierarchy
# ---------------------------------------------------------------------------#
class Role(StrEnum):
    """Application-level roles (values are persisted as lower-case strings)."""

    BASIC = "basic"
    EXPERT = "expert"
    ADMIN = "admin"
    SERVICE_ACCESS = "service_access"  # for internal use only, not exposed to users

    # Helper: convert arbitrary input to a valid Role
    @classmethod
    def from_str(cls, value: Optional[str]) -> "Role":
        """Return a Role object, defaulting to :data:`Role.BASIC`.

        Maps SERVICE_ACCESS to EXPERT for effective role usage.
        """
        if value is None:
            return cls.BASIC
        try:
            role_value = value.lower()
            if role_value == cls.SERVICE_ACCESS:
                # Treat service_access as expert
                return cls.EXPERT
            return cls(role_value)
        except ValueError:
            logger.warning("Unknown role '%s' – treating as 'basic'", value)
            return cls.BASIC


#: Numeric hierarchy.  *Higher number ⇒ more privileges*.
PRIORITY: Dict[Role, int] = {
    Role.BASIC: 1,
    Role.EXPERT: 2,
    Role.ADMIN: 3,
}


def can_user_see(user_role: Optional[str], collection_role: Optional[str]) -> bool:
    """Return *True* if the user is allowed to see the collection.

    Rules
    -----
    * **service_access** → sees **everything** (internal/system use).
    * **admin**  → sees *admin*, *expert* and *basic* collections.
    * **expert** → sees *expert* and *basic*.
    * **basic**  → sees *basic* only.
    """
    if user_role.lower() == Role.SERVICE_ACCESS.value:
        return True

    u_level = PRIORITY[Role.from_str(user_role)]
    c_level = PRIORITY[Role.from_str(collection_role)]
    return u_level >= c_level



# ---------------------------------------------------------------------------#
# Azure AD helpers
# ---------------------------------------------------------------------------#
def get_user_roles(auth: Any) -> List[str]:
    """Extract role names from the Azure-AD JWT (always returns at least one)."""

    # LOCAL_DEV Support: Check for dev_local role
    if isinstance(auth, dict) and auth.get("role") == "dev_local":
        logger.info("LOCAL_DEV: Assigning admin role for dev_local user")
        return ["admin"]

    # Check for admin_ui role (AdminUI bypass)
    if isinstance(auth, dict) and auth.get("role") == "admin_ui":
        logger.info("LOCAL_DEV: Assigning admin role for admin_ui user")
        return ["admin"]

    try:
        raw_roles = auth.claims.get("roles")  # type: ignore[attr-defined]
    except Exception:
        logger.exception("Auth object lacks 'claims' or 'roles'; falling back to 'basic'")
        return ["basic"]

    if not raw_roles:  # None, empty list or empty string
        return ["basic"]

    # `roles` can be a single string or a list of strings
    if isinstance(raw_roles, str):
        raw_roles = [raw_roles]

    try:
        return [str(r).lower() for r in raw_roles]
    except Exception:  # defensive
        logger.exception("Failed to parse the 'roles' claim")
        return ["basic"]


def get_entra_auth_user_info(auth: Any) -> Tuple[Dict[str, Any], bool]:
    """Return a serialisable ``user_info`` dict **and** a success flag.

    The dict keys are::

        user_name : Display name from the token (or "Unknown")
        email     : Preferred username / e-mail (or "Unknown")
        roles     : List[str] – at least ``["basic"]``
        timestamp : float – Unix timestamp in Europe/Berlin TZ

    If anything goes wrong while reading the JWT we return an “Admin fallback”
    and the success flag is ``False``.
    """
    tz_berlin = pytz.timezone("Europe/Berlin")
    now_ts: float = datetime.now(tz_berlin).timestamp()

    try:
        info: Dict[str, Any] = {
            "user_name": auth.claims.get("name", "Unknown"),
            "email": auth.claims.get("preferred_username", "Unknown"),
            "roles": get_user_roles(auth),
            "timestamp": now_ts,
        }
        return info, True
    except Exception:
        fallback: Dict[str, Any] = {
            "user_name": "Admin",
            "email": "Admin",
            "roles": ["admin"],
            "timestamp": now_ts,
        }
        return fallback, False


def roles_required(allowed: Set[str]):
    """Fastapi dependency factory."""
    allowed_lc: Set[str] = {r.lower() for r in allowed}

    async def _checker(auth: Any = Depends(get_auth)) -> Any:
        user_roles_lc = {r.lower() for r in get_user_roles(auth)}

        if Role.SERVICE_ACCESS.value in user_roles_lc:
            return auth

        if user_roles_lc.isdisjoint(allowed_lc):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient privileges – allowed roles: " f"{', '.join(sorted(allowed_lc))}",
            )
        return auth

    return _checker
