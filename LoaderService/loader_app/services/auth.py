import logging
from typing import Any

from fastapi import Request, Security
from fastapi.security import SecurityScopes
from fastapi_azure_auth import SingleTenantAzureAuthorizationCodeBearer
from loader_app.settings import settings
from msal import ConfidentialClientApplication

logger = logging.getLogger(__name__)


class CustomSingleTenantAzureAuthorizationCodeBearer(SingleTenantAzureAuthorizationCodeBearer):
    """Custom Azure Authorization Code Bearer that allows for special handling"""

    async def __call__(self, request: Request, security_scopes: SecurityScopes):
        """Custom call method to handle special cases like admin UI or local development."""
        if getattr(request.state, "is_admin_ui", False):
            return {"role": "admin_ui", "user": "admin"}
        if settings.is_debug and not settings.is_entra_id_auth_on:
            return {"role": "dev_local", "user": "developer"}
        return await super().__call__(request, security_scopes)


azure_scheme = CustomSingleTenantAzureAuthorizationCodeBearer(
    app_client_id=settings.app_client_id,
    tenant_id=settings.tenant_id,
    scopes=settings.scopes,
    allow_guest_users=True,
)


async def get_auth(user: Any = Security(azure_scheme)):
    """Retrieve the authenticated user from the request."""
    return user


def get_token():
    """Acquire an access token for the Azure AD application using MSAL."""
    try:
        authority = f"https://login.microsoftonline.com/{settings.tenant_id}"
        scopes = [f"api://{settings.app_client_id}/.default"]

        app = ConfidentialClientApplication(
            client_id=settings.app_client_id,
            client_credential=settings.app_client_secret,
            authority=authority,
        )

        result = app.acquire_token_silent(scopes, account=None)
        if not result:
            result = app.acquire_token_for_client(scopes=scopes)

        if "access_token" in result:
            return result["access_token"]
        else:
            raise Exception(f"Failed to acquire token: {result.get('error_description')}")
    except Exception as e:
        logger.error(f"Error acquiring token: {e}")
    return None
