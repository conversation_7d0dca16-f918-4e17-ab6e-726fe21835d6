import io
import logging
import time
from typing import Any, Dict, List, Optional

import fitz  # PyMuPDF
import pytesseract
from bs4 import BeautifulSoup
from langchain_core.documents import Document
from loader_app.llm.models import DEFAULT_LLM_MODEL, MODEL_PRESETS
from PIL import Image, UnidentifiedImageError

logger = logging.getLogger(__name__)


class DocumentChecker:
    """A class to check documents for garbled characters and meaningful text."""

    def __init__(
        self,
        garbled_indicators: Optional[List[str]] = None,
        llm_model_name: str = DEFAULT_LLM_MODEL,
    ):
        """
        Initialize the DocumentChecker object.

        :param garbled_indicators: A list of indicators that signify garbled text. If None, the default list will be used.
        :param llm_model_name: The name of the language model to use for checking text meaningfulness.
        """
        self._chat_model = MODEL_PRESETS[llm_model_name].chat_model
        self.garbled_indicators = garbled_indicators or []

    def contains_garbled_characters(self, idx, text: str) -> bool:
        """
        Check if the given text contains any garbled characters.

        :param idx: The index of the document page.
        :param text: The text to be checked.
        :return: True if garbled characters are found, False otherwise.
        """
        logger.info(f"Page {idx + 1} is under check to contains garbled characters.")
        is_contain = any(char in text for char in self.garbled_indicators)
        logger.info(f"Contains garbled characters: {is_contain}")
        return is_contain

    def is_text_meaningful(self, idx, text: str) -> bool:
        """
        Check if the given text is meaningful using a language model.

        :param idx: The index of the document page.
        :param text: The text to be checked.
        :return: True if the text is meaningful, False otherwise.
        """
        logger.info(f"Page {idx + 1} does not contain garbled characters. Checking with LLM if there is any nonsense text.")
        prompt = f"Is the following text meaningful?\n\n{text}\n\nAnswer with yes or no."

        try:
            response = self._chat_model.invoke(prompt)
            answer = response.content.strip().lower()
            logger.info(f"LLM Answer for 'Is the following text meaningful?': {answer}")
            return "yes" in answer
        except Exception as e:
            logger.error(f"Error in checking text meaningfulness: {e}")
            return False

    def check_documents(self, documents: List[Document]) -> List[Document]:
        """
        Check a list of documents for garbled characters and meaningfulness.
        """
        results = []
        for idx, doc in enumerate(documents):
            page_content = doc.page_content
            source_path = doc.metadata.get("source") or doc.metadata.get("file_path") or "unknown_source"
            if self.contains_garbled_characters(idx, page_content):
                results.append(self.process_page(page_num=idx, file_path=source_path))
            elif not self.is_text_meaningful(idx, page_content):
                results.append(self.process_page(page_num=idx, file_path=source_path))
            else:
                results.append(doc)
        return results

    @staticmethod
    def extract_images_and_graphics(file_path: str):
        """
        Extract pages containing images and vector graphics from the PDF.

        :return: A list of page numbers that contain images or vector graphics.
        """
        image_and_graphic_pages = []
        document = fitz.open(file_path)

        for page_num in range(len(document)):
            page = document.load_page(page_num)
            images = page.get_images(full=True)
            graphics = page.get_drawings()  # Method to get vector graphics

            if images or graphics:
                image_and_graphic_pages.append(page_num)  # Page numbers start from 0

        return image_and_graphic_pages

    @staticmethod
    def extract_text_and_images(file_path: str):
        """
        Extract text and images from each page of the PDF.

        :return: A tuple containing two lists:
                 - text_pages: A list of tuples with page number and extracted text.
                 - image_pages: A list of tuples with page number and extracted images as bytes.
        """
        document = fitz.open(file_path)
        text_pages = []
        image_pages = []

        for page_num in range(len(document)):
            page = document.load_page(page_num)
            text = page.get_text("text")
            images = page.get_images(full=True)
            text_pages.append((page_num, text))

            if images:
                image_list = []
                for img in images:
                    xref = img[0]
                    base_image = document.extract_image(xref)
                    image_bytes = base_image["image"]
                    image_list.append(image_bytes)
                image_pages.append((page_num, image_list))

        return text_pages, image_pages

    @staticmethod
    def extract_text_from_image(image: Image.Image) -> str:
        """Extract text from an image using OCR.

        Args:
            image (Image.Image): The image from which to extract text.

        Returns:
            str: Extracted text.
        """
        return pytesseract.image_to_string(image)

    def process_page(self, page_num: int, file_path: str) -> Document:
        """Process a single page of a PDF.

        Args:
            page_num (int): The page number to process.
            file_path (str): The path to the PDF file.

        Returns:
            Document: Processed document with text and OCR text from images.
        """
        start_time = time.time()
        doc = None
        try:
            logger.info(f"Processing page {page_num} of {file_path}")
            doc = fitz.open(file_path)
            page = doc.load_page(page_num)
            text = page.get_text("text")
            metadata_json = page.get_text("json")
            images = page.get_images(full=True)
            for img in images:
                xref = img[0]
                base_image = page.parent.extract_image(xref)
                image_bytes = base_image["image"]
                try:
                    image = Image.open(io.BytesIO(image_bytes))
                    text += "\n" + self.extract_text_from_image(image)
                except UnidentifiedImageError:
                    logger.error(f"Cannot identify image file for image xref {xref} in PDF")
                    continue
                except Exception as e:
                    logger.error(f"Error extracting text from image xref {xref} in PDF: {e}")
                    continue
            end_time = time.time()
            processing_time_minutes = (end_time - start_time) / 60
            logger.info(f"Processed page {page_num} of {file_path} in {processing_time_minutes:.2f} minutes")
            return Document(
                page_content=text,
                metadata={
                    "page_num": page_num,
                    "metadata": metadata_json,
                    "file_path": file_path,
                },
            )
        except Exception as e:
            logger.error(f"Error processing page {page_num} of {file_path}: {e}")
            return Document(page_content="", metadata={"page_num": page_num, "error": str(e)})
        finally:
            if doc:
                doc.close()

    @staticmethod
    def clean_html(raw_html: str) -> str:
        """
        Remove HTML tags from a string using BeautifulSoup.
        """
        if not raw_html:
            return ""
        soup = BeautifulSoup(raw_html, "html.parser")
        return soup.get_text()

    def extract_content_from_json(self, json_data: List[Dict[str, Any]]) -> List[Document]:
        """
        Convert a JSON array into a list of Document objects.
        """
        documents = []
        for item in json_data:
            title = item.get("title", "Untitled")
            intro_text = self.clean_html(item.get("introText", ""))
            detail_text = self.clean_html(item.get("detailText", ""))
            document_content = f"{title}\n{intro_text}\n{detail_text}\n"

            answers = item.get("answers", [])
            for answer in answers:
                if answer and "content" in answer:
                    answer_content = answer["content"]
                    if answer_content:
                        answer_title = answer_content.get("title", "")
                        answer_intro = self.clean_html(answer_content.get("introText", ""))
                        answer_detail = self.clean_html(answer_content.get("detailText", ""))
                        document_content += f"{answer_title}\n{answer_intro}\n{answer_detail}\n"

            documents.append(Document(page_content=document_content, metadata={"title": title}))

        return documents
