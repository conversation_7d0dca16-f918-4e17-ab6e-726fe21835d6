# type: ignore
from __future__ import annotations

import time
from collections import defaultdict
from contextlib import AbstractContextManager
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union
from zoneinfo import ZoneInfo

import redis
import tiktoken

# from langchain_core.callbacks import UsageMetadataCallbackHandler  # unused here
from loader_app.llm.enums import (
    OPENAI_EMBEDDING_MODELS,
    OPENAI_MODELS,
    OS_EMBEDDING_MODELS,
    OS_MODELS,
)
from loader_app.settings import settings

Price = Tuple[float, float]  # (input_price, output_price)

# Price = (input_usd_per_1M_tokens, output_usd_per_1M_tokens)
AZURE_OPENAI_PRICES_PER_MILLION: Dict[str, Price] = {
    # Classic GPT-4 (8K context). Announced by Microsoft at launch.
    "gpt-4": (30.0, 60.0),
    # GPT-4.1 series (Azure generally mirrors OpenAI list pricing).
    # If you deploy "gpt-4.1-2025-04-14" on Azure, use these.
    "gpt-4.1": (2.0, 8.0),
    # GPT-3.5 on Azure (aka gpt-35-turbo).
    "gpt-35-turbo": (1.5, 2.0),
    # GPT-4o v2 naming on Azure (e.g., deployments called "gpt-4o-2").
    # Azure docs and community threads show the same rate as 4o unless stated otherwise.
    "gpt-4o-2": (2.5, 10.0),
    # GPT-4o on Azure (text tokens).
    "gpt-4o": (2.5, 10.0),
    # GPT-4o mini on Azure (text tokens).
    "gpt-4o-mini": (0.15, 0.60),
}

tiktoken_model_map = {"gpt-4": "gpt-4", "gpt-4.1": "gpt-4", "gpt-4o": "gpt-4o", "gpt-4o-2": "gpt-4o", "gpt-4o-mini": "gpt-4o-mini"}  # Use gpt-4 tokenizer


def _ensure_list(value: Optional[Union[str, List[str]]]) -> List[str]:
    """Convert input to list format."""
    if value is None:
        return ["*"]
    if isinstance(value, str):
        return [value]
    return value


def _glob_escape(pattern: str) -> str:
    """Escape glob special characters for exact match."""
    return pattern.replace("*", r"\*").replace("?", r"\?").replace("[", r"\[").replace("]", r"\]")


class TokenLimiter(object):
    """TokenLimiter class to manage token usage and limits."""

    def __init__(self) -> None:
        """Initialize token limiter."""
        self.expire_interval_seconds = 30 * 24 * 60 * 60  # max ttl (30 days)
        self.redis_conn = redis.Redis(settings.redis_host, port=settings.redis_port, db=settings.dramatiq_redis_db)
        self.response_token_buffer = 2500

    def _get_timestamp(self) -> int:
        """Get timestamp."""
        return int(time.time())

    def store_token_usage(
        self,
        tokens_used: int,
        cost: Optional[float] = None,
        user_email: Optional[str] = None,
        llm_model: Optional[str] = None,
    ) -> None:
        """
        token_usage:{ts}:{email}:{model} -> tokens
        token_usage_cost:{ts}:{email}:{model} -> cost
        """
        ts = self._get_timestamp()
        email = user_email or "unknown"
        model = llm_model or "unknown"

        pipe = self.redis_conn.pipeline()
        pipe.setex(f"token_usage:{ts}:{email}:{model}", self.expire_interval_seconds, tokens_used)
        if cost is not None:  # keep 0.0 as well
            pipe.setex(f"token_usage_cost:{ts}:{email}:{model}", self.expire_interval_seconds, cost)
        pipe.execute()

    def get_token_usage(
        self,
        interval_seconds: int = -1,
        is_cost: bool = False,
        user_emails: Optional[Union[str, List[str]]] = None,
        llm_models: Optional[Union[str, List[str]]] = None,
        breakdown: bool = False,
        group_by: str = "day",  # "day" | "hour"
        tz: str = "Europe/London",
        split_by: Optional[List[str]] = None,  # e.g. ["email"], ["model"], ["email","model"]
    ) -> Union[float, List[Tuple[str, float]], List[Dict[str, Any]]]:
        """
        Aggregate usage/cost. By default returns a total float.
        If breakdown=True:
          - without split_by -> List[(bucket, value)]
          - with split_by -> List[{"bucket", ("email"), ("model"), "value"}]
        Filters:
          - user_emails: str or List[str], None -> all
          - llm_models: str or List[str], None -> all
        """
        prefix = "token_usage_cost" if is_cost else "token_usage"

        emails = _ensure_list(user_emails)
        models = _ensure_list(llm_models)

        now = self._get_timestamp()
        start_ts = 0 if interval_seconds == -1 else now - int(interval_seconds)
        tzinfo = ZoneInfo(tz)

        # Build patterns for each (email, model) filter; escape glob chars for exact match
        patterns: List[str] = []
        for e in emails:
            e_pat = "*" if e == "*" else _glob_escape(e)
            for m in models:
                m_pat = "*" if m == "*" else _glob_escape(m)
                patterns.append(f"{prefix}:*:{e_pat}:{m_pat}")

        # Collect keys with dedupe and time-window filter
        key_ts: Dict[str, int] = {}
        for pattern in patterns:
            for raw_key in self.redis_conn.scan_iter(match=pattern, count=1000):
                key = raw_key.decode() if isinstance(raw_key, bytes) else raw_key
                parts = key.split(":")
                # schema: [prefix, ts, email, model]
                if len(parts) < 4:
                    continue
                try:
                    ts = int(parts[1])
                except ValueError:
                    continue
                if ts >= start_ts:
                    key_ts[key] = ts

        if not key_ts:
            if breakdown:
                return [] if split_by else []
            return 0.0

        # Bulk GET values
        pipe = self.redis_conn.pipeline()
        for k in key_ts.keys():
            pipe.get(k)
        values = pipe.execute()

        if not breakdown:
            total = 0.0
            for v in values:
                if v is None:
                    continue
                if isinstance(v, bytes):
                    v = v.decode()
                try:
                    total += float(v)
                except (TypeError, ValueError):
                    pass
            return total

        # ---- Build buckets ----
        group_by = group_by.lower()
        do_split_email = split_by and "email" in split_by
        do_split_model = split_by and "model" in split_by

        # Accumulator:
        #   if split_by -> key = (bucket, email?, model?)
        #   else        -> key = (bucket,)
        buckets: Dict[tuple, float] = defaultdict(float)

        for (key, ts), val in zip(key_ts.items(), values):
            if val is None:
                continue
            if isinstance(val, bytes):
                val = val.decode()
            try:
                num = float(val)
            except (TypeError, ValueError):
                continue

            parts = key.split(":")
            email = parts[2] if len(parts) > 2 else ""
            model = parts[3] if len(parts) > 3 else ""

            dt = datetime.fromtimestamp(ts, tz=tzinfo)
            bucket = dt.strftime("%Y-%m-%d %H:00") if group_by == "hour" else dt.strftime("%Y-%m-%d")

            if do_split_email and do_split_model:
                k = (bucket, email, model)
            elif do_split_email:
                k = (bucket, email)
            elif do_split_model:
                k = (bucket, model)
            else:
                k = (bucket,)

            buckets[k] += num

        # Format output
        if not split_by:
            # List[(bucket, value)]
            return sorted([(k[0], v) for k, v in buckets.items()], key=lambda x: x[0])

        # List[dict] with columns depending on split_by
        rows: List[Dict[str, Any]] = []
        for k, v in buckets.items():
            row: Dict[str, Any] = {"bucket": k[0], "value": v}
            if do_split_email and do_split_model:
                row["email"] = k[1]
                row["model"] = k[2]
            elif do_split_email:
                row["email"] = k[1]
            elif do_split_model:
                row["model"] = k[1]
            rows.append(row)

        # Sort by bucket, then email/model lexicographically if present
        def sort_key(r: Dict[str, Any]):
            return (
                r["bucket"],
                r.get("email", ""),
                r.get("model", ""),
            )

        rows.sort(key=sort_key)
        return rows

    def get_token_usage_cost(
        self,
        interval_seconds: int = -1,
        user_emails: Optional[Union[str, List[str]]] = None,
        llm_models: Optional[Union[str, List[str]]] = None,
        breakdown: bool = False,
        group_by: str = "day",
        tz: str = "Europe/London",
        split_by: Optional[List[str]] = None,
    ) -> Union[float, List[Tuple[str, float]], List[Dict[str, Any]]]:
        """Get token usage cost - wrapper around get_token_usage with is_cost=True."""
        return self.get_token_usage(
            interval_seconds=interval_seconds,
            is_cost=True,
            user_emails=user_emails,
            llm_models=llm_models,
            breakdown=breakdown,
            group_by=group_by,
            tz=tz,
            split_by=split_by,
        )

    @staticmethod
    def get_token_limit(model_name: str):
        """Get the token limit for a specific model."""
        TOKEN_LIMITS = {
            OPENAI_EMBEDDING_MODELS.ADA002.value: 2048,  # ADA-002
            OPENAI_MODELS.GPT4.value: 8192,  # GPT-4
            OPENAI_MODELS.GPT4O2.value: 32768,  # GPT-4 O2
            OS_MODELS.LLAMA270CHATHF.value: 4096,  # Llama 2 70B
            OS_MODELS.LLAMA370CHATHF.value: 128000,  # Llama 3.1 70B
            OS_MODELS.LLAMA3170BINSTRUCT.value: 128000,  # Llama 3.1 70B
            OS_MODELS.MIXTRAL8B.value: 32768,  # Mixtral 8x7B
            OS_MODELS.PHI4.value: 8000,  # Phi-3 Small
            OS_EMBEDDING_MODELS.BGEM3.value: 8192,  # BGE-M3
        }
        limit = TOKEN_LIMITS.get(model_name)
        if limit:
            return limit
        return None

    @staticmethod
    def calculate_tokens(text: str, model_name: str) -> int:
        """Calculate the number of tokens in a given text for a specific model."""
        encoding = tiktoken.encoding_for_model(model_name)
        return len(encoding.encode(text))

    @staticmethod
    def trim_previous_messages(text: str, max_tokens: int, model="gpt-4o") -> str:
        """Trim the previous messages to fit within the token limit for a specific model."""
        try:
            encoding = tiktoken.encoding_for_model(model)
        except Exception as e:
            encoding = None

        if not encoding:
            try:
                encoding = tiktoken.get_encoding("cl100k_base")
            except Exception as e:
                print(f"Failed to get encoding for model: {model}. Error: {str(e)}")
                encoding = None

        if not encoding:
            raise ValueError(f"Failed to get encoding for model: {model}")

        encoded_messages = encoding.encode(text)
        if len(encoded_messages) <= max_tokens:
            return text
        trimmed_encoded = encoded_messages[:max_tokens]
        trimmed_str = encoding.decode(trimmed_encoded)
        return trimmed_str

    def prepare_token_safe_question(
        self,
        user_question: str,
        previous_messages: str,
        model_name: str,
        safety_margin=100,
    ) -> str:
        """Prepare a token-safe question by trimming previous messages if necessary."""
        max_token_limit = self.get_token_limit(model_name)
        combined_prompt = f"Vorherige Nachrichten:\n{previous_messages}\n\nNeue Frage:\n{user_question}"
        prompt_tokens = self.calculate_tokens(combined_prompt, model_name)
        total_usage = prompt_tokens + self.response_token_buffer
        if total_usage <= max_token_limit:
            return combined_prompt

        user_question_tokens = self.calculate_tokens(user_question, model_name)
        max_for_previous = max_token_limit - self.response_token_buffer - user_question_tokens - safety_margin
        if max_for_previous <= 0:
            return user_question

        trimmed_prev = self.trim_previous_messages(text=previous_messages, max_tokens=max_for_previous, model=model_name)

        final_prompt = f"Vorherige Nachrichten:\n{trimmed_prev}\n\nNeue Frage:\n{user_question}"
        while True:
            final_tokens = self.calculate_tokens(final_prompt, model_name)
            total_usage_final = final_tokens + self.response_token_buffer
            if total_usage_final <= max_token_limit:
                break
            overflow = total_usage_final - max_token_limit
            trimmed_prev = self.trim_previous_messages(trimmed_prev, max_for_previous - overflow, model=model_name)
            final_prompt = f"Vorherige Nachrichten:\n{trimmed_prev}\n\nNeue Frage:\n{user_question}"
            if not trimmed_prev.strip():
                final_prompt = user_question
                break

        return final_prompt


class StreamCostCalculator(AbstractContextManager):
    """
    Manual token counting and cost calculation for streaming mode.
    Uses tiktoken for accurate token counting since LangChain's UsageMetadataCallbackHandler
    doesn't work reliably with Azure OpenAI streaming.
    """

    def __init__(
        self,
        model_name: str,
        prices: Dict[str, Price],
        *,
        prices_per_million: bool = True,
    ):
        """Initialize the calculator.

        Args:
            model_name: Logical model key used for price lookup (e.g. "gpt-4o").
            prices: Mapping of model_name → (input_price_per_1M, output_price_per_1M).
            prices_per_million: If True, prices are per 1,000,000 tokens; otherwise per token.
        """
        self.model_name = model_name
        self.prices = prices
        self.prices_per_million = prices_per_million

        # Tiktoken model mapping
        self.tiktoken_model_map = {"gpt-4": "gpt-4", "gpt-4.1": "gpt-4", "gpt-4o": "gpt-4o", "gpt-4o-2": "gpt-4o", "gpt-4o-mini": "gpt-4o-mini"}  # Use gpt-4 tokenizer

        self.total_input_tokens = 0
        self.total_output_tokens = 0
        self.total_tokens = 0
        self.total_cost = 0.0
        self.input_text = ""
        self.output_text = ""

        # Initialize tiktoken encoding
        self.encoding = None
        self._init_encoding()

    def _init_encoding(self):
        """Initialize tiktoken encoding for the model."""
        try:
            import tiktoken

            tiktoken_model = self.tiktoken_model_map.get(self.model_name, "gpt-4")
            self.encoding = tiktoken.encoding_for_model(tiktoken_model)
            print(f"🔢 StreamCostCalculator: Initialized tiktoken for model: {self.model_name} -> {tiktoken_model}")
        except Exception as e:
            print(f"❌ StreamCostCalculator: Tiktoken initialization error: {e}")
            self.encoding = None

    def set_input_text(self, text: str):
        """Set the input text for token counting."""
        self.input_text = text
        if self.encoding:
            self.total_input_tokens = len(self.encoding.encode(text))
            print(f"🔢 StreamCostCalculator: Input tokens: {self.total_input_tokens}")
        else:
            self.total_input_tokens = 0

    def add_output_text(self, text: str):
        """Add output text chunk."""
        self.output_text += text

    def __enter__(self) -> "StreamCostCalculator":
        """Enter the context manager and return self."""
        print(f"📊 StreamCostCalculator: Starting manual calculation for model: {self.model_name}")
        return self

    def __exit__(self, exc_type, exc, tb) -> bool:
        """Finalize calculations on context exit and do not suppress exceptions."""
        self._finalize()
        return False  # do not suppress exceptions

    def _finalize(self) -> None:
        """Calculate final token counts and costs."""
        print("🔍 StreamCostCalculator: Finalizing calculations")

        # Calculate output tokens
        if self.encoding and self.output_text:
            self.total_output_tokens = len(self.encoding.encode(self.output_text))
        else:
            self.total_output_tokens = 0

        self.total_tokens = self.total_input_tokens + self.total_output_tokens

        print(f"🔢 StreamCostCalculator: Final token counts - input: {self.total_input_tokens}, output: {self.total_output_tokens}, total: {self.total_tokens}")

        # Calculate cost
        if self.model_name in self.prices:
            input_price, output_price = self.prices[self.model_name]

            if self.prices_per_million:
                self.total_cost = (self.total_input_tokens * input_price + self.total_output_tokens * output_price) / 1_000_000.0
                print(
                    f"💰 StreamCostCalculator: Cost calculation: ({self.total_input_tokens} * {input_price} + {self.total_output_tokens} * {output_price}) / 1M = {self.total_cost}"
                )
            else:
                self.total_cost = self.total_input_tokens * input_price + self.total_output_tokens * output_price
                print(f"💰 StreamCostCalculator: Cost calculation: ({self.total_input_tokens} * {input_price} + {self.total_output_tokens} * {output_price}) = {self.total_cost}")
        else:
            self.total_cost = 0.0
            print(f"❌ StreamCostCalculator: No price found for model: {self.model_name}")

        print(f"📊 StreamCostCalculator: Final results - tokens: {self.total_tokens}, cost: {self.total_cost}")
