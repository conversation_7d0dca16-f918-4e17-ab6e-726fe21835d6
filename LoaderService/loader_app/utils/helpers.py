import base64
import io
import logging
from mimetypes import guess_type
from urllib.parse import urlparse

from loader_app.settings import settings

logger = logging.getLogger(__name__)


def local_image_to_base64(image_path: str) -> tuple:
    """Convert a local image file to base64 encoding."""
    mime_type, _ = guess_type(image_path)
    if mime_type is None:
        mime_type = "application/octet-stream"

    with open(image_path, "rb") as image_file:
        base64_encoded_data = base64.b64encode(image_file.read()).decode("utf-8")

    return mime_type, base64_encoded_data


def image_to_base64(image) -> tuple:
    """Convert image bytes to base64 encoding."""
    mime_type = "image/png"
    buffered = io.BytesIO()
    image.save(buffered, format="PNG")
    base64_encoded_data = base64.b64encode(buffered.getvalue()).decode("utf-8")
    return mime_type, base64_encoded_data


def dramatiq_task_logger(task_name, rmq_message):
    """Log the details of a Dramatiq task."""
    logger.info(f"{task_name=} - Processing message_id={rmq_message.message_id}, queue={rmq_message.queue_name}, retried_so_far={rmq_message.options.get('retries', 0)}")


# Middleware to check for adminui requests
def extract_host_with_port_from_url(url: str) -> str:
    """
    Extract host:port from a URL.
    Example: "http://loader:8002" -> "loader:8002"
    """
    parsed = urlparse(url)
    return f"{parsed.hostname}:{parsed.port}"


def get_allowed_admin_hosts() -> set:
    """
    Extract host:port from the URLs in settings.
    Example: "http://loader:8002" -> "loader:8002"
    """
    return {
        extract_host_with_port_from_url(settings.indexer_base_url),
        extract_host_with_port_from_url(settings.loader_base_url),
        extract_host_with_port_from_url(settings.query_base_url),
    }
