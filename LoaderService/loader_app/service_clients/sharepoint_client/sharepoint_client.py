import logging
import os
import time
from typing import Any, Dict, Generator, List, Optional, Tuple, Union

import requests

DEFAULT_DELAY = 0.2

logger = logging.getLogger(__name__)

AZURE_TENANT_ID = os.getenv("AZURE_TENANT_ID")
SHAREPOINT_CLIENT_ID = os.getenv("SHAREPOINT_CLIENT_ID")
SHAREPOINT_CLIENT_SECRET = os.getenv("SHAREPOINT_CLIENT_SECRET")
SHAREPOINT_RESOURCE_URL = os.getenv("SHAREPOINT_RESOURCE_URL")


def ensure_sharepoint_envs_set() -> bool:
    """Ensure all required environment variables are set."""
    required_vars = ["AZURE_TENANT_ID", "SHAREPOINT_CLIENT_ID", "SHAREPOINT_CLIENT_SECRET", "SHAREPOINT_RESOURCE_URL"]
    for var in required_vars:
        if not os.getenv(var):
            return False
    return True


class SharePointClient:
    """A client for interacting with the Microsoft SharePoint API."""

    def __init__(self, tenant_id: str, client_id: str, client_secret: str, resource_url: str, request_delay: float = DEFAULT_DELAY):
        """
        Initializes a new instance of the class with the specified parameters.
        """
        self.tenant_id = tenant_id
        self.client_id = client_id
        self.client_secret = client_secret
        self.resource_url = resource_url
        self.base_url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token"
        self.headers = {"Content-Type": "application/x-www-form-urlencoded"}
        self.access_token = self.get_access_token()
        self.request_delay = request_delay

    def get_access_token(self) -> Optional[str]:
        """
        Retrieves an access token from an OAuth2.0 token endpoint.
        """
        body = {
            "grant_type": "client_credentials",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "scope": self.resource_url + ".default",
        }
        response = requests.post(self.base_url, headers=self.headers, data=body)
        logger.info(f"Access token response: {response.json().get('access_token')}")
        return response.json().get("access_token")

    def get_site_data(self) -> Dict[str, Any]:
        """
        Retrieves all SharePoint sites, including pagination.
        """
        all_sites = []
        next_link = "https://graph.microsoft.com/v1.0/sites"
        while next_link:
            response = requests.get(next_link, headers={"Authorization": f"Bearer {self.access_token}"})
            data = response.json()
            if "value" in data:
                all_sites.extend(data["value"])
            next_link = data.get("@odata.nextLink")
        return {"value": all_sites}

    def get_site_id_by_name(self, site_data: Dict[str, Any], target_name: str) -> Optional[str]:
        """
        Searches for the site ID based on the site name.
        """
        sites = site_data.get("value", [])
        for site in sites:
            if site.get("name") == target_name:
                return site.get("id")
        return None

    def get_drives(self, site_id: str) -> List[Dict[str, str]]:
        """
        Retrieves the drive IDs and names associated with a site.
        Returns a list of dicts with 'id' and 'name'.
        """
        drives_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drives"
        response = requests.get(drives_url, headers={"Authorization": f"Bearer {self.access_token}"})
        drives = response.json().get("value", [])
        return [{"id": drive["id"], "name": drive["name"]} for drive in drives]

    def get_folder_id_by_path(self, site_id: str, folder_path: str) -> Dict[str, Optional[str]]:
        """
        Find the folder id by a given path, returns dict {"drive_id": ..., "folder_id": ...}
        """
        path_parts = folder_path.strip("/").split("/")
        if not path_parts:
            logger.error("Empty folder path provided")
            return {"drive_id": None, "folder_id": None}

        root_drives = self.get_drives(site_id)
        root_drive_name = path_parts.pop(0)
        root_drive_id = None
        last_folder_id = None

        if root_drive_name in [drive["name"] for drive in root_drives]:
            for drive in root_drives:
                if drive["name"] == root_drive_name:
                    root_drive_id = drive["id"]
                    last_folder_id = root_drive_id
                    break
        if root_drive_id is not None:
            # Use a more general type annotation that can handle both return types
            current_folder_content: List[Dict[str, Any]] = self.get_drive_content(site_id, root_drive_id)
            for current_path in path_parts:
                if current_path in [item["name"] for item in current_folder_content]:
                    for item in current_folder_content:
                        if item["name"] == current_path:
                            folder_id = item["id"]
                            last_folder_id = folder_id
                            current_folder_content = self.get_folder_content(site_id, root_drive_id, folder_id)
        return {"drive_id": root_drive_id, "folder_id": last_folder_id}

    def get_drive_content(self, site_id: str, drive_id: str) -> List[Dict[str, str]]:
        """
        Retrieves the content of a drive root folder.
        Returns a list of dicts with 'id' and 'name'.
        """

        folder_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drives/{drive_id}/root/children"
        response = requests.get(folder_url, headers={"Authorization": f"Bearer {self.access_token}"})
        documents = response.json().get("value", [])
        return [{"id": item["id"], "name": item["name"]} for item in documents]

    def get_folder_content(self, site_id: str, drive_id: str, folder_id: str) -> List[Dict[str, Union[str, bool]]]:
        """
        Retrieves the content of a folder.
        Returns a list of dicts with 'id', 'name', and 'is_folder'.
        """
        folder_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drives/{drive_id}/items/{folder_id}/children"
        response = requests.get(folder_url, headers={"Authorization": f"Bearer {self.access_token}"})
        documents = response.json().get("value", [])

        return [{"id": item["id"], "name": item["name"], "is_folder": "folder" in item} for item in documents]

    def list_folder_contents(self, site_id: str, drive_id: str, folder_id: str, level: int = 0) -> List[Dict[str, Any]]:
        """
        Lists the contents of a specific folder recursively.
        Returns a list of items with names, types, and optionally MIME types and URLs.
        """
        folder_contents_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drives/{drive_id}/items/{folder_id}/children"
        contents_headers = {"Authorization": f"Bearer {self.access_token}"}
        contents_response = requests.get(folder_contents_url, headers=contents_headers)
        folder_contents = contents_response.json()
        items_list = []
        if "value" in folder_contents:
            for item in folder_contents["value"]:
                if "folder" in item:
                    items_list.extend(self.list_folder_contents(site_id, drive_id, item["id"], level + 1))
                elif "file" in item:
                    items_list.append(
                        {
                            "name": item["name"],
                            "type": "File",
                            "mimeType": item["file"]["mimeType"],
                            "url": item["@microsoft.graph.downloadUrl"],
                            "eTag": item["eTag"],
                            "lastModifiedDateTime": item["lastModifiedDateTime"],
                            "file_id": item["id"],
                        }
                    )
        return items_list

    def get_lists(self, site_id: str) -> List[Dict[str, str]]:
        """
        Retrieves all lists from a SharePoint site along with their IDs and names.
        Returns a list of dicts with 'id' and 'name'.
        """
        all_lists = []
        next_link = f"https://graph.microsoft.com/v1.0/sites/{site_id}/lists"
        try:
            while next_link:
                response = requests.get(next_link, headers={"Authorization": f"Bearer {self.access_token}"})
                response.raise_for_status()
                data = response.json()
                if "value" in data:
                    all_lists.extend([{"id": lst["id"], "name": lst["name"]} for lst in data["value"]])
                next_link = data.get("@odata.nextLink")
                if next_link:
                    time.sleep(self.request_delay)
            return all_lists
        except requests.exceptions.RequestException as e:
            return []

    def get_list_content(self, site_id: str, list_id: str) -> Union[List[Dict[str, Any]], str]:
        """
        Retrieves all items from a SharePoint list.
        Returns a list of dicts with 'id' and 'fields'.
        """
        try:
            contents: List[Dict[str, Any]] = []
            items_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/lists/{list_id}/items?$expand=fields"
            response = requests.get(items_url, headers={"Authorization": f"Bearer {self.access_token}"})
            response.raise_for_status()
            items = response.json().get("value", [])
            for item in items:
                item_data = {"id": item.get("id"), "fields": item.get("fields", {})}
                contents.append(item_data)
            return contents
        except requests.exceptions.RequestException as e:
            return f"Error: {str(e)}"

    def extract_sharepoint_names(self, site_data: Dict[str, Any]) -> List[str]:
        """
        Extrahiert die Namen der SharePoint-Sites, die nicht auf der Blacklist stehen.
        """
        matching_names = []
        sites = site_data.get("value", [])
        blacklist = ["Apps", "All Company", "Team Site", "", "Communication site"]
        for site in sites:
            if not site.get("isPersonalSite", False) and site.get("displayName", site.get("name", "")) not in blacklist:
                name = site.get("displayName", site.get("name", ""))
                matching_names.append(name)
        return matching_names

    # ------------------------------------Website Integration------------------------------------
    def get_site_pages_id(self, site_id: str) -> List[Dict[str, str]]:
        """
        Retrieves the IDs and names of pages in a SharePoint site.
        Returns a list of dicts with 'id' and 'name'.
        """
        site_pages_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/pages"
        response = requests.get(site_pages_url, headers={"Authorization": f"Bearer {self.access_token}"})
        site_pages = response.json().get("value", [])
        return [{"id": page["id"], "name": page["name"]} for page in site_pages]

    def yield_all_pages(self, site_id: str) -> Generator[Tuple[str, Dict[str, Any]], None, None]:
        """
        Yields all pages from a SharePoint site as JSON objects.
        """
        pages = self.get_site_pages_id(site_id)
        for page in pages:
            time.sleep(self.request_delay)
            content = self.get_page_content(site_id, page["id"], page["name"])
            if content:
                yield page["name"], content

    def get_page_content(self, site_id: str, page_id: str, page_name: str) -> Optional[Dict[str, Any]]:
        """
        Retrieves a SharePoint page content using the beta API with canvas layout.
        """
        page_url = f"https://graph.microsoft.com/beta/sites/{site_id}/pages/{page_id}/microsoft.graph.sitePage?$expand=canvasLayout"
        response = requests.get(page_url, headers={"Authorization": f"Bearer {self.access_token}"})
        if response.status_code == 200:
            full_content = response.json()
            web_url = full_content.get("webUrl")
            if web_url:
                try:
                    web_response = requests.get(web_url, headers={"Authorization": f"Bearer {self.access_token}"})
                    if web_response.status_code == 200:
                        full_content["webContent"] = web_response.text
                except Exception as e:
                    full_content["webContent"] = f"Failed to fetch web content: {str(e)}"
            return full_content
        logger.warning(f"Failed to fetch page content for {page_name}. Status code: {response.status_code}")
        return None

    async def check_connection(self) -> bool:
        """
        Checks the connection to the SharePoint API with a test request.
        """
        if not self.access_token:
            logger.error("Connection check failed: No access token available.")
            return False
        test_url = f"{self.resource_url}/v1.0/sites/root"
        try:
            response = requests.get(test_url, headers={"Authorization": f"Bearer {self.access_token}"})
            if response.status_code == 200:
                logger.info("Connection to SharePoint successfully established.")
                return True
            else:
                logger.warning(f"Connection check to SharePoint failed. Status: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"Error checking connection to SharePoint: {e}")
            return False


def get_client() -> Optional[SharePointClient]:
    """
    Create and return a SharePoint client instance.
    Args:
        SHAREPOINT_CLIENT_ID: The client ID for SharePoint authentication
        SHAREPOINT_CLIENT_SECRET: The client secret for SharePoint authentication
        AZURE_TENANT_ID: The tenant id for the SharePoint site
        SHAREPOINT_RESOURCE_URL: name of the resource (mostly the graph resource)

    Returns:
        SharePointClient: A configured client instance or None if environment variables are missing
    """
    tenant_id = os.getenv("AZURE_TENANT_ID", "")
    client_id = os.getenv("SHAREPOINT_CLIENT_ID", "")
    client_secret = os.getenv("SHAREPOINT_CLIENT_SECRET", "")
    resource_url = os.getenv("SHAREPOINT_RESOURCE_URL", "")

    # Check if any of the required variables are empty strings
    if not tenant_id or not client_id or not client_secret or not resource_url:
        logger.error("Missing required SharePoint environment variables")
        return None

    return SharePointClient(
        tenant_id,
        client_id,
        client_secret,
        resource_url,
        request_delay=0.2,
    )
