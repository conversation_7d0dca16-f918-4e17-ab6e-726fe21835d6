from typing import Optional

import httpx
from fastapi import HTTPException
from loader_app.settings import settings


async def rename_collection_request(
    collection_id: str,
    new_name: str,
    headers: Optional[dict] = None,
) -> bool:
    """Renames an embedding collection by sending a PUT request to the indexer service."""
    url = f"{settings.indexer_base_url}/embedding/collection/{collection_id}/rename"
    payload = {"new_name": new_name}

    async with httpx.AsyncClient() as client:
        try:
            response = await client.put(url, json=payload, headers=headers)
            if response.status_code == 200:
                return True
            return False
        except httpx.HTTPError as e:
            raise HTTPException(status_code=500, detail=str(e))
