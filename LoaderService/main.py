import asyncio
import logging
import os

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Request, Security, status
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi_azure_auth import SingleTenantAzureAuthorizationCodeBearer
from health_check import SKIP_CHEC<PERSON>, check_postgres, check_rabbit, check_redis
from loader_app.api.endpoints import (
    atlassian,
    collection,
    document,
    sharepoint,
    token_apis,
    url,
)
from loader_app.database import Base, engine
from loader_app.service_clients.sharepoint_client.sharepoint_client import (
    ensure_sharepoint_envs_set,
)
from loader_app.settings import settings
from loader_app.utils.helpers import get_allowed_admin_hosts
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint

# ─────────────────────────────────────────────────────────────
# Config & helpers
# ─────────────────────────────────────────────────────────────
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

azure_scheme = SingleTenantAzureAuthorizationCodeBearer(
    app_client_id=settings.app_client_id,
    tenant_id=settings.tenant_id,
    scopes=settings.scopes,
    allow_guest_users=True,
)


# ─────────────────────────────────────────────────────────────
# App
# ─────────────────────────────────────────────────────────────
app = FastAPI(
    swagger_ui_oauth2_redirect_url="/oauth2-redirect",
    swagger_ui_init_oauth={
        "usePkceWithAuthorizationCodeGrant": True,
        "clientId": settings.openapi_client_id,
        "scopes": settings.scope_name,
    },
)

# ─────────────────────────────────────────────────────────────
# Middlewares
# ─────────────────────────────────────────────────────────────
# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Middleware to check for adminui requests
class AdminUIMiddleware(BaseHTTPMiddleware):
    """Middleware to check if the request is from the admin UI."""

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint):
        """Dispatch method to check if the request is from the admin UI."""
        admin_ui_header = request.headers.get("x-admin-ui", "")
        host = request.headers.get("host", "")
        if admin_ui_header == "true" and host in get_allowed_admin_hosts():
            request.state.is_admin_ui = True
        else:
            request.state.is_admin_ui = False
        response = await call_next(request)
        return response


# Add middleware to check for adminui requests
app.add_middleware(AdminUIMiddleware)

# ─────────────────────────────────────────────────────────────
# Include routers
# ─────────────────────────────────────────────────────────────
app.include_router(collection.router, tags=["collection"])
app.include_router(document.router, tags=["document"])
app.include_router(url.router, tags=["url"])
app.include_router(token_apis.router, tags=["token"])
app.include_router(atlassian.router, prefix="/atlassian", tags=["atlassian"])

if ensure_sharepoint_envs_set():
    app.include_router(sharepoint.router, tags=["sharepoint"])


# ─────────────────────────────────────────────────────────────
# Health-check endpoints
# ─────────────────────────────────────────────────────────────
@app.get("/live", tags=["_internal"])
async def live() -> JSONResponse:
    """
    Kubernetes liveness probe
    We are alive if the service is running.
    This endpoint returns a 200 status code if the service is alive.
    Returns:
        JSONResponse: JSON response indicating the status of the service.
        200 OK if the service is alive.
    """
    return JSONResponse({"status": "alive"}, status_code=200)


@app.get("/ready", tags=["_internal"])
async def ready() -> JSONResponse:
    """
    Kubernetes readiness probe
    We are ready if all services are up and running.
    This endpoint checks the health of PostgreSQL, Redis, and RabbitMQ.
    If any of the services are down, it returns a 503 status code.
    If SKIP_CHECK is set to true, it returns a 200 status code with "skipped" message.
    Returns:
        JSONResponse: JSON response indicating the status of the service.
        200 OK if all services are healthy or SKIP_CHECK is true.
        503 Service Unavailable if any service is unhealthy.
    """
    if SKIP_CHECK:
        return JSONResponse({"status": "ready", "checks": "skipped"}, 200)

    checks = await asyncio.gather(check_postgres(), check_redis(), check_rabbit())
    all_ok = all(checks)
    status_code = 200 if all_ok else 503
    return JSONResponse(
        {
            "status": "ready" if all_ok else "not_ready",
            "postgres": checks[0],
            "redis": checks[1],
            "rabbitmq": checks[2],
        },
        status_code=status_code,
    )


# ─────────────────────────────────────────────────────────────
# Exception handlers
# ─────────────────────────────────────────────────────────────
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Exception handler for validation errors.

    Args:
        request: request.
        exc: raised error.

    Returns:
        JSONResponse: details about the validation error.
    """
    exc_str = f"{exc}".replace("\n", " ").replace("   ", " ")
    logging.error(f"{request}: {exc_str}")
    content = {"status_code": 422, "message": exc_str, "data": None}
    return JSONResponse(content=content, status_code=status.HTTP_422_UNPROCESSABLE_ENTITY)


# ─────────────────────────────────────────────────────────────
# Secure endpoint for testing the authentication
# ─────────────────────────────────────────────────────────────
@app.get("/secure-endpoint")
async def secure_endpoint(user=Security(azure_scheme)):
    """
    Secure endpoint that requires authentication.
    This endpoint is protected and requires the user to be authenticated
    using Azure AD. It returns a message along with the authenticated user details.
    Its usefull for testing the authentication especially while using swagger (/docs).
    Args:
        user: The authenticated user obtained from Azure AD.
    Returns:
        dict: A message indicating successful authentication and user details.
    """
    return {"message": "Hello, authenticated user!", "user": user}


@app.on_event("startup")
async def load_config() -> None:
    """
    Event handler to load Azure AD configuration on startup.
    This function loads the OpenID configuration (Swagger) for Azure AD when the application
    starts up. It ensures that the Azure AD scheme is properly configured.
    Returns:
        None
    """
    await azure_scheme.openid_config.load_config()


# ─────────────────────────────────────────────────────────────
# Database initialization
# ─────────────────────────────────────────────────────────────
@app.on_event("startup")
def startup_event():
    """Startup event to initialize the database."""
    with engine.begin() as conn:
        Base.metadata.create_all(bind=conn)


# ─────────────────────────────────────────────────────────────
# Main entry point
# ─────────────────────────────────────────────────────────────
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=int(os.getenv("API_PORT", 8002)))
