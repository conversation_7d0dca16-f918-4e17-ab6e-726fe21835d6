# Core Dependencies
wheel==0.45.1        # A package for building and distributing Python wheels (binary distributions).
setuptools==76.0.0   # A package management tool for installing, upgrading, and managing Python packages.

# Database and ORM
sqlalchemy==2.0.39            # SQL toolkit and ORM for Python, used for interacting with databases.
sqlalchemy-utils==0.41.2      # Additional utilities and extensions for SQLAlchemy, such as custom data types and validation.
databases[postgresql]==0.9.0  # Async database library for PostgreSQL, compatible with SQLAlchemy.
pgvector==0.3.6               # PostgreSQL extension for storing and querying vector embeddings.
redis==5.2.1                  # Redis client for Python, used for caching and real-time data processing.
psycopg[binary]==3.2.4        # PostgreSQL database adapter for Python, used to connect and interact with PostgreSQL.
alembic==1.13.1               # Database migration tool for SQLAlchemy.
asyncpg>=0.29                 # Asynchronous PostgreSQL database adapter for Python, used for high-performance database interactions.

# Web Frameworks and API
fastapi[all]==0.115.11         # High-performance web framework for building APIs with Python.
starlette==0.46.1              # Lightweight ASGI framework used by FastAPI for request handling.
uvicorn==0.34.0                # ASGI server for running FastAPI applications.
fastapi-azure-auth==5.1.0      # Azure AD authentication integration for FastAPI.
starlette-context==0.4.0       # Middleware for managing request-scoped context in Starlette/FastAPI.
requests==2.32.3               # Popular HTTP library for making API requests in Python.
httpx==0.28.1                  # Asynchronous HTTP client for Python, supporting HTTP/2 and connection pooling.
streamlit==1.43.0              # Web application framework for creating interactive data applications.

# Markdown and Web Scraping
markdown2==2.5.3           # Library for converting Markdown text to HTML.
beautifulsoup4==4.13.3     # HTML and XML parsing library for web scraping and data extraction.

# Logging, Background Tasks and Queuing
aiologger==0.7.0                 # Asynchronous logging library for Python.

# LangChain and AI-related Libraries
langchain==0.3.25               # Main LangChain package for building LLM-based applications.
langchain-openai==0.3.10        # OpenAI integration for LangChain, supporting LLMs and embeddings.
langchain-community==0.3.21     # Community modules for LangChain, including integrations with databases and vector stores.
langchain-core==0.3.58          # Core components of LangChain for chaining operations and data handling.
langchain-experimental==0.3.4   # Experimental features and early-stage integrations for LangChain.
langchain-postgres==0.0.14      # PostgreSQL integration for LangChain, enabling vector storage and retrieval.
openai==1.78.1                  # OpenAI API client for accessing GPT-4, GPT-3.5, DALL·E, and other services.
tiktoken==0.9.0                 # OpenAI's tokenizer library for counting tokens and estimating costs.
duckduckgo_search==8.0.2        # DuckDuckGo search API client for Python, used for web scraping and data retrieval.

# Retrieval and Re-ranking
rank_bm25==0.2.2                # Efficient BM25 implementation for Python.
sentence_transformers==2.7.0    # Library that provides cross-encoders for powerful re-ranking.
elasticsearch==8.16.0          # Official Elasticsearch client for Python.

# Azure Authentication (if needed for future integrations)
azure-identity>=1.15.0         # Azure authentication and identity management

# Testing and Code Quality
pytest==8.3.5                 # Testing framework for writing and executing unit tests in Python.
pytest-cov==6.0.0             # Pytest plugin for measuring test coverage using coverage.py.
pytest_mock==3.14.0           # Pytest plugin for easily using unittest.mock in tests.
pytest_postgresql==7.0.0      # Pytest plugin for setting up and managing PostgreSQL databases in tests.
ruff==0.9.10                  # Fast Python linter and formatter for code quality checks.
mypy==1.15.0                  # Static type checker for Python to enforce type annotations.
mypy-extensions==1.0.0        # Additional extensions and utilities for mypy type checking.
types-requests==2.32.0.20250306 # Type hints for the requests library, useful for mypy static analysis.

# Authentication and Security
msal                  # Microsoft Authentication Library for Python, used for Azure AD authentication.
pycryptodome==3.21.0 # Cryptographic library for Python, used for secure data handling.
