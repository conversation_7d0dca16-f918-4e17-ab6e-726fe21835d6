import logging
import uuid
from datetime import datetime
from typing import Any, Optional
from urllib.parse import urlparse

import markdown2
import pygments
import pytz
from bs4 import BeautifulSoup
from pygments.formatters.html import HtmlFormatter
from pygments.lexers import get_lexer_by_name, guess_lexer
from query_app.llm.enums import OPENAI_MODELS
from query_app.llm.models import MODEL_PRESETS
from query_app.settings import settings

logger = logging.getLogger(__name__)


def generate_uuid() -> str:
    """Generate uuid."""
    return str(uuid.uuid4())


def merge_sequential_ol_blocks(soup: BeautifulSoup):
    """Merge sequential <ol> blocks in a BeautifulSoup object."""
    try:
        all_ols = soup.find_all("ol")
        i = 0
        while i < len(all_ols) - 1:
            current = all_ols[i]
            next_ = all_ols[i + 1]
            if current.find_next_sibling() == next_:
                for li in next_.find_all("li", recursive=False):
                    current.append(li.extract())
                next_.decompose()
                all_ols = soup.find_all("ol")
            else:
                i += 1

        return soup
    except Exception as e:
        return soup


def markdown_to_html(markdown_content: str) -> str:
    """Converts Markdown to HTML, applies syntax highlighting, and adds .query-text-style class to all elements."""
    html_content = markdown2.markdown(
        markdown_content,
        extras=[
            "tables",
            "strike",
            "cuddled-lists",
            "spoiler",
            "nofollow",
            "target-blank",
            "header-ids",
        ],
    )

    soup = BeautifulSoup(html_content, "html.parser")

    for tag in soup.find_all(
        [
            "p",
            "h1",
            "h2",
            "h3",
            "ul",
            "ol",
            "li",
            "blockquote",
            "code",
            "pre",
            "a",
            "img",
        ]
    ):
        tag["class"] = tag.get("class", []) + ["query-text-style-2"]

    for pre_tag in soup.find_all("pre"):
        code_tag = pre_tag.find("code")
        if code_tag:
            lang = code_tag.get("class", ["text"])[0].replace("language-", "")
            code_text = code_tag.get_text()

            try:
                lexer = get_lexer_by_name(lang, stripall=True)
            except ValueError:
                lexer = guess_lexer(code_text)

            formatter = HtmlFormatter(cssclass="query-text-style-2")
            highlighted_code = pygments.highlight(code_text, lexer, formatter)
            pre_tag.replace_with(BeautifulSoup(highlighted_code, "html.parser"))

    soup = merge_sequential_ol_blocks(soup)
    return str(soup)


def get_entra_auth_user_info(auth: Any) -> Optional[tuple]:
    """Get the user info from the auth object."""

    # Get current time in Germany's timezone (Europe/Berlin)
    germany_timezone = pytz.timezone("Europe/Berlin")
    current_time_in_germany = datetime.now(germany_timezone)
    if isinstance(auth, dict):
        return {
            "user_name": "Admin",
            "email": "Admin",
            "timestamp": current_time_in_germany.timestamp(),
        }, False

    try:
        return {
            "user_name": auth.claims.get("name", "Unknown"),
            "email": auth.claims.get("preferred_username", "Unknown"),
            "timestamp": current_time_in_germany.timestamp(),
        }, True
    except Exception as e:
        logger.error("Error retrieving user info", exc_info=e)
        return {
            "user_name": "Admin",
            "email": "Admin",
            "timestamp": current_time_in_germany.timestamp(),
        }, False


def generate_session_title(query_content: str) -> str:
    """Generate a session title using ChatGPT with a maximum of 60 characters.

    Args:
        query_content (str): The query content for generating a title.

    Returns:
        str: Generated session title.
    """
    prompt = f'Generiere einen prägnanten und relevanten Titel (max. 60 Zeichen) für die folgende Frage: "{query_content}"'

    try:
        response = MODEL_PRESETS[OPENAI_MODELS.GPT4O.value].chat_model.invoke(prompt)
        title = response.content.strip()
        title = title.replace('"', "")
        if len(title) > 60:
            title = title[:57] + "..."
        return title
    except Exception as e:
        # Fallback title in case of an error
        return "Standard-Sitzungstitel"


def adjust_content_mapper(adjust_type: str, original_question: str, original_answer: Optional[str]) -> str:
    """
    Modify the previous answer based on adjust_type and return the updated content in German.
    """

    if not original_answer:
        return original_question

    adjustments = {
        "more_detail": "Lassen Sie uns dies ausführlicher erklären.",
        "less_detail": "Lassen Sie uns dies kürzer und allgemeiner halten. (Antwort wird um 50 % gekürzt.)",
        "shorter_answer": "Fassen Sie die vorherige Antwort zusammen. (Antwort wird um 50 % gekürzt.)",
        "longer_answer": "Geben Sie eine umfassendere Antwort mit mehr Details.",
    }

    prompt = adjustments.get(adjust_type, "Bitte antworten Sie erneut.")

    modified_prompt = f"Frage: {original_question}\n\n" f"Vorherige Antwort: {original_answer}\n\n" f"{prompt}"

    return modified_prompt


def dramatiq_task_logger(task_name, rmq_message):
    """Log the task name and message details for Dramatiq tasks."""
    logger.info(f"{task_name=} - Processing message_id={rmq_message.message_id}, queue={rmq_message.queue_name}, retried_so_far={rmq_message.options.get('retries', 0)}")


# Middleware to check for adminui requests
def extract_host_with_port_from_url(url: str) -> str:
    """
    Extract host:port from a URL.
    Example: "http://loader:8002" -> "loader:8002"
    """
    parsed = urlparse(url)
    return f"{parsed.hostname}:{parsed.port}"


def get_allowed_admin_hosts() -> set:
    """
    Extract host:port from the URLs in settings.
    Example: "http://loader:8002" -> "loader:8002"
    """
    return {
        extract_host_with_port_from_url(settings.indexer_base_url),
        extract_host_with_port_from_url(settings.loader_base_url),
        extract_host_with_port_from_url(settings.query_base_url),
    }
