from dataclasses import dataclass
from typing import Callable, Dict, List, Optional

from fastapi import HTT<PERSON>Exception


@dataclass
class SlashCommand:
    """Represents a slash command with its handler and metadata."""

    name: str
    handler: Callable
    description: str
    requires_args: bool = False
    arg_description: Optional[str] = None


class SlashCommandManager:
    """Manages slash commands and their execution."""

    def __init__(self) -> None:
        """Initialize the SlashCommandManager.

        Sets up an empty commands dictionary and registers the default commands
        that are available out of the box.
        """
        self.commands: Dict[str, SlashCommand] = {}
        self._register_default_commands()

    def register_command(self, command: SlashCommand) -> None:
        """Register a new slash command."""
        if command.name in self.commands:
            raise ValueError(f"Command /{command.name} already exists")
        self.commands[command.name] = command

    def remove_command(self, command_name: str) -> None:
        """Remove a slash command."""
        if command_name not in self.commands:
            raise ValueError(f"Command /{command_name} does not exist")
        del self.commands[command_name]

    def update_command(self, command: SlashCommand) -> None:
        """Update an existing slash command."""
        if command.name not in self.commands:
            raise ValueError(f"Command /{command.name} does not exist")
        self.commands[command.name] = command

    def get_command(self, name: str) -> Optional[SlashCommand]:
        """Get a command by name."""
        return self.commands.get(name)

    def get_all_commands(self) -> List[SlashCommand]:
        """Get all registered commands."""
        return list(self.commands.values())

    def execute_command(self, command_text: str) -> dict:
        """Execute a slash command from its text representation."""
        if not command_text.startswith("/"):
            raise ValueError("Command must start with /")

        # Split command and arguments
        parts = command_text[1:].strip().split(maxsplit=1)
        command_name = parts[0].lower()
        args = parts[1] if len(parts) > 1 else None

        # Get the command
        command = self.get_command(command_name)
        if not command:
            raise HTTPException(status_code=400, detail=f"Unknown command: /{command_name}. Use /help to see available commands.")

        # Check if command requires arguments
        if command.requires_args and not args:
            raise HTTPException(status_code=400, detail=f"Command /{command_name} requires arguments. {command.arg_description}")

        # Execute the command
        try:
            result = command.handler(args) if args else command.handler()
            return {"success": True, "command": command_name, "result": result}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error executing command /{command_name}: {str(e)}")

    def _register_default_commands(self) -> None:
        """Register default commands."""
        # Help command
        self.register_command(SlashCommand(name="help", handler=self._help_command, description="Show available commands and their descriptions"))

        # Clear command
        self.register_command(SlashCommand(name="clear", handler=self._clear_command, description="Clear the current chat history"))

        # Download chat command
        self.register_command(SlashCommand(name="download", handler=self._download_command, description="Download the current chat session", requires_args=False))

        # Websearch command
        self.register_command(
            SlashCommand(
                name="websearch", handler=self._websearch_command, description="Search the web for information", requires_args=True, arg_description="Usage: /websearch <query>"
            )
        )

    def _help_command(self) -> dict:
        """Handler for the /help command."""
        commands = self.get_all_commands()
        help_text = "Available commands:\n\n"
        for cmd in sorted(commands, key=lambda x: x.name):
            help_text += f"• /{cmd.name}: {cmd.description}"
            if cmd.requires_args:
                help_text += f"\n  {cmd.arg_description}"
            help_text += "\n\n"
        return {"message": help_text}

    def _clear_command(self) -> dict:
        """Handler for the /clear command."""
        return {"message": "Chat history cleared"}

    def _download_command(self) -> dict:
        """Handler for the /download command."""
        try:
            # Return a signal to the frontend to trigger download of current chat
            return {"message": "Chat download initiated", "action": "download_chat", "timestamp": "now"}
        except Exception as e:
            return {"message": f"Error initiating download: {str(e)}"}

    def _websearch_command(self, query: str) -> dict:
        """Handler for the /websearch command."""
        try:
            from query_app.services.azure_data_web_service import AzureDataWebService
            from query_app.settings import settings

            if not query or not query.strip():
                return {"message": "Please provide a search query. Usage: /websearch <query>"}

            # Initialize Azure Data Web search service
            azure_search = AzureDataWebService(
                decision_model_name=settings.web_search_decision_model,
                azure_endpoint=settings.azure_ai_endpoint,
                deployment=settings.web_search_decision_deployment,
                azure_api_key=settings.azure_ai_api_key,
                search_max_results=settings.web_search_max_results,
                api_version=settings.web_search_api_version,
            )

            # Perform web search
            web_docs = azure_search.run_web_search(query.strip())

            if not web_docs:
                return {"message": f"No web search results found for: {query}"}

            # Format results
            results = []
            for doc in web_docs[:5]:  # Limit to top 5 results
                title = doc.metadata.get("title", "No title")
                source = doc.metadata.get("source", "No URL")
                content = doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content
                results.append(f"• **{title}**\n  {content}\n  Source: {source}")

            result_text = f"Web search results for '{query}':\n\n" + "\n\n".join(results)
            return {"message": result_text, "action": "websearch_results", "query": query, "results": web_docs}

        except Exception as e:
            return {"message": f"Error performing web search: {str(e)}"}


# Create a singleton instance
slash_command_manager = SlashCommandManager()
