import logging
from typing import List, Optional
from uuid import UUID

import psycopg
from query_app.api.models.request import AddFeedbackRequest
from query_app.database import DBSession
from query_app.database.models import Feedback, Query

logger = logging.getLogger(__name__)


def insert_feedback(db_session: DBSession, feedback_request: AddFeedbackRequest) -> str:
    """Insert new feedback."""
    query = db_session.query(Query).filter_by(id=UUID(feedback_request.query_id)).first()
    if not query:
        raise ValueError("Query not found with the provided query_id")

    feedback = Feedback(
        query_id=feedback_request.query_id,
        collection_id=query.collection_id,
        feedback_type=feedback_request.feedback_type,
        feedback_value=feedback_request.feedback_value,
        feedback_text=feedback_request.feedback_text,
        status=feedback_request.status,
    )
    db_session.add(feedback)
    db_session.commit()
    db_session.refresh(feedback)
    return feedback.id


def get_feedback(db_session: DBSession, feedback_id: str) -> Optional[Feedback]:
    """Fetch feedback by ID."""
    try:
        return db_session.query(Feedback).filter_by(id=feedback_id).first()
    except psycopg.errors.InvalidTextRepresentation:
        raise ValueError("Invalid feedback ID")


def get_feedbacks(db_session: DBSession) -> List[Feedback]:
    """Fetch all feedback records."""
    try:
        return db_session.query(Feedback).all()
    except psycopg.errors.InvalidTextRepresentation:
        raise ValueError("Invalid DB query for feedbacks")


def update_feedback(
    db_session: DBSession,
    feedback_id: str,
    request_id: Optional[str] = None,
    status: Optional[str] = None,
) -> None:
    """Update feedback."""
    feedback_record = db_session.query(Feedback).filter_by(id=feedback_id).first()
    if not feedback_record:
        logger.warning("record not found")
    else:
        if request_id:
            feedback_record.request_id = request_id
        if status:
            feedback_record.status = status
        db_session.commit()


def delete_feedback(db_session: DBSession, feedback_id: str) -> bool:
    """Delete feedback by id. Returns True if deleted."""
    feedback_record = db_session.query(Feedback).filter_by(id=feedback_id).first()
    if not feedback_record:
        return False
    db_session.delete(feedback_record)
    db_session.commit()
    return True


def delete_feedbacks_by_query_id(db_session: DBSession, query_id: str) -> int:
    """Delete all feedbacks by query_id. Returns number of deleted records."""
    try:
        feedback_records = db_session.query(Feedback).filter_by(query_id=query_id).all()
    except psycopg.errors.InvalidTextRepresentation:
        raise ValueError("Invalid query ID format")

    if not feedback_records:
        return 0

    for record in feedback_records:
        db_session.delete(record)
    db_session.commit()
    return len(feedback_records)
