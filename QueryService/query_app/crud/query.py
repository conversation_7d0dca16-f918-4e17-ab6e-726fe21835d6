"""CRUD operations for managing queries in the QueryService."""

import logging
from typing import Any, List, Optional
from uuid import UUID

import psycopg
from query_app.api.models.request import QueryRequest
from query_app.database import DBSession
from query_app.database.models import Query

logger = logging.getLogger(__name__)


def get_relevant_docs_by_query_id(db: DBSession, query_id: str) -> List[dict]:
    """Fetch relevant documents for a given query ID.

    Args:
        db (DBSession): Database session.
        query_id (str): Query ID.

    Returns:
        List[dict]: List of relevant documents associated with the query.
    """
    try:
        query = db.query(Query).filter(Query.id == UUID(query_id)).first()
        if not query:
            raise ValueError(f"Query with ID {query_id} not found.")
        docs = query.retrieve_docs()
        return docs
    except Exception as e:
        raise ValueError(f"Failed to fetch relevant documents for query ID {query_id}: {e}")


def insert_query(db_session: DBSession, query_request: QueryRequest, collection_id: str, session_id: str) -> UUID:
    """Insert a new query into the database.

    Args:
        db_session (DBSession): Database session.
        query_request (QueryRequest): The incoming query request.
        collection_id (str): ID of the collection.
        session_id (str): ID of the user session.

    Returns:
        UUID: ID of the inserted query.
    """
    query = Query(
        request_id=query_request.request_id,
        collection_id=collection_id,
        content=query_request.content,
        raw_request=query_request.dict(),
        session_id=session_id,
    )
    db_session.add(query)
    db_session.commit()
    db_session.refresh(query)
    return query.id


def get_query(db_session: DBSession, query_id: str, collection_id: str) -> Optional[Query]:
    """Retrieve a query by its ID and collection ID.

    Args:
        db_session (DBSession): Database session.
        query_id (str): ID of the query.
        collection_id (str): ID of the collection.

    Returns:
        Optional[Query]: The found query or None.
    """
    try:
        query = db_session.query(Query).filter_by(id=query_id, collection_id=collection_id).first()
    except psycopg.errors.InvalidTextRepresentation:
        raise Exception("invalid query request")
    return query


def update_query(
    db_session: DBSession,
    query_id: UUID,
    collection_id: str,
    request_id: Optional[str] = None,
    content: Optional[str] = None,
    answer: Optional[str] = None,
    raw_response: Optional[dict] = None,
    retrieved_docs: Optional[dict] = None,
    retrieved_docs_mmr: Optional[dict] = None,
    is_favorite: Optional[bool] = None,
) -> Any:
    """Update fields of an existing query.

    Args:
        db_session (DBSession): Database session.
        query_id (UUID): ID of the query.
        collection_id (str): Collection ID.
        request_id (str, optional): Optional request ID.
        content (str, optional): Optional updated content.
        answer (str, optional): Optional updated answer.
        raw_response (dict, optional): Optional updated raw response.
        retrieved_docs (dict, optional): Retrieved documents.
        retrieved_docs_mmr (dict, optional): MMR documents.
        is_favorite (bool, optional): Whether the query is marked favorite.

    Returns:
        Query: Updated query object.
    """
    query_record = db_session.query(Query).filter_by(id=query_id, collection_id=collection_id).first()
    if not query_record:
        logger.warning("record not found")
    else:
        if request_id:
            query_record.request_id = request_id
        if content:
            query_record.content = content
        if answer:
            query_record.answer = answer
        if raw_response:
            query_record.raw_response = raw_response
        if retrieved_docs:
            query_record.retrieved_docs = retrieved_docs
        if retrieved_docs_mmr:
            query_record.retrieved_docs_mmr = retrieved_docs_mmr
        if is_favorite is not None:
            query_record.is_favorite = is_favorite
        db_session.commit()
    return query_record


def delete_query(db_session: DBSession, query_id: str, collection_id: Optional[str] = None) -> None:
    """Delete a query by ID (and optional collection ID).

    Args:
        db_session (DBSession): Database session.
        query_id (str): ID of the query.
        collection_id (str, optional): Optional collection ID.

    Returns:
        None
    """
    if collection_id:
        query_record = db_session.query(Query).filter_by(id=query_id, collection_id=collection_id).first()
    else:
        query_record = db_session.query(Query).filter_by(id=query_id).first()
    if not query_record:
        logger.warning("record not found")
    else:
        db_session.delete(query_record)
        db_session.commit()


def delete_queries_by_collection_id(db_session: DBSession, collection_id: str) -> None:
    """Delete all queries belonging to a specific collection.

    Args:
        db_session (DBSession): Database session.
        collection_id (str): Collection ID.

    Returns:
        None
    """
    query_records = db_session.query(Query).filter_by(collection_id=collection_id).all()
    if not query_records:
        logger.warning("no query found")
    else:
        for query_record in query_records:
            db_session.delete(query_record)
        db_session.commit()


def get_queries_by_session_id(db: DBSession, session_id: str) -> List[dict]:
    """Fetch all queries for a given session ID.

    Args:
        db (DBSession): Database session.
        session_id (str): Session ID.

    Returns:
        List[dict]: List of query dictionaries.
    """
    try:
        queries = db.query(Query).filter(Query.session_id == UUID(session_id)).order_by(Query.created_at.asc()).all()
        return [query.to_dict() for query in queries]
    except Exception as e:
        raise ValueError(f"Failed to fetch queries for session ID {session_id}: {e}")


def get_queries(db_session: DBSession, collection_id: str) -> Optional[List[Query]]:
    """Get all queries by collection ID.

    Args:
        db_session (DBSession): Database session.
        collection_id (str): Collection ID.

    Returns:
        Optional[List[Query]]: List of queries or empty list.
    """
    try:
        queries = db_session.query(Query).filter_by(collection_id=collection_id).all()
    except psycopg.errors.InvalidTextRepresentation:
        raise Exception("invalid query request")
    return queries or []
