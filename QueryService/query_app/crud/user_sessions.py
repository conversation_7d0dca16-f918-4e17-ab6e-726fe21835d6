import logging
from datetime import datetime
from typing import Optional
from uuid import UUID

from query_app.api.models.request import UserSessionUpdate
from query_app.database import DBSession
from query_app.database.models import Query, UserSession
from query_app.utils.helpers import generate_session_title, get_entra_auth_user_info

logger = logging.getLogger(__name__)


def get_or_create_user_session_with_title(
    db_session: DBSession,
    auth,
    session_id: Optional[str],
    query_content: Optional[str] = None,
    collection_id: Optional[str] = None,
) -> UserSession:
    """
    Get an existing session or create a new one for the user.
    If the session exists but the title is missing, generate a title.

    Args:
        db_session (Session): Database session.
        user_email (str): User's email address.
        session_id (str): Session ID.
        query_content (str): The query content for generating a title.
        collection_id (str): Collection ID associated with the session.
        folder_name (str): Optional folder name; if provided, its chat_goal is used.

    Returns:
        UserSession: The existing or newly created user session.
    """
    try:

        user_info, is_azure_user = get_entra_auth_user_info(auth)
        user_email = user_info.get("email")
        if not collection_id:
            raise ValueError("Collection ID is required to create a user session.")

        if session_id:
            session = db_session.query(UserSession).filter_by(id=UUID(session_id)).first()
            if not session:
                raise ValueError(f"UserSession with ID {session_id} not found.")
            if not session.title or session.title == "Neue Sitzung":
                custom_title = generate_session_title(query_content)
            elif session.goal:
                now = datetime.now()
                formatted_date = now.strftime("%d/%m/%Y")
                custom_title = f"{formatted_date} - {session.goal}"
            else:
                custom_title = generate_session_title(query_content)

            session.title = custom_title
            db_session.commit()
        else:
            title = generate_session_title(query_content)
            session = UserSession(
                user_email=user_email,
                title=title,
                collection_id=UUID(collection_id),
            )
            db_session.add(session)
            db_session.commit()
            db_session.refresh(session)

        return session
    except Exception as e:
        db_session.rollback()
        raise ValueError(f"Failed to get or create UserSession: {e}")


def get_user_session_by_id(db: DBSession, session_id: str):
    """
    Fetch a user session by its ID.

    Args:
        db (DBSession): Database session.
        session_id (str): Session ID.

    Returns:
        UserSession: The user session if found.
    """
    session = db.query(UserSession).filter(UserSession.id == UUID(session_id)).first()
    if not session:
        raise ValueError(f"UserSession with ID {session_id} not found.")
    return session


def get_all_user_sessions(db: DBSession, skip: int = 0, limit: int = 10):
    """
    Fetch all user sessions with optional pagination.

    Args:
        db (DBSession): Database session.
        skip (int): Number of records to skip.
        limit (int): Number of records to fetch.

    Returns:
        List[UserSession]: List of user sessions.
    """
    try:
        sessions = db.query(UserSession).offset(skip).limit(limit).all()
        return sessions
    except Exception as e:
        raise ValueError(f"Failed to fetch UserSessions: {e}")


def update_user_session(db: DBSession, session_id: str, session_data: UserSessionUpdate):
    """
    Update an existing user session.

    Args:
        db (DBSession): Database session.
        session_id (str): Session ID to update.
        session_data (UserSessionUpdate): Updated session data.

    Returns:
        UserSession: The updated user session.
    """
    session = db.query(UserSession).filter(UserSession.id == UUID(session_id)).first()
    if not session:
        raise ValueError(f"UserSession with ID {session_id} not found.")
    try:
        for key, value in session_data.dict(exclude_unset=True).items():
            setattr(session, key, value)
        db.commit()
        db.refresh(session)
        return session
    except Exception as e:
        db.rollback()
        raise ValueError(f"Failed to update UserSession: {e}")


def delete_user_session(db: DBSession, session_id: str):
    """
    Delete a user session by its ID.

    Args:
        db (DBSession): Database session.
        session_id (str): Session ID.

    Returns:
        UserSession: The deleted user session.
    """
    session = db.query(UserSession).filter(UserSession.id == UUID(session_id)).first()
    if not session:
        raise ValueError(f"UserSession with ID {session_id} not found.")
    try:
        db.delete(session)
        db.commit()
        return session
    except Exception as e:
        db.rollback()
        raise ValueError(f"Failed to delete UserSession: {e}")


def get_queries_by_session_id(db: DBSession, session_id: str):
    """
    Fetch all queries for a given session ID.

    Args:
        db (DBSession): Database session.
        session_id (str): Session ID.

    Returns:
        List[Query]: List of queries associated with the session.
    """
    try:
        queries = db.query(Query).filter(Query.session_id == UUID(session_id)).order_by(Query.created_at.asc()).all()
        queries = [query.to_dict() for query in queries]
        return queries
    except Exception as e:
        raise ValueError(f"Failed to fetch queries for session ID {session_id}: {e}")


def get_sessions_by_collection_id(db: DBSession, collection_id: str, user_email: str):
    """
    Fetch user sessions associated with a collection ID in descending order by created_at.
    """
    try:
        sessions = (
            db.query(UserSession)
            .filter(UserSession.collection_id == UUID(collection_id))
            .filter(UserSession.user_email == user_email)
            .order_by(UserSession.created_at.desc())
            .all()
        )
        return [session.to_dict() for session in sessions]

    except Exception as e:
        raise ValueError(f"Failed to fetch sessions for collection ID {collection_id}: {e}")


def get_sessions(db_session: DBSession, user_email: str):
    """
    Fetch all user sessions of a user and their queries,
    including collection info and returning everything in a single list of dicts.
    """
    try:
        sessions = db_session.query(UserSession).filter(UserSession.user_email == user_email).all()
        sessions_data = []
        for session in sessions:
            session_dict = session.to_dict()

            # Queries
            queries = [q.to_dict() for q in session.queries]
            session_dict["queries"] = queries
            session_dict["collection_name"] = session.collection.name if session.collection else None

            sessions_data.append(session_dict)

        return sessions_data
    except Exception as e:
        raise ValueError(f"Failed to fetch user sessions and queries: {e}")


def create_session(db: DBSession, collection_id: str, user_email: str, goal: str):
    """
    Create a new user session.

    Args:
        db (DBSession): Database session.
        collection_id (str): Collection ID.
        user_email (str): User's email.
        goal (str): Session goal.

    Returns:
        UserSession: The created user session.
    """
    try:
        new_session = UserSession(
            title="Neue Sitzung",
            collection_id=UUID(collection_id),
            user_email=user_email,
            goal=goal,
        )
        db.add(new_session)

        if goal and goal.strip():
            now = datetime.now()
            formatted_date = now.strftime("%d/%m/%Y")
            new_session.title = f"{formatted_date} - {goal}"

        db.commit()
        db.refresh(new_session)
        return new_session.to_dict()
    except Exception as e:
        db.rollback()
        raise ValueError(f"Failed to create UserSession: {e}")


def delete_session_by_id(db_session: DBSession, session_id: str) -> None:
    """Delete a user session by its ID."""
    session = db_session.query(UserSession).filter(UserSession.id == session_id).first()
    if session:
        db_session.delete(session)
        db_session.commit()


def update_session_title(
    db_session: DBSession,
    session_id: str,
    title: Optional[str] = None,
    custom_config: Optional[dict] = None,
) -> dict:
    """Update the title and custom configuration of a user session."""
    session = db_session.query(UserSession).filter(UserSession.id == session_id).first()
    if not session:
        raise ValueError("Session not found")

    if title:
        session.title = title
    if custom_config:
        session.custom_config = custom_config
    db_session.commit()
    db_session.refresh(session)
    return session.to_dict()


def get_session_by_id(db_session: DBSession, session_id: str) -> UserSession:
    """
    Retrieve a session by its ID.
    """
    return db_session.query(UserSession).filter(UserSession.id == session_id).first()


def update_session_pdfs(db_session: DBSession, session_id: str, uploaded_pdfs: str) -> dict:
    """Update the uploaded PDFs for a user session."""
    session = db_session.query(UserSession).filter(UserSession.id == session_id).first()
    if not session:
        raise ValueError("Session not found")

    session.uploaded_pdfs = uploaded_pdfs
    db_session.commit()
    db_session.refresh(session)
    return session.to_dict()
