import logging
from time import time
from typing import Optional, Tuple
from uuid import UUID

from langchain.chains import HypotheticalDocumentEmbedder, LLMChain, RetrievalQA
from langchain.chains.retrieval_qa.base import BaseRetrievalQA
from langchain.prompts import PromptTemplate
from langchain_community.callbacks import get_openai_callback
from query_app.llm.models import MODEL_PRESETS
from query_app.services.token_limiter import TokenLimiter
from query_app.services.vectordb import VectorDB
from query_app.settings import settings

logger = logging.getLogger(__name__)

# this is being used by HYDE.
HYDE_PROMPT_TEMPLATE = """Bitte schreiben Sie eine Antwort auf die Frage

    Frage: {question}
    Antwort:"""


DEFAULT_SYSTEM_PROMPT = """Du bist ein assistent für die beantwortung von ausschreibungen bzw. das erstellen von bewerbungsunterlagen auf ausschreibungen.
    Anbei hast du eine zip datei mit diversen unterlagen zu diesem thema von einem unserer kunden.
    wenn dir eine aufgabe gestellt wird, stellst du entsprechende nachfragen bis du sicher bist dass du einen entsprechenden text formulieren kannst.
    Bitte beschreibe deine arbeitsschritte.
    Wenn Sie eines oder mehrere der bereitgestellten Dokumente zitieren, geben Sie diese bitte in Ihren Antworten in form ["Name Der Quelle 1", "Name Der Quelle 2"] an. So viel
    wie möglich verwenden.Formuliere die texte sachlich und beziehe dich auf innerhalb der dokumente vorhandene informationen."""

PROMPT_TEMPLATE_SUFFIX = """

    {context}

    Frage: {question}
    Antwort:"""


class Hyde(object):
    """Hyde class for handling queries and document retrieval."""

    def __init__(
        self,
        collection_name: str,
        embedding_model_name: str,
        llm_model_name: str,
        top_k: Optional[int] = settings.hyde_top_k,
        custom_system_prompt: Optional[str] = None,
    ):
        """Initialize hyde object.

        Args:
            collection_name: collection name.
            embedding_model_name: embedding model.
            llm_model_name: llm model.
            top_k: the number of top results to retrieve.
            custom_system_prompt: custom system prompt to be used in collection(German only).
        """
        self.collection_name = f"{collection_name}_hyde"
        self.top_k = top_k

        _llm = MODEL_PRESETS[llm_model_name]
        self._chat_model = _llm.chat_model
        self._multi_llm = _llm.multi_llm

        self.hyde_prompt_template = HYDE_PROMPT_TEMPLATE
        self.prompt = PromptTemplate(input_variables=["question"], template=self.hyde_prompt_template)
        self.llm_chain = LLMChain(
            llm=self._multi_llm,
            prompt=self.prompt,
            llm_kwargs={"max_tokens": settings.hyde_generation_max_token_count},
            verbose=True,
        )
        self._embedding_model = HypotheticalDocumentEmbedder(
            llm_chain=self.llm_chain,
            base_embeddings=MODEL_PRESETS[embedding_model_name].embedding_model,
        )
        self._load_db()

        # generation part

        # use custom prompt if exists
        # NOTE: there is a fixed suffix(PROMPT_TEMPLATE_SUFFIX) which is in German.
        self.system_prompt = custom_system_prompt or DEFAULT_SYSTEM_PROMPT
        self.prompt_template = self.system_prompt + PROMPT_TEMPLATE_SUFFIX

        self.qa_chain_prompt = PromptTemplate.from_template(self.prompt_template)
        self.retrieval_chain = self._init_retrieval_chain()

        self.token_limiter = TokenLimiter()

    def _load_db(self) -> None:
        """Load/init vectordb."""
        self.vectordb = VectorDB(self.collection_name, embedding_model=self._embedding_model)

    def _init_retrieval_chain(self) -> BaseRetrievalQA:
        """Init retrievel chain(langchain)."""
        retriever = self.vectordb.db.as_retriever(search_kwargs={"k": self.top_k}, search_type="mmr")
        return RetrievalQA.from_chain_type(
            self._chat_model,
            chain_type="stuff",
            retriever=retriever,
            return_source_documents=True,
            chain_type_kwargs={"prompt": self.qa_chain_prompt},
            verbose=True,
        )

    def _get_collection_count(self) -> int:
        """Get document count in the collection."""
        # TODO: check and verify this
        return self.vectordb._collection.count()  # type: ignore

    def query(self, query: str) -> dict:
        """Run query.

        Args:
            query: question.

        Returns:
            response: answer.
        """
        with get_openai_callback() as cb:
            # TODO: add document deduplication
            _start = time()
            query_response = self.retrieval_chain({"query": query})
            _elapsed_time = time() - _start
            logger.info(f"hyde query was executed in {_elapsed_time} seconds")
            logger.debug(f"{query_response=}")
            self.token_limiter.store_token_usage(cb.total_tokens, cb.total_cost)  # cb.total_tokens cb.prompt_tokens cb.completion_tokens cb.total_cost
            return query_response

    def retrieve_relevant_docs(self, query: str, top_k: int = 5) -> Tuple[list, list]:
        """Retrieve relevant documents from the collection.

        Args:
            query: question.
            top_k: the number of documents to be retrieved.
        """
        # Ensure top_k is an integer and not None
        top_k = top_k if top_k is not None else self.top_k
        docs = self.vectordb.similarity_search(query, k=top_k)
        docs_mmr = self.vectordb.db.max_marginal_relevance_search(query, k=top_k)  # type: ignore
        return docs, docs_mmr

    def index_anonymized_chunks(self, document_id: UUID, splits: list) -> None:
        """Add documents(preferably anonymized) to the collection.

        Args:
            document_id: document identifier.
            splits: chunks.
        """
        _start = time()
        logger.debug(f"extracting embeddings for {len(splits)} chunks")
        self.vectordb.add_documents(document_id, splits)
        _elapsed_time = time() - _start
        logger.debug(f"extracted embeddings for {len(splits)} chunks in {_elapsed_time} seconds")
