import logging
import re
from typing import List

from langchain import PromptTemplate

# <PERSON><PERSON>hain and local imports from your original class
from langchain.schema import Document, HumanMessage, SystemMessage

# Azure OpenAI client
from openai import AzureOpenAI
from query_app.llm.models import MODEL_PRESETS

logger = logging.getLogger(__name__)


class AzureDataWebService:
    """
    Encapsulates web search logic using an Azure OpenAI deployment
    with the integrated Bing Search ("on your data") feature.
    It preserves the structure of the original WebService class, including
    a separate decision step before performing the search.
    """

    def __init__(
        self,
        decision_model_name: str,
        azure_endpoint: str,
        deployment: str,
        azure_api_key: str,
        search_max_results: int = 5,
        api_version: str = "2025-01-01-preview",
    ):
        """
        Initializes the web service with a decision model and Azure Search parameters.

        Args:
            decision_model_name: The key/name of the chat model to use for the decision step.
            azure_endpoint: The Azure OpenAI endpoint (e.g., "https://aifoundry-fbeta.cognitiveservices.azure.com/").
            deployment: The deployment name (e.g., "gpt-4.1").
            azure_api_key: The API key for your Azure OpenAI resource.
            search_max_results: The maximum number of web hits for the Azure service to retrieve.
            api_version: The API version to use for Azure OpenAI.
        """
        # --- Decision Making Components (from original class) ---
        self.decision_model = MODEL_PRESETS[decision_model_name].chat_model
        self.decision_prompt = PromptTemplate.from_template(
            "You are an assistant that decides if external web search is needed.\n\n"
            "Below are snippets from internal documents:\n{context}\n\n"
            "User question:\n{question}\n\n"
            "Decide if the user's question can be answered using only the information in the snippets.\n"
            "If the information in the snippets is unrelated, incomplete, or insufficient to answer the question, reply 'yes'.\n"
            "Otherwise, reply 'no'.\n\n"
            "Reply with exactly 'yes' or 'no'."
        )

        # --- Azure OpenAI Client Components ---
        if not azure_endpoint or not azure_api_key or not deployment:
            raise ValueError("Azure endpoint, deployment, and API key are required.")

        self.search_max_results = search_max_results
        self.deployment = deployment

        # Initialize Azure OpenAI client
        self.client = AzureOpenAI(
            api_version=api_version,
            azure_endpoint=azure_endpoint,
            api_key=azure_api_key,
        )

    def is_need_web_search(self, query: str, docs: List[Document], document_char_size: int = 1500) -> bool:
        """
        Returns True if the LLM, when shown `docs`, says a web search is needed.
        (This method is identical to the original implementation).
        """
        try:
            logger.info("Deciding if web search is needed....")
            snippets = [d.page_content[:document_char_size].replace("\n", " ") for d in docs]
            context = "\n".join(f"- {s}" for s in snippets) or "No docs available."

            prompt = self.decision_prompt.format(context=context, question=query)
            response = self.decision_model([SystemMessage(content="Decide if web search is needed."), HumanMessage(content=prompt)])
            answer = response.content.strip().lower()
            logger.info(f"The decision is: {answer=}")
            return answer.startswith("yes")
        except Exception as e:
            logger.error("Error during decision-making for web search", exc_info=e)
            return False

    def run_web_search(self, query: str) -> List[Document]:
        """
        Perform web search using Azure OpenAI without dataSources parameter.

        Note: This method generates web search-like responses using the Azure OpenAI model
        without relying on the dataSources parameter which is not supported in all deployments.
        """
        logger.info("Running Azure OpenAI web search simulation...")

        try:
            # Use Azure OpenAI client for web search simulation
            response = self.client.chat.completions.create(
                messages=[
                    {
                        "role": "system",
                        "content": (
                            "You are a helpful assistant that answers user questions directly and "
                            "accurately. Provide clear, relevant information that directly addresses "
                            "what the user is asking for. Be informative but focused on the specific query."
                        ),
                    },
                    {
                        "role": "user",
                        "content": (
                            f"User asked: {query}\n\nPlease provide a clear and comprehensive answer to "
                            "this specific question. Focus on what the user is actually asking for and "
                            "provide relevant, accurate information."
                        ),
                    },
                ],
                max_tokens=4000,
                temperature=0.1,
                top_p=1.0,
                frequency_penalty=0.0,
                presence_penalty=0.0,
                model=self.deployment,
            )

            # Extract content from the standard response and create document-like objects
            docs: List[Document] = []

            if hasattr(response, "choices") and response.choices:
                choice = response.choices[0]
                if hasattr(choice, "message") and choice.message.content:
                    content = choice.message.content

                    # Split the response into logical sections to simulate multiple sources
                    # This creates a more realistic web search experience
                    sections = self._split_response_into_sections(content, query)

                    for i, section in enumerate(sections):
                        if section.strip():  # Only add non-empty sections
                            # Try to extract a meaningful title from the section
                            section_lines = section.strip().split("\n")
                            title = f"Information about {query} - Part {i + 1}"

                            # Look for headers in the first few lines
                            for line in section_lines[:3]:
                                line = line.strip()
                                if line and (
                                    line.startswith("#")
                                    or ":" in line  # noqa: W503
                                    or line.isupper()  # noqa: W503
                                    or any(marker in line for marker in ["Overview", "Analysis", "Business", "Performance", "Strategic", "Future", "Challenges"])  # noqa: W503
                                ):
                                    # Clean up the header text
                                    clean_title = line.replace("#", "").replace(":", "").strip()
                                    if len(clean_title) > 5 and len(clean_title) < 80:
                                        title = clean_title
                                    break

                            docs.append(
                                Document(
                                    page_content=section.strip(),
                                    metadata={"source": "", "title": title, "search_query": query, "web_search": True, "search_engine": "azure_openai", "section_number": i + 1},
                                )
                            )

            # Ensure we have at least one document
            if not docs:
                docs.append(
                    Document(
                        page_content=f"No specific information found for: {query}",
                        metadata={"source": "", "title": f"Search result for: {query}", "search_query": query, "web_search": True, "search_engine": "azure_openai"},
                    )
                )

            logger.info(f"Azure web search returned {len(docs)} results")
            return docs

        except ImportError as e:
            logger.error(f"Missing dependency for Azure OpenAI: {e}")
            return []
        except Exception as e:
            logger.error(f"Azure OpenAI web search failed: {e}", exc_info=True)
            return []

    def _split_response_into_sections(self, content: str, query: str) -> List[str]:
        """
        Split the response content into logical sections to simulate multiple search sources.
        Handles both formatted responses with headers and plain text responses.
        """
        # First, try to split by major sections (using ## headers or numbered sections)
        section_markers = [
            r"##\s+\d+\.",  # ## 1. Section Title
            r"##\s+[A-Z]",  # ## A. Section Title or ## Overview
            r"###\s+",  # ### Sub-section
            r"---",  # --- dividers
            r"\n\d+\.",  # 1. Numbered sections at start of line
            r"\n[A-Z]\.",  # A. Lettered sections at start of line
        ]

        # Try splitting by headers first
        for marker in section_markers:
            if re.search(marker, content):
                parts = re.split(marker, content)
                # Remove empty parts and clean up
                parts = [part.strip() for part in parts if part.strip()]
                if len(parts) >= 2:
                    # Take the first few sections, ensuring they're substantial
                    substantial_parts = []
                    for part in parts[: self.search_max_results + 2]:
                        if len(part) > 100:  # Only include substantial sections
                            substantial_parts.append(part)
                    if len(substantial_parts) >= 2:
                        return substantial_parts[: self.search_max_results]

        # Fallback to splitting by major paragraph breaks (triple newlines or more)
        major_sections = [p.strip() for p in re.split(r"\n{3,}", content) if p.strip()]
        if len(major_sections) >= 2:
            return major_sections[: self.search_max_results]

        # Split by double newlines (paragraph breaks)
        paragraphs = [p.strip() for p in content.split("\n\n") if p.strip() and len(p.strip()) > 50]

        if len(paragraphs) >= 2:
            # Group 2-3 paragraphs per section for better readability
            grouped_sections: List[str] = []
            current_group: List[str] = []

            for para in paragraphs:
                current_group.append(para)
                if len(current_group) >= 2:  # 2-3 paragraphs per section
                    grouped_sections.append("\n\n".join(current_group))
                    current_group = []

                    if len(grouped_sections) >= self.search_max_results:
                        break

            # Add remaining paragraphs
            if current_group:
                grouped_sections.append("\n\n".join(current_group))

            return grouped_sections

        # If single long paragraph, split by sentences more intelligently
        if len(paragraphs) == 1:
            sentences = [s.strip() for s in paragraphs[0].split(".") if s.strip() and len(s.strip()) > 20]

            # Group sentences into sections of 3-5 sentences each
            sentence_sections: List[str] = []
            current_section: List[str] = []

            for sentence in sentences:
                current_section.append(sentence + ".")
                if len(current_section) >= 4:  # 3-5 sentences per section
                    sentence_sections.append(" ".join(current_section))
                    current_section = []

                    if len(sentence_sections) >= self.search_max_results:
                        break

            # Add any remaining sentences as the last section
            if current_section:
                sentence_sections.append(" ".join(current_section))

            return sentence_sections

        # Final fallback: return the original content as a single section
        return [content] if content else []
