import json
import logging

from query_app.services.redis_connection import redis_conn

logger = logging.getLogger(__name__)


class RedisThreadManager:
    """Manages conversation threads in Redis."""

    def __init__(self):
        """Initialize the RedisThreadManager with a Redis connection."""
        self.redis = redis_conn

    def create_thread(self, thread_id: str, custom_system_prompt: str):
        """Create a new thread with an optional custom system prompt."""
        if self.redis.exists(f"thread:{thread_id}"):
            return self.retrieve_thread(thread_id)
        self.redis.set(
            f"thread:{thread_id}",
            json.dumps([{"role": "system", "content": custom_system_prompt}] if custom_system_prompt else []),
        )
        return self.retrieve_thread(thread_id)

    def retrieve_thread(self, thread_id: str):
        """Retrieve a thread by its ID."""
        thread_data = self.redis.get(f"thread:{thread_id}")
        if not thread_data:
            return []
        return json.loads(thread_data)

    def retrieve_messages(self, thread_id: str, prepare_context: bool = False):
        """Retrieve messages from a thread by its ID."""
        thread_data = self.redis.get(f"thread:{thread_id}")
        thread_messages = json.loads(thread_data)
        if not thread_messages:
            return "" if prepare_context else []

        if prepare_context:
            context_parts = [f"{message['role'].capitalize()}: {message['content']}\n" for message in thread_messages]
            return "\n".join(context_parts)

        return thread_messages

    def append_messages(self, thread_id: str, messages: list):
        """Append messages to an existing thread."""
        thread_data = self.retrieve_thread(thread_id)
        thread_data.extend(messages)
        self.redis.set(f"thread:{thread_id}", json.dumps(thread_data))
        return thread_data

    def delete_thread(self, thread_id: str):
        """Delete a thread by its ID."""
        if not self.redis.exists(f"thread:{thread_id}"):
            raise ValueError(f"Thread {thread_id} does not exist.")
        self.redis.delete(f"thread:{thread_id}")

    def is_exist(self, thread_id: str):
        """Check if a thread exists by its ID."""
        return self.redis.exists(f"thread:{thread_id}")
