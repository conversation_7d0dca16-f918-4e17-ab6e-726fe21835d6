"""Semantic Search Service. """

import logging
from time import time
from typing import As<PERSON><PERSON><PERSON>ator, <PERSON>, Optional, Tuple
from uuid import UUID

from langchain.chains import ConversationalRetrieval<PERSON>hain, RetrievalQA
from langchain.memory import ConversationBufferMemory
from langchain.prompts import PromptTemplate
from langchain.schema import HumanMessage
from langchain_community.callbacks import get_openai_callback
from langchain_community.chat_message_histories import RedisChatMessageHistory
from langchain_core.documents import Document
from query_app.enums.rank import ReRankStrategy
from query_app.enums.search import ChatHistoryTTL
from query_app.llm.enums import OPENAI_MODELS, ReRankLLMModels, WebLLMModels
from query_app.llm.models import MODEL_PRESETS
from query_app.services.rerank import ReRank, ReRankRetriever
from query_app.services.search import SearchEngine
from query_app.services.token_limiter import (
    AZURE_OPENAI_PRICES_PER_MILLION,
    StreamCostCal<PERSON>tor,
    TokenLimiter,
)
from query_app.services.vectordb import VectorDB
from query_app.settings import settings

logger = logging.getLogger(__name__)

DEFAULT_SYSTEM_PROMPT_MEMORY = """
Du bist ein Assistent für die Beantwortung von Ausschreibungen bzw. das Erstellen von Bewerbungsunterlagen auf Ausschreibungen.
Anbei hast du eine ZIP-Datei mit diversen Unterlagen zu diesem Thema von einem unserer Kunden.
Wenn dir eine Aufgabe gestellt wird, stellst du entsprechende Nachfragen, bis du sicher bist, dass du einen entsprechenden Text formulieren kannst.
Bitte beschreibe deine Arbeitsschritte.Wenn Sie eines oder mehrere der bereitgestellten Dokumente zitieren, geben Sie diese bitte in Ihren Antworten in Form ["Name Der Quelle 1",
"Name Der Quelle 2"] an. So viel wie möglich verwenden.Formuliere die Texte sachlich und beziehe dich auf innerhalb der Dokumente vorhandene Informationen.

Zusätzlich darfst du gerne auf den bisherigen Gesprächsverlauf (Chatverlauf) zurückgreifen, um bereits genannte Informationen (z.B. den Namen des Nutzers) in deine Antworten mit
einzubeziehen.
"""

DEFAULT_SYSTEM_PROMPT = """
Du bist ein assistent für die beantwortung von ausschreibungen bzw. das erstellen von bewerbungsunterlagen auf ausschreibungen.
    Anbei hast du eine zip datei mit diversen unterlagen zu diesem thema von einem unserer kunden.
    wenn dir eine aufgabe gestellt wird, stellst du entsprechende nachfragen bis du sicher bist dass du einen entsprechenden text formulieren kannst.
    Bitte beschreibe deine arbeitsschritte.
    Wenn Sie eines oder mehrere der bereitgestellten dokumente zitieren, geben Sie diese bitte in Ihren Antworten in form ["Name Der Quelle 1", "Name Der Quelle 2"] an. So viel
    wie möglich verwenden.Formuliere die texte sachlich und beziehe dich auf innerhalb der dokumente vorhandene informationen.
"""

PROMPT_TEMPLATE_SUFFIX = """

    {context}

    Frage: {question}
    Hilfreiche Antwort:"""

PROMPT_TEMPLATE_SUFFIX_MEMORY = """
    Chatverlauf:
    {chat_history}

    Kontext:
    {context}

    Frage:
    {question}

    Hilfreiche Antwort:
"""


class SemanticSearch(object):
    """Semantic Search Service."""

    def __init__(
        self,
        collection_name: str,
        embedding_model_name: str,
        llm_model_name: str,
        top_k: Optional[int] = settings.semantic_search_top_k,
        session_id: Optional[str] = None,
        custom_system_prompt: Optional[str] = None,
        streaming: Optional[bool] = False,
        search_method: str = "similarity",
        is_memory_on: Optional[bool] = False,
        query_history_ttl: Optional[int] = ChatHistoryTTL.ONE_MONTH.value,
        re_rank: Optional[bool] = False,
        re_rank_strategy: ReRankStrategy = ReRankStrategy.SMALL_LLM,
        re_rank_small_llm_model_name: str = ReRankLLMModels.GPT4OMINI.value,
        re_rank_big_llm_model_name: str = OPENAI_MODELS.GPT4O.value,
        check_web_service: Optional[bool] = False,
        web_llm_model_name: str = WebLLMModels.GPT4OMINI.value,
        user_email: Optional[str] = None,
    ):
        """Initialize semantic search service.

        Args:
            collection_name: Name of the vector collection (without suffix); will be used with "_semantic_search".
            embedding_model_name: Embedding model key from presets.
            llm_model_name: LLM model key from presets ("-stream" suffix will be applied when streaming=True).
            top_k: Default number of documents to retrieve.
            session_id: Chat session identifier for memory/Redis keys.
            custom_system_prompt: Optional override for the system prompt used in QA chains.
            streaming: If True, use streaming LLM and streaming cost accounting.
            search_method: Retrieval method hint (e.g., "similarity").
            is_memory_on: Enable conversation memory (Redis) if True.
            query_history_ttl: TTL for chat history in Redis.
            re_rank: Enable re-ranking with small/big LLMs.
            re_rank_strategy: Strategy for reranking (small LLM preferred, etc.).
            re_rank_small_llm_model_name: Preset key for small rerank model.
            re_rank_big_llm_model_name: Preset key for big rerank model.
            check_web_service: If True, optionally augment with web search.
            web_llm_model_name: Preset key for web search decision model.
            user_email: End-user email for token usage accounting per user/thread.
        """

        self.collection_name = f"{collection_name}_semantic_search"
        self.user_email = user_email

        # ---------- rerank & web ------------------------------------------------
        self.re_rank = re_rank
        self.reranker = ReRank(re_rank_strategy, small_llm_model_name=re_rank_small_llm_model_name, big_llm_model_name=re_rank_big_llm_model_name)
        # Initialize web search service (Azure AI Foundry or DuckDuckGo fallback)
        self.check_web_service = check_web_service
        self.web_service = None
        if check_web_service:
            try:
                if settings.enable_azure_ai_search and settings.azure_ai_api_key:
                    from query_app.services.azure_data_web_service import (
                        AzureDataWebService,
                    )

                    self.web_service = AzureDataWebService(
                        decision_model_name=settings.web_search_decision_model,
                        azure_endpoint=settings.azure_ai_endpoint,
                        deployment=settings.web_search_decision_deployment,
                        azure_api_key=settings.azure_ai_api_key,
                        search_max_results=settings.web_search_max_results,
                        api_version=settings.web_search_api_version,
                    )
                    logger.info("Azure Data Web search service initialized")
                else:
                    # Fallback to DuckDuckGo
                    from query_app.services.web import WebService

                    self.web_service = WebService(decision_model_name=settings.web_search_decision_model, search_max_results=settings.web_search_max_results)
                    logger.info("DuckDuckGo web search service initialized (fallback)")
            except Exception as e:
                logger.error(f"Failed to initialize web search service: {e}")
                self.web_service = None
        # ---------- LLM ---------------------------------------------------------
        self.top_k = top_k
        self.streaming = streaming
        self.llm_model_name = llm_model_name  # Store for cost calculation
        self._embedding_model = MODEL_PRESETS[embedding_model_name].embedding_model
        self._chat_model = MODEL_PRESETS[llm_model_name + "-stream" if self.streaming else llm_model_name].chat_model

        # ---------- prompt ------------------------------------------------------
        self.prompt_template = (custom_system_prompt or (DEFAULT_SYSTEM_PROMPT if is_memory_on else DEFAULT_SYSTEM_PROMPT_MEMORY)) + (
            PROMPT_TEMPLATE_SUFFIX_MEMORY if is_memory_on else PROMPT_TEMPLATE_SUFFIX
        )
        self.qa_chain_prompt = PromptTemplate.from_template(self.prompt_template)

        # ---------- storage + search -------------------------------------------
        self.search_method = search_method
        self.vectordb = VectorDB(self.collection_name, embedding_model=self._embedding_model)
        self.search_engine = SearchEngine(self.vectordb, default_method=self.search_method, top_k=top_k)

        # ---------- chains & memory --------------------------------------------
        self.session_id = session_id
        self.is_memory_on = is_memory_on
        self.query_history_ttl = query_history_ttl
        self.retrieval_chain_with_memory, self.memory = self._init_retrieval_chain()

        # ---------- token limiter --------------------------------------------
        self.token_limiter = TokenLimiter()

    # ---------------------------------------------------------------------
    # Retrieval helpers
    # ---------------------------------------------------------------------
    def _init_memory(self):
        """Initialize memory for the retrieval chain."""
        chat_history = RedisChatMessageHistory(
            session_id=self.session_id,
            url=settings.redis_query_history_url,
            key_prefix=f"chat_history:{self.collection_name}:{self.session_id}:",
            ttl=self.query_history_ttl,
        )

        memory = ConversationBufferMemory(
            chat_memory=chat_history,
            return_messages=True,
            memory_key="chat_history",
        )

        return memory

    def _init_retrieval_chain(self):
        """Initialize the retrieval chain."""
        retriever = self.vectordb.db.as_retriever(search_kwargs={"k": self.top_k}, search_type="mmr")
        if self.reranker:
            retriever = ReRankRetriever(base_retriever=retriever, reranker=self.reranker, final_k=self.top_k)

        if self.is_memory_on:
            memory = self._init_memory()

            chain = ConversationalRetrievalChain.from_llm(
                llm=self._chat_model,
                retriever=retriever,
                memory=memory,
                verbose=True,
                return_source_documents=False,
                combine_docs_chain_kwargs={"prompt": self.qa_chain_prompt},
            )

            return chain, memory

        return (
            RetrievalQA.from_chain_type(
                self._chat_model,
                chain_type="stuff",
                retriever=retriever,
                return_source_documents=True,
                chain_type_kwargs={"prompt": self.qa_chain_prompt},
                verbose=True,
            ),
            None,
        )

    def _get_collection_count(self) -> int:
        """Get document count in the collection."""
        # TODO: check and verify this
        return self.vectordb._collection.count()  # type: ignore

    def _choose_multiplier(self) -> int:
        """Choose multiplier based on the number of chunks in the collection."""
        try:
            n_chunks = self._get_collection_count()
            logger.info(f"n_chunks: {n_chunks}")
        except Exception:
            n_chunks = 0
        if n_chunks < 50000:
            return 3
        if n_chunks < 500000:
            return 5
        if n_chunks < 2000000:
            return 7
        return 9

    @staticmethod
    def _doc_from_input(item):
        """Extract Document from input item."""
        return item[0] if isinstance(item, tuple) else item

    # ---------------------------------------------------------------------
    # RETRIEVAL
    # ---------------------------------------------------------------------
    def retrieve_relevant_docs(self, query: str, top_k: Optional[int] = None):
        """Retrieve relevant documents based on the query."""
        is_web_searched = False
        final_k = top_k if top_k is not None else (self.top_k or 5)  # default fallback
        multiplier = self._choose_multiplier()
        first_pass_k = min(multiplier * final_k, 128)

        raw_hits: List[Tuple[Document, float]] = self.search_engine.search(
            query,
            k=first_pass_k if self.re_rank else final_k,
        )
        docs_only: List[Document] = [d for d, _ in raw_hits]

        before_ids = [d.id for d in docs_only][:10]
        if self.re_rank and self.reranker:
            reranked_docs = self.reranker(question=query, docs=docs_only, top_k=final_k)
            if isinstance(reranked_docs, tuple):
                docs_only = reranked_docs[0]
            else:
                docs_only = reranked_docs
            after_ids = [d.id for d in docs_only][:10]
            logger.info(f"Is reranking successful ? {before_ids != after_ids}")

        if self.check_web_service and self.web_service and self.web_service.is_need_web_search(query, docs_only):
            is_web_searched = True
            web_docs = self.web_service.run_web_search(query)
            docs_only = web_docs + docs_only
            return docs_only, web_docs, is_web_searched

        docs_mmr = None if self.re_rank else self.vectordb.db.max_marginal_relevance_search(query, k=final_k)
        return docs_only, docs_mmr, is_web_searched

    # ---------------------------------------------------------------------
    # INDEXING
    # ---------------------------------------------------------------------
    def index_anonymized_chunks(self, document_id: UUID, splits: list) -> None:
        """Add documents(preferably anonymized) to the collection.

        Args:
            document_id: document identifier.
            splits: chunks.
        """
        _start = time()
        logger.debug(f"extracting embeddings for {len(splits)} chunks")
        self.vectordb.add_documents(document_id, splits)
        _elapsed_time = time() - _start
        logger.debug(f"extracted embeddings for {len(splits)} chunks in {_elapsed_time} seconds")

    # ---------------------------------------------------------------------
    # QUERY
    # ---------------------------------------------------------------------

    def query(self, query: str) -> dict:
        """Run query.

        Args:
            query: question.

        Returns:
            response: answer.
        """
        with get_openai_callback() as cb:
            # TODO: add document deduplication
            _start = time()
            if self.is_memory_on:
                chat_hist_text = "\n".join(f"{m.type}: {m.content}" for m in self.memory.chat_memory.messages)
                query_response = self.retrieval_chain_with_memory({"question": query, "chat_history": chat_hist_text})
            else:
                query_response = self.retrieval_chain_with_memory({"query": query})
            _elapsed_time = time() - _start
            logger.info(f"semantic_search query was executed in {_elapsed_time} seconds")
            logger.debug(f"{query_response=}")
            model_name = self.llm_model_name.replace("-stream", "")
            self.token_limiter.store_token_usage(tokens_used=cb.total_tokens, cost=cb.total_cost, user_email=self.user_email, llm_model=model_name)
            return query_response

    async def query_stream(self, query: str, retrieved_docs: Optional[List[Document]] = None) -> AsyncGenerator[str, None]:
        """Run query in streaming mode."""
        print(f"🚀 STREAM DEBUG: Starting query_stream with model: {self.llm_model_name}")

        with StreamCostCalculator(self.llm_model_name, AZURE_OPENAI_PRICES_PER_MILLION, prices_per_million=True) as calculator:
            context = " ".join(self._doc_from_input(doc).page_content for doc in (retrieved_docs or []))

            if self.is_memory_on:
                chat_hist_text = "\n".join(f"{m.type}: {m.content}" for m in self.memory.chat_memory.messages)
                prompt_text = self.qa_chain_prompt.format(
                    context=context,
                    question=query,
                    chat_history=chat_hist_text,
                )
            else:
                prompt_text = self.qa_chain_prompt.format(
                    context=context,
                    question=query,
                )

            # Set input text for token counting
            calculator.set_input_text(prompt_text)

            prompt = HumanMessage(content=prompt_text)
            chunks: List[str] = []

            # Stream and collect output
            async for token in self._chat_model.astream([prompt]):
                text = token.content if hasattr(token, "content") else str(token)
                chunks.append(text)
                calculator.add_output_text(text)  # Add each chunk to calculator
                yield text

            if self.is_memory_on:
                answer = "".join(chunks)
                self.memory.save_context({"input": query}, {"output": answer})

            # Calculator will finalize on __exit__ and calculate final tokens/cost

        # Store token usage and cost from calculator
        print(f"🚀 STREAM DEBUG: Final calculation - tokens: {calculator.total_tokens}, cost: {calculator.total_cost}")
        model_name = self.llm_model_name.replace("-stream", "")
        self.token_limiter.store_token_usage(tokens_used=calculator.total_tokens, cost=calculator.total_cost, user_email=self.user_email, llm_model=model_name)
