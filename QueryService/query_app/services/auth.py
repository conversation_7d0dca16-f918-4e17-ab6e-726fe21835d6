import logging
from typing import Any

from fastapi import Request, Security
from fastapi.security import SecurityScopes
from fastapi_azure_auth import SingleTenantAzureAuthorizationCodeBearer
from msal import ConfidentialClientApplication
from query_app.settings import settings

logger = logging.getLogger(__name__)


class CustomSingleTenantAzureAuthorizationCodeBearer(SingleTenantAzureAuthorizationCodeBearer):
    """Custom authorization scheme that extends SingleTenantAzureAuthorizationCodeBearer"""

    async def __call__(self, request: Request, security_scopes: SecurityScopes):
        """Custom call method to handle different user roles and debug settings."""
        if getattr(request.state, "is_admin_ui", False):
            return {"role": "admin_ui", "user": "admin"}
        if settings.is_debug and not settings.is_entra_id_auth_on:
            return {"role": "dev_local", "user": "developer"}
        return await super().__call__(request, security_scopes)


azure_scheme = CustomSingleTenantAzureAuthorizationCodeBearer(
    app_client_id=settings.app_client_id,
    tenant_id=settings.tenant_id,
    scopes=settings.scopes,
    allow_guest_users=True,
)


async def get_auth(user: Any = Security(azure_scheme)):
    """Retrieve the authenticated user information."""
    if isinstance(user, dict) and user.get("role") == "admin_ui":
        return user
    return user


def get_token():
    """Acquire an access token for the application using MSAL."""
    try:
        authority = f"https://login.microsoftonline.com/{settings.tenant_id}"
        scopes = [f"api://{settings.app_client_id}/.default"]

        app = ConfidentialClientApplication(
            client_id=settings.app_client_id,
            client_credential=settings.app_client_secret,
            authority=authority,
        )

        result = app.acquire_token_silent(scopes, account=None)
        if not result:
            result = app.acquire_token_for_client(scopes=scopes)

        if "access_token" in result:
            return result["access_token"]
        else:
            raise Exception(f"Failed to acquire token: {result.get('error_description')}")
    except Exception as e:
        logger.error(f"Error acquiring token: {e}")
    return None
