"""VectorDB service layer for managing document embeddings and search methods."""

import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from time import sleep
from typing import List, Optional, Tuple
from uuid import UUID

from langchain.schema import Document
from langchain_postgres.vectorstores import PGVector
from query_app.database.session import db_context
from query_app.settings import settings
from sqlalchemy import create_engine, text

logger = logging.getLogger(__name__)


class VectorDB(object):
    """VectorDB provides document storage, embedding, and search over a PGVector backend."""

    def __init__(
        self,
        collection_name: str,
        embedding_model,
        top_k: int = 4,
    ) -> None:
        """Initialize vector store (LangChain PGVector wrapper).

        Args:
            collection_name (str): Name of the collection/table in Postgres SQL.
            embedding_model: The embedding model to use for vectorization.
            search_method (str): Search method ("similarity", "mmr", "keyword", "hybrid").
            top_k (int): Number of top results to retrieve for similarity-based searches.
        """
        self.collection_name = collection_name
        self.embedding_model = embedding_model
        self.top_k = top_k

        # Initialize the PGVector
        self.db = PGVector(
            connection=str(settings.pgvector_url),
            collection_name=self.collection_name,
            embeddings=self.embedding_model,
            use_jsonb=True,
        )
        self.retriever = self.db.as_retriever(search_kwargs={"k": top_k})

    @staticmethod
    def serialize_collection_store(collection_store) -> dict:
        """Serialize a collection store object to a dictionary."""
        return {"name": collection_store.name, "cmetadata": collection_store.cmetadata}

    @staticmethod
    def serialize_embedding_store(embedding_store, embedding=False) -> dict:
        """Serialize an embedding store object, optionally including embedding vectors."""
        if embedding:
            return {
                "collection_id": embedding_store.collection_id,
                "document": embedding_store.document,
                "embedding": embedding_store.embedding,
                "cmetadata": embedding_store.cmetadata,
                "id": embedding_store.id,
            }
        return {
            "collection_id": embedding_store.collection_id,
            "document": embedding_store.document,
            "cmetadata": embedding_store.cmetadata,
            "id": embedding_store.id,
        }

    def _get_collection_count(self) -> int:
        """Retrieve the count of documents in the collection.

        Returns:
            int: Count of documents.
        """
        logger.info("_get_collection_count")
        with db_context() as db_session:
            collection = self.db.get_collection(db_session)
            if not collection:
                return 0
            count = db_session.query(self.db.EmbeddingStore).filter_by(collection_id=collection.uuid).count()
            return count

    def get_item_count(self) -> int:
        """Return total number of documents stored across all collections."""
        with db_context() as db_session:
            count = db_session.query(self.db.EmbeddingStore).count()
            return count

    def add_document(self, document_id: UUID, page_content: str, metadata: dict):
        """Add new document to the collection with embedding.

        Args:
            document_id (str): Document identifier.
            page_content (str): Content of the document.
            metadata (dict): Document metadata.
        """
        # Create a Document object from langchain
        new_doc = Document(page_content=page_content, metadata=metadata)
        # Add document to PGVector
        self.db.add_documents([new_doc], ids=[document_id])

    def add_documents(self, document_id: UUID, splits: list):
        """Add new documents to the collection with embedding (batch).

        Args:
            document_id (str): Document identifier.
            splits (list): chunks from the documents
        """
        # process document batch by batch and have some delay between batches
        # to avoid ratelimit errors on openai endpoint
        split_by = 10
        sleep_interval_between_splits = 1  # seconds

        # push chunk batches
        logger.info(f"[{document_id=}] | {len(splits)} documents(chunks) will be added. {split_by=}, {sleep_interval_between_splits=}")
        subset_start_ind = 0
        while subset_start_ind <= len(splits):
            # generate subset
            subset_splits = splits[subset_start_ind : subset_start_ind + split_by]
            logger.info(f"[{document_id=}] | processing batch at {subset_start_ind=}, batch_size: {len(subset_splits)}")

            _documents = []
            _document_ids = []
            for ind, split in enumerate(subset_splits):
                # Create a Document object
                _doc_id = f"{document_id}_{subset_start_ind + ind}"
                # NOTE: replace added because of the issue with null characters
                # ValueError: A string literal cannot contain NUL (0x00) characters
                _new_doc = Document(
                    page_content=str(split.page_content).replace("\x00", "\uFFFD"),
                    metadata=split.metadata,
                )
                _document_ids.append(_doc_id)
                _documents.append(_new_doc)
            del subset_splits

            with ThreadPoolExecutor(max_workers=8) as executor:  # Increased number of workers
                futures = [executor.submit(self.db.add_documents, _documents, ids=_document_ids)]
                for future in as_completed(futures):
                    try:
                        future.result()
                    except Exception as e:
                        logger.error(f"Exception occurred while adding documents: {e}")
            del _documents
            subset_start_ind += split_by
            sleep(sleep_interval_between_splits)

    def delete_document(self, document_id: UUID):
        """Delete document from the collection.

        Args:
            document_id (str): Document identifier.
        """
        self.db.delete(ids=[document_id])

    def update_document(self, document_id: UUID, content: str, metadata: dict) -> None:
        """Update document in the collection.
        This workaround deletes the existing document and adds a new one with the updated content and metadata.

        Args:
            document_id: Document identifier.
            content: New content.
            metadata: New metadata.
        """
        self.delete_document(document_id)
        updated_doc = Document(page_content=content, metadata=metadata)
        self.db.add_documents([updated_doc], ids=[document_id])

    def get_relevant_documents(self, query: str) -> list:
        """Retrieve relevant documents from the collection.

        Args:
            query: question.
        """
        return self.retriever.get_relevant_documents(query)

    def get_documents_with_embeddings(self):
        """Retrieves all documents along with their embeddings and metadata from the database.

        This method queries the database to fetch every document stored within, alongside their respective embeddings
        and metadata. The embeddings represent the vector representations of the document contents, which can be used
        for various machine learning and natural language processing tasks.

        Returns:
            documents_with_embeddings (list of dict): A list where each element is a dictionary containing a document's
            text (`document`), its embedding vector (`embedding`), and any associated metadata (`metadata`).

        Example:
            [
                {
                    "document": "Sample document text.",
                    "embedding": [0.1, 0.2, 0.3],
                    "cmetadata": {"author": "Author Name"}
                },
                ...
            ]
        """
        from sqlalchemy.orm import sessionmaker

        engine = create_engine(str(settings.pgvector_url))
        SessionLocal = sessionmaker(bind=engine)
        with SessionLocal() as session:
            documents = session.query(self.db.EmbeddingStore).all()
            return [self.serialize_embedding_store(doc, embedding=True) for doc in documents]

    def get_documents(self, collection_id: Optional[UUID] = None, document_id: Optional[UUID] = None):
        """Retrieve documents directly from the *vector* database.

        We cannot rely on the Query-Service primary DB session because vector data lives in the separate
        `postgres_indexer` instance (see settings.pgvector_url). Therefore we open a *dedicated* read-only
        SQLAlchemy engine pointing at that URL and issue raw SQL.

        Args:
            collection_id (Optional[str]): Filter by collection ID.
            document_id (Optional[str]): Filter by document ID.

        Returns:
            documents (list of str): A list containing the textual content of each document stored in the database.

        Example:
            ["First document text.", "Second document text.", ...]
        """
        # ----------------------------------------------------
        from query_app.settings import settings

        engine = create_engine(str(settings.pgvector_url))

        sql = "SELECT id, collection_id, document, cmetadata FROM langchain_pg_embedding"
        clauses = []
        params = {}
        if collection_id is not None:
            clauses.append("collection_id = :cid")
            params["cid"] = str(collection_id)
        if document_id is not None:
            clauses.append("id LIKE :did")
            params["did"] = f"{document_id}%"
        if clauses:
            sql += " WHERE " + " AND ".join(clauses)

        with engine.connect() as conn:
            rows = conn.execute(text(sql), params).fetchall()

        # map rows to expected dict structure
        return [
            {
                "collection_id": row[1],
                "document": row[2],
                "cmetadata": row[3],
                "id": row[0],
            }
            for row in rows
        ]

    def get_collection(self):
        """Retrieve current collection metadata."""
        with db_context() as db_session:
            return self.db.get_collection(db_session)

    def get_collections(self):
        """Return a list of all collections available in the vector store."""
        with db_context() as db_session:
            documents = db_session.query(self.db.CollectionStore).all()
            return [self.serialize_collection_store(doc) for doc in documents]

    def azure_ai_web_search(self, query: str, k: int = 4) -> List[Tuple[Document, float]]:
        """Perform Azure AI Foundry enhanced web search.

        Args:
            query (str): Query string for Azure AI web search.
            k (int): The number of top results to retrieve.

        Returns:
            List[Tuple[Document, float]]: A list of Document objects with their scores.
        """
        try:
            from query_app.services.azure_data_web_service import AzureDataWebService
            from query_app.settings import settings

            # Get initial internal results using VectorDB's similarity search
            internal_docs = self.db.similarity_search_with_score(query, k=k * 2)
            docs_only = [doc for doc, _ in internal_docs]

            # Initialize Azure Data Web search service
            azure_search = AzureDataWebService(
                decision_model_name=settings.web_search_decision_model,
                azure_endpoint=settings.azure_ai_endpoint,
                deployment=settings.web_search_decision_deployment,
                azure_api_key=settings.azure_ai_api_key,
                search_max_results=settings.web_search_max_results,
                api_version=settings.web_search_api_version,
            )

            # Check if web search is needed
            if azure_search.is_need_web_search(query, docs_only):
                logger.info("Azure AI determined web search is needed")
                web_docs = azure_search.run_web_search(query)

                # Combine web results with internal results
                web_results = [(doc, 1.0) for doc in web_docs]
                combined_results = web_results + internal_docs[:k]

                return combined_results[:k]
            else:
                logger.info("Azure AI determined internal documents are sufficient")
                return internal_docs[:k]

        except Exception as e:
            logger.error(f"Azure AI web search failed: {e}, falling back to similarity search")
            return self.db.similarity_search_with_score(query, k=k)
