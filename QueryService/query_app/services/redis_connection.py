"""Orchestrator module for Dramatiq + RabbitMQ + Redis (verbose log edition)"""

import logging

import redis
from query_app.settings import settings
from redis.exceptions import ConnectionError as RedisConnectionError

logger = logging.getLogger(__name__)


# --------------------------------------------------------------------------- #
# Singleton broker holder
# --------------------------------------------------------------------------- #
class RedisBroker:
    """Singleton class to hold the Redis client for query history storage."""

    _instance = None

    def __new__(cls):
        """Ensure only one instance of RedisBroker exists."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._init()
        return cls._instance

    # --------------- Construction helpers ---------------------------------- #
    def _init(self) -> None:
        """Initialize the Redis client for query history storage."""
        logger.info("initializing-redis-broker")
        self.redis_client = self._setup_redis()

    # --------------- Redis ------------------------------------------------- #
    def _setup_redis(self) -> redis.Redis:
        """Set up the Redis client with the specified parameters."""
        pool = redis.ConnectionPool(
            host=settings.redis_host,
            port=settings.redis_port,
            db=settings.redis_query_history_db,
            max_connections=10,
            socket_timeout=5,
            retry_on_timeout=True,
            health_check_interval=30,
        )
        try:
            client = redis.Redis(connection_pool=pool)
            client.ping()
            return client
        except RedisConnectionError as e:
            logger.error("redis-connection-error", exc_info=e)
            raise


# --------------------------------------------------------------------------- #
# module-level singletons
# --------------------------------------------------------------------------- #
redis_broker = RedisBroker()
redis_conn = redis_broker.redis_client
