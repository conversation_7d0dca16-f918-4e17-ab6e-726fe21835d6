import json
import logging
import re
from collections import defaultdict
from typing import Any, Callable, Dict, List, Optional, Tuple

from elasticsearch import Elasticsearch
from langchain.schema import Document
from query_app.services.vectordb import VectorDB
from query_app.settings import settings
from rank_bm25 import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sentence_transformers import CrossEncoder

_CROSS_ENCODER_CACHE: Dict[str, "CrossEncoder"] = {}  # populated lazily via preload_models

logger = logging.getLogger(__name__)


class SearchEngine:
    """Encapsulates vector / keyword / hybrid retrieval strategies."""

    def __init__(
        self,
        vdb: VectorDB,
        *,
        default_method: str = "similarity",
        top_k: Optional[int] = None,
    ) -> None:
        """Initialize the SearchEngine with a VectorDB instance and set up search methods."""
        self.vdb = vdb
        self.top_k = top_k or vdb.top_k
        self.method_dispatcher: Dict[str, Callable[..., Any]] = {
            "similarity": self._similarity_search,
            "mmr": self._max_marginal_relevance_search_with_score_by_vector,
            "keyword": self._keyword_search,
            "hybrid": self._hybrid_search,
            "bm25": self._bm25_search,
            "fused": self._fused_search,
            "rerank": self._cross_encoder_rerank,
            "elastic search": self._elastic_bm25_search if Elasticsearch and settings.elastic_enabled else self._bm25_search,
            "azure_ai_web": self._azure_ai_web_search,
        }
        if default_method not in self.method_dispatcher:
            raise ValueError(f"Unknown search method: {default_method}")
        self.default_method = default_method

        # ---------------- internal caches ----------------
        self._bm25_index: Optional[BM25Okapi] = None  # built lazily
        self._tokenised_corpus: List[List[str]] = []  # mirrors vdb docs for BM25
        self._bm25_corpus: List[str] = []  # raw text strings aligned with tokenised_corpus
        self._bm25_meta: List[dict] = []  # metadata aligned with corpus (initialised lazily)

        # Elastic client (optional)
        self._es: Optional[Elasticsearch] = None
        if Elasticsearch and settings.elastic_enabled:
            try:
                self._es = Elasticsearch(settings.elastic_url, request_timeout=10)
            except Exception:
                self._es = None

    # ------------------------------------------------------------
    # Public API
    # ------------------------------------------------------------
    def search(self, query: str, *, k: Optional[int] = None, method: Optional[str] = None):
        """Perform a search in the collection using the specified method."""
        fn = self.method_dispatcher[method or self.default_method]
        return fn(query, k=k or self.top_k)

    # ------------------------------------------------------------
    # Concrete search implementations
    # ------------------------------------------------------------
    def _similarity_search(self, query: str, k: int = 4) -> List[Tuple[Document, float]]:
        """Dense-vector *top-k* retrieval (cosine / inner-product).

        Steps
        -----
        1. The query string is embedded with the same embedding model that was
           used for indexing the corpus.
        2. We delegate to PGVector / FAISS via LangChain's
           ``similarity_search_with_score`` wrapper which performs an exact
           nearest-neighbour search (no ANN).  The store returns the best *k*
           passages sorted by similarity.

        Parameters
        ----------
        query : str
            Natural-language question / phrase.
        k : int, default ``4``
            Number of passages to return.

        Returns
        -------
        List[Tuple[langchain.schema.Document, float]]
            Each tuple is *(document, similarity_score)* where the score is
            already normalised by the vector DB backend (cosine distance →
            higher is better).
        """
        # Perform similarity search and return results with scores
        docs_with_score = self.vdb.db.similarity_search_with_score(query, k=k or self.top_k)
        return docs_with_score

    def _max_marginal_relevance_search_with_score_by_vector(self, query: str, k: int = 4, fetch_k: int = 20, lambda_mult: float = 0.5) -> List[Tuple[Document, float]]:
        """Dense retrieval with **Maximal Marginal Relevance (MMR)**.

        Idea: prefer passages that are *both* similar to the query *and* diverse
        w.r.t. each other.  Good for chat assistants where redundancy hurts.

        Implementation details
        ----------------------
        • Embed the query → vector ``q``.
        • Fetch a wider candidate set (``fetch_k``) using plain similarity.
        • Greedy MMR selection picks ``k`` passages that maximise
          ``λ·sim(q, d) − (1-λ)·max_j sim(d, d_j)``.

        Parameters
        ----------
        query : str
            Natural-language question / phrase used to search the corpus.
        k : int
            Final number of passages.
        fetch_k : int
            Candidate pool size before MMR.
        lambda_mult : float in ``[0,1]``
            ``1`` → pure similarity, ``0`` → pure diversity.
        """
        embedding = self.vdb.embedding_model.embed_query(query)
        docs_with_score = self.vdb.db.max_marginal_relevance_search_with_score_by_vector(embedding, k=k, fetch_k=fetch_k, lambda_mult=lambda_mult)
        return docs_with_score

    def _keyword_search(self, query: str, k: int = 4) -> List[Tuple[Document, float]]:
        """Naïve case-insensitive keyword scan over the raw corpus.

        Cheap baseline that requires **no embeddings**.  Mainly useful when the
        query contains rare proper nouns that dense models may miss, or as part
        of hybrid fusion.
        """
        keywords = query.split()
        docs_with_embeddings = self.vdb.get_documents_with_embeddings()
        results = []
        for doc_dict in docs_with_embeddings:
            text = doc_dict["document"]
            meta = doc_dict["cmetadata"]
            if isinstance(meta, str):
                try:
                    meta = json.loads(meta)
                except Exception:
                    pass
            if any(re.search(keyword, text, re.IGNORECASE) for keyword in keywords):
                results.append((Document(page_content=text, metadata=meta), 1.0))
        return results[:k]

    def _hybrid_search(self, query: str, k: int = 4) -> List[Tuple[Document, float]]:
        """*Score-level* fusion of similarity, MMR and keyword signals.

        Each individual method returns *(doc, score)*.  We convert the document
        to a hashable key ``(content, frozenset(metadata))`` and **sum** the
        scores coming from different retrieval styles.  The final ranking is
        the descending combined score.

        NB:  Because raw scores live on different scales this is only meant as
        a quick heuristic.  For production consider min-max or Z-score
        normalisation.
        """
        # Perform individual searches using different methods
        vector_results = self._similarity_search(query, k=k)
        mmr_results = self._max_marginal_relevance_search_with_score_by_vector(query, k=k)
        keyword_results = self._keyword_search(query)

        # Combine all results
        all_results = vector_results + mmr_results + keyword_results

        # Rank and combine results from all search methods
        combined_results: Dict[tuple, float] = defaultdict(float)

        # Aggregate scores from all methods using the document's content and metadata as keys
        for doc, score in all_results:
            key = (doc.page_content, frozenset(doc.metadata.items()))
            combined_results[key] += score

        # Sort by combined score
        ranked_results = sorted(combined_results.items(), key=lambda item: item[1], reverse=True)

        # Convert back to a list of Document objects with their scores
        final_results = [(Document(page_content=key[0], metadata=dict(key[1])), score) for key, score in ranked_results]

        return final_results

    # ------------------------------------------------------------
    # New search implementations (state-of-the-art additions)
    # ------------------------------------------------------------
    def _bm25_search(self, query: str, k: int = 4) -> List[Tuple[Document, float]]:
        """Classic sparse-term retrieval (**BM25**).

        We tokenise each document into lowercase whitespace-separated tokens and
        instantiate `rank_bm25.BM25Okapi` at call time.  For moderate corpora
        (<100k docs) this is acceptable; for larger sets the index should be
        cached or outsourced to Elastic / OpenSearch.

        Returns raw BM25 relevance scores (higher is better).
        """
        # Lazy-build index if needed
        self._ensure_bm25_index()
        if self._bm25_index is None:
            return []

        query_tokens = query.lower().split()
        scores = self._bm25_index.get_scores(query_tokens)

        # Select top-k indices
        ranked = sorted(enumerate(scores), key=lambda x: x[1], reverse=True)[:k]

        results: List[Tuple[Document, float]] = []
        for idx, score in ranked:
            meta = self._bm25_meta[idx]
            if isinstance(meta, str):
                try:
                    meta = json.loads(meta)
                except Exception:
                    pass
            results.append((Document(page_content=self._bm25_corpus[idx], metadata=meta), float(score)))
        return results

    def _fused_search(self, query: str, k: int = 4, *, alpha: float = 0.5, dense_k: int = 20, sparse_k: int = 50) -> List[Tuple[Document, float]]:
        """Linear fusion of dense cosine score and sparse BM25 score.

        ``score = α * dense + (1-α) * bm25`` where scores missing from one side
        are treated as 0.  We first retrieve a *wider* candidate pool from each
        modality then combine and sort.
        """

        dense_res = self._similarity_search(query, k=dense_k)
        sparse_res = self._bm25_search(query, k=sparse_k)

        # Normalise scores (min-max) to the range [0,1] per modality
        if dense_res:
            max_dense = max(s for _, s in dense_res) or 1.0
            dense_res = [(d, s / max_dense) for d, s in dense_res]

        if sparse_res:
            max_sparse = max(s for _, s in sparse_res) or 1.0
            sparse_res = [(d, s / max_sparse) for d, s in sparse_res]

        combined: Dict[Tuple[str, frozenset], float] = defaultdict(float)

        for doc, score in dense_res:
            key = (doc.page_content, frozenset(doc.metadata.items()))
            combined[key] += alpha * float(score)

        for doc, score in sparse_res:
            key = (doc.page_content, frozenset(doc.metadata.items()))
            combined[key] += (1 - alpha) * float(score)

        # Sort & pick top-k
        ranked = sorted(combined.items(), key=lambda x: x[1], reverse=True)[:k]
        return [(Document(page_content=key[0], metadata=dict(key[1])), score) for key, score in ranked]

    def _cross_encoder_rerank(self, query: str, k: int = 4, *, candidate_k: int = 50, model_name: str = "cross-encoder/ms-marco-MiniLM-L-6-v2") -> List[Tuple[Document, float]]:
        """Second-stage **Cross-Encoder** reranking.

        1. Retrieve ``candidate_k`` documents with the fast dense retriever.
        2. Each ⟨query, doc⟩ pair is scored by a
           *classification-style* Transformer that attends to the full joint
           text (MS-MARCO MiniLM-L6 by default).
        3. Return the best ``k`` by model logits.

        This is slower (O(k·L²)) but yields state-of-the-art relevance.
        """

        # Collect first-pass candidates via dense search
        first_pass_docs = [d for d, _ in self._similarity_search(query, k=candidate_k)]
        if not first_pass_docs:
            return []

        cross_enc = self._get_cross_encoder(model_name)
        pairs = [[query, doc.page_content] for doc in first_pass_docs]
        scores = cross_enc.predict(pairs)

        ranked = sorted(zip(first_pass_docs, scores), key=lambda x: x[1], reverse=True)[:k]
        return [(doc, float(score)) for doc, score in ranked]

    def _azure_ai_web_search(self, query: str, k: int = 4) -> List[Tuple[Document, float]]:
        """Delegate Azure AI web search to VectorDB."""
        return self.vdb.azure_ai_web_search(query, k)

    # ------------------------------------------------------------
    # Static helpers
    # ------------------------------------------------------------
    @staticmethod
    def _get_cross_encoder(model_name: str):
        """Return cached CrossEncoder; load if absent."""
        if model_name not in _CROSS_ENCODER_CACHE:
            try:
                from sentence_transformers import CrossEncoder  # type: ignore
            except ImportError as exc:  # pragma: no cover
                raise ImportError("sentence_transformers missing ‑ add it to requirements.txt") from exc

            _CROSS_ENCODER_CACHE[model_name] = CrossEncoder(model_name)
        return _CROSS_ENCODER_CACHE[model_name]

    # Public utility to warm-up heavy models (call at app startup)
    @classmethod
    def preload_models(cls, *, cross_encoder_model: str = "cross-encoder/ms-marco-MiniLM-L-6-v2") -> None:
        """Download / load heavyweight models so first request is fast."""
        cls._get_cross_encoder(cross_encoder_model)

    # ------------------------------------------------------------
    # Private helpers
    # ------------------------------------------------------------
    def _ensure_bm25_index(self) -> None:
        """(Re)build BM25 index if corpus size has changed.

        Called automatically by `_bm25_search`. Keeps a single in-memory
        instance so repeated BM25 or fused searches are fast.
        """
        docs = self.vdb.get_documents()
        if docs and len(docs) != len(self._tokenised_corpus):
            corpus = [d["document"] for d in docs]
            self._bm25_corpus = corpus
            self._tokenised_corpus = [c.lower().split() for c in corpus]
            self._bm25_index = BM25Okapi(self._tokenised_corpus)
            self._bm25_meta = [d["cmetadata"] for d in docs]

    # ------------------------------------------------------------
    # Elasticsearch BM25
    # ------------------------------------------------------------
    def _elastic_bm25_search(self, query: str, k: int = 4) -> List[Tuple[Document, float]]:
        """BM25 retrieval via Elasticsearch index named after the collection.

        Requires the documents to be indexed externally. Falls back to in-memory
        BM25 if ES is unavailable.
        """
        if not self._es:
            return self._bm25_search(query, k)

        index_name = self.vdb.collection_name
        try:
            response = self._es.search(index=index_name, size=k, query={"match": {"document": query}})
        except Exception as e:
            # Index missing; fall back
            logger.error("*** Elasticsearch response not found ***")
            logger.info("Falling back to in-memory BM25 search")
            return self._bm25_search(query, k)
        hits = response.get("hits", {}).get("hits", [])
        results: List[Tuple[Document, float]] = []
        for hit in hits:
            source = hit.get("_source", {})
            text = source.get("document", "")
            meta = source.get("cmetadata", {})
            if isinstance(meta, str):
                try:
                    meta = json.loads(meta)
                except Exception:
                    pass
            score = float(hit.get("_score", 1.0))
            results.append((Document(page_content=text, metadata=meta), score))
        return results
