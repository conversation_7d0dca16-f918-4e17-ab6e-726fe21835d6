# ---------------------------------------------------------------------------
# Core class
# ---------------------------------------------------------------------------
from __future__ import annotations

"""Reusable re‑ranking helper (vector‑search second stage).

Supports **three** strategies:
1. SMALL_LLM  – point‑wise scoring with a cheap chat model (default).
2. SELF_RERANK – list‑wise ordering with a larger model.
3. CROSS_ENCODER –  bi‑encoder/cross‑encoder from sentence‑transformers.

Drop‑in replacement for <PERSON><PERSON>hai<PERSON> retrievers via ``ReRankRetriever``.
"""

from typing import List, Optional, Sequence, Tuple, cast

from langchain.llms.base import BaseLLM
from langchain.prompts import PromptTemplate
from langchain.schema import BaseMessage, Document
from langchain_core.callbacks import (
    AsyncCallbackManagerForRetrieverRun,
    CallbackManagerForRetrieverRun,
)
from langchain_core.retrievers import BaseRetriever
from query_app.enums.rank import ReRankStrategy
from query_app.llm.enums import OPENAI_MODELS, ReRankLLMModels
from query_app.llm.models import MODEL_PRESETS

# Uncomment if you want the cross‑encoder strategy
# import torch
# from sentence_transformers import CrossEncoder


__all__ = ["ReRank", "ReRankRetriever"]

# ---------------------------------------------------------------------------
# Constants & prompts
# ---------------------------------------------------------------------------
FIRST_PASS_POOL_MULT: int = 8  # how many candidates per k for first stage
TIEBREAKER_USE_LEN: bool = True  # use chunk length as 2nd sort key
DEFAULT_CE_MODEL = "BAAI/bge-reranker-base"

SMALL_LLM_PROMPT = PromptTemplate.from_template(
    """You are an expert relevance grader.

Question: {question}

Candidate chunk:
{chunk}

### Instruction
Return **one** relevance score on its own line, nothing else.
The score MUST be a number from 0 (irrelevant) to 10 (perfect). Decimals allowed.

### Output
"""
)

SELF_RERANK_PROMPT = PromptTemplate.from_template(
    """Below are {k} context chunks retrieved for a question.
Rank them **from best (0) to worst ({k_minus_1})** according to how well they help answer the question.
Return *only* a comma‑separated list of zero‑based indices.

Question: {question}

Contexts:
{contexts}

Answer:"""
)


# ---------------------------------------------------------------------------
# Core class
# ---------------------------------------------------------------------------
class ReRank:
    """Second‑stage re‑ranking.

    Parameters
    ----------
    strategy : ReRankStrategy
        Which algorithm to use (default SMALL_LLM).
    small_llm / big_llm : BaseLLM
        Chat models used for SMALL_LLM and SELF_RERANK.
    """

    def __init__(
        self,
        strategy: ReRankStrategy = ReRankStrategy.SMALL_LLM,
        *,
        small_llm_model_name: str | None = ReRankLLMModels.GPT4OMINI.value,
        big_llm_model_name: str | None = OPENAI_MODELS.GPT4O.value,
    ) -> None:
        """Re‑ranker constructor."""

        self.strategy = strategy
        self.small_llm: BaseLLM = MODEL_PRESETS[small_llm_model_name].chat_model
        self.big_llm: BaseLLM = MODEL_PRESETS[big_llm_model_name].chat_model

    # ---------------------------------------------------------------------
    def __call__(
        self,
        question: str,
        docs: Sequence[Document],
        *,
        top_k: int = 5,
        return_scores: bool = False,
    ) -> List[Document] | Tuple[List[Document], List[float]]:
        """Re‑rank *docs* for *question* and return the best *top_k*."""
        if not docs:
            return ([], []) if return_scores else []

        ranked_docs: List[Document] = []
        ranked_scores: List[float] = []

        # -------- 1. SMALL_LLM ------------------------------------------------
        if self.strategy is ReRankStrategy.SMALL_LLM:
            scores = self._small_llm_scores(question, docs)
            ranked_docs, ranked_scores = self._sort_by_score(docs, scores, top_k)

        # -------- 2. SELF_RERANK ---------------------------------------------
        elif self.strategy is ReRankStrategy.SELF_RERANK:
            order = self._self_rerank_order(question, docs)[:top_k]
            ranked_docs = [docs[i] for i in order]
            ranked_scores = [1.0] * len(ranked_docs)

        if return_scores:
            return ranked_docs, ranked_scores
        else:
            return ranked_docs

    # ------------------------------------------------------------------
    # Internal helpers
    # ------------------------------------------------------------------
    def _small_llm_scores(self, question: str, docs: Sequence[Document]) -> List[float]:
        """Batch score with the chat model.
        LangChain's `ChatModel.generate` expects **List[List[BaseMessage]]**.
        We wrap each prompt in a single `HumanMessage` so it fits that API.
        """
        from langchain.schema import (
            HumanMessage,  # local import to avoid circular issues
        )

        prompt_msgs = [[HumanMessage(content=SMALL_LLM_PROMPT.format(question=question, chunk=d.page_content[:4096]))] for d in docs]

        # one deterministic request; `generations` aligns with input order
        resp = self.small_llm.generate(prompt_msgs, temperature=0)
        scores: List[float] = []
        for gen in resp.generations:
            raw = self._get_text(gen[0])
            try:
                scores.append(float(raw))
            except Exception:
                scores.append(0.0)
        return scores

    def _self_rerank_order(self, question: str, docs: Sequence[Document]) -> List[int]:
        ctx = "\n".join(f"[{i}] {d.page_content}" for i, d in enumerate(docs))
        prompt = SELF_RERANK_PROMPT.format(question=question, k=len(docs), k_minus_1=len(docs) - 1, contexts=ctx)
        raw = self._get_text(self.big_llm.invoke(prompt))
        idx = [int(i) for i in raw.split(",") if i.strip().isdigit() and int(i) < len(docs)]
        return idx or list(range(len(docs)))

    # ------------------------------------------------------------------
    @staticmethod
    def _sort_by_score(docs: Sequence[Document], scores: List[float], k: int) -> Tuple[List[Document], List[float]]:
        """Sort by score (desc) + tie‑breakers."""

        from typing import Any, List, Tuple

        Zipped3 = Tuple[float, Document, int]
        Zipped4 = Tuple[float, Document, int, int]

        zipped_base: List[Zipped3] = list(zip(scores, docs, range(len(docs))))

        def sort_key_len(x: Zipped4) -> Tuple[float, int, int]:
            return -x[0], -x[3], x[2]

        def sort_key_default(x: Zipped3) -> Tuple[float, int]:
            return -x[0], x[2]

        sorted_items: List[Any]

        if TIEBREAKER_USE_LEN:
            zipped_with_len = [(s, d, idx, len(d.page_content)) for s, d, idx in zipped_base]
            sorted_items = sorted(zipped_with_len, key=sort_key_len)[:k]
        else:
            sorted_items = sorted(zipped_base, key=sort_key_default)[:k]

        return [item[1] for item in sorted_items], [item[0] for item in sorted_items]

    @staticmethod
    def _get_text(msg: str | BaseMessage) -> str:
        if isinstance(msg, BaseMessage):
            return msg.content
        return str(msg).strip()


# ---------------------------------------------------------------------------
# Wrapper retriever
# ---------------------------------------------------------------------------
class ReRankRetriever(BaseRetriever):
    """Wrap an existing retriever with second‑stage re‑ranking."""

    base_retriever: BaseRetriever
    reranker: Optional[ReRank] = None
    final_k: int = 4

    # ---------------- sync ----------------
    def _get_relevant_documents(
        self,
        query: str,
        *,
        run_manager: CallbackManagerForRetrieverRun | None = None,
    ) -> List[Document]:
        docs = self.base_retriever.get_relevant_documents(query)
        if not self.reranker:
            return docs[: self.final_k]
        return cast(List[Document], self.reranker(query, docs, top_k=self.final_k, return_scores=False))

    # --------------- async ----------------
    async def _aget_relevant_documents(
        self,
        query: str,
        *,
        run_manager: AsyncCallbackManagerForRetrieverRun | None = None,
    ) -> List[Document]:
        return self._get_relevant_documents(query, run_manager=run_manager)
