import logging
from typing import List, Literal

from langchain import PromptTemplate
from langchain.schema import Document, HumanMessage, SystemMessage
from langchain_community.tools import DuckDuckGoSearchResults, DuckDuckGoSearchRun
from query_app.llm.models import MODEL_PRESETS

logger = logging.getLogger(__name__)


class WebService:
    """Encapsulates all DuckDuckGo-based web search logic, supports both DuckDuckGoSearchRun (raw text) and DuckDuckGoSearchResults (structured data)."""

    def __init__(self, decision_model_name: str, search_max_results: int = 5, ddg_type: Literal["run", "results"] = "results"):
        """Initializes the web service with a decision model and search parameters.
        Args:
            decision_model_name: key/name of the chat model to use for decision.
            search_max_results: maximum number of web hits to fetch.
            ddg_type: which DuckDuckGo tool to use:
                - 'run': returns raw text (DuckDuckGoSearchRun)
                - 'results': returns structured list of dicts (DuckDuckGoSearchResults)
                  (default)
        """
        # Chat model for decision-making
        self.decision_model = MODEL_PRESETS[decision_model_name].chat_model
        self.search_max_results = search_max_results
        self.ddg_type = ddg_type

        # Initialize both tools; we'll pick based on `ddg_type`
        self.ddg_run = DuckDuckGoSearchRun()
        self.ddg_results = DuckDuckGoSearchResults(output_format="list")

        # Prompt template for LLM decision
        self.decision_prompt = PromptTemplate.from_template(
            "You are an assistant that decides if external web search is needed.\n\n"
            "Below are snippets from internal documents:\n{context}\n\n"
            "User question:\n{question}\n\n"
            "Decide if the user's question can be answered using only the information in the snippets.\n"
            "If the information in the snippets is unrelated, incomplete, or insufficient to answer the question, reply 'yes'.\n"
            "Otherwise, reply 'no'.\n\n"
            "Reply with exactly 'yes' or 'no'."
        )

    def is_need_web_search(self, query: str, docs: List[Document], document_char_size: int = 1500) -> bool:
        """Returns True if the LLM, when shown `docs`, says a web search is needed."""
        try:
            # Create a simple context from the first 1500 chars of each doc
            logger.info("Deciding if web search is needed....")
            snippets = [d.page_content[:document_char_size].replace("\n", " ") for d in docs]
            context = "\n".join(f"- {s}" for s in snippets) or "No docs available."

            # Build and send the decision prompt
            prompt = self.decision_prompt.format(context=context, question=query)
            response = self.decision_model([SystemMessage(content="Decide if web search is needed."), HumanMessage(content=prompt)])
            answer = response.content.strip().lower()
            logger.info(f"The decision is: {answer=}")
            return answer.startswith("yes")
        except Exception as e:
            logger.error("Error during decision-making for web search", exc_info=e)
            return False

    def run_web_search(self, query: str) -> List[Document]:
        """Perform the DuckDuckGo search based on selected backend,
        convert hits into Document objects with metadata.
        """
        try:
            docs: List[Document] = []
            if self.ddg_type == "results":
                logger.info("Running DuckDuckGo web search...")
                # Structured results: each hit is a dict with 'link', 'snippet', 'title'
                raw_hits = self.ddg_results.invoke(query)[: self.search_max_results]
                for hit in raw_hits:
                    docs.append(Document(page_content=hit.get("snippet", ""), metadata={"source": hit.get("link", ""), "title": hit.get("title", "")}))
            else:
                # Raw text: need to parse out links manually if desired
                raw_text = self.ddg_run.invoke(query)
                docs.append(Document(page_content=raw_text, metadata={"source": None, "title": "DuckDuckGo raw text"}))
            return docs
        except Exception as e:
            logger.error("Error during web search", exc_info=e)
            return []
