"""Utility functions for encryption and FastAPI authentication header construction."""

from __future__ import annotations

import base64
import logging

from Crypto.Cipher import AES
from fastapi import HTT<PERSON>Exception
from query_app.settings import settings

logger = logging.getLogger(__name__)


def encrypt_email(email: str) -> str:
    """Encrypt an email address using AES-ECB with PKCS7-like padding.

    Args:
        email (str): The plain email address to encrypt.

    Returns:
        str: The base64-url encoded encrypted email.
    """
    hash_key = settings.hash_key
    key_bytes = base64.urlsafe_b64decode(hash_key)
    BS = 16

    pad = lambda s: s + (BS - len(s) % BS) * chr(BS - len(s) % BS)
    cipher = AES.new(key_bytes, AES.MODE_ECB)
    padded_email = pad(email)
    encrypted = cipher.encrypt(padded_email.encode("utf-8"))
    return base64.urlsafe_b64encode(encrypted).decode("utf-8")


def decrypt_email(encrypted_email: str) -> str:
    """Decrypt an AES-ECB encrypted email back to plain text.

    Args:
        encrypted_email (str): The base64-url encoded encrypted email.

    Returns:
        str: The decrypted plain email address.
    """
    hash_key = settings.hash_key
    key_bytes = base64.urlsafe_b64decode(hash_key)
    unpad = lambda s: s[: -ord(s[-1])]
    cipher = AES.new(key_bytes, AES.MODE_ECB)
    decoded = base64.urlsafe_b64decode(encrypted_email)
    decrypted = cipher.decrypt(decoded).decode("utf-8")
    return unpad(decrypted)


def create_headers(auth):
    """Create headers for FastAPI requests based on the authentication source.

    Args:
        auth: Authentication object or dictionary.

    Returns:
        dict: A dictionary of HTTP headers.

    Raises:
        HTTPException: If authentication token is missing or invalid.
    """
    if isinstance(auth, dict):
        is_admin = auth.get("user") == "admin" and auth.get("role") == "admin_ui"
        local_dev = auth.get("user") == "developer" and auth.get("role") == "dev_local"
        headers = {"X-Admin-UI": "true"} if is_admin or local_dev else {}
        return headers
    elif not isinstance(auth, dict) and not auth.access_token:
        raise HTTPException(status_code=401, detail="Unauthorized")
    else:
        return {"Authorization": f"Bearer {auth.access_token}"}
