from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field
from query_app.enums.search import SearchMethod


# ---- Feedback Request ----
class AddFeedbackRequest(BaseModel):
    """Model for adding feedback to a query."""

    query_id: Optional[str] = Field(None, description="feedback query id")
    feedback_type: str
    feedback_value: str
    feedback_text: Optional[str] = Field(None, description="feedback extra description")
    status: Optional[str] = Field(None, description="feedback status")


# ---- Query Request ----
class QueryRequest(BaseModel):
    """Model for creating a new query."""

    request_id: Optional[str] = Field(None, description="request id")
    content: str
    custom_prompt: Optional[str] = Field(None, description="custom prompt")
    status: Optional[str] = Field(None, description="query status")
    stream: Optional[bool] = Field(None, description="stream on or off")
    search_method: SearchMethod = Field(
        None,
        description="search method [default: similarity, options: similarity, mmr, keyword, hybrid]",
    )
    session_id: Optional[str] = Field(None, description="session id")
    inline_pdf_content: Optional[str] = Field(
        None,
        description="inline pdf content, when a pdf is uploaded just for the query",
    )
    folder_name: Optional[str] = Field(None, description="folder name")
    markdown_support_on: Optional[bool] = Field(False, description="markdown support on or off")
    top_k: Optional[int] = Field(5, description="top k results to return [default: 5]")


class QueryUpdateRequest(BaseModel):
    """Model for updating an existing query."""

    request_id: Optional[str] = Field(None, description="request id")
    query_id: Optional[str] = Field(None, description="query id")
    custom_prompt: Optional[str] = Field(None, description="custom prompt")
    status: Optional[str] = Field(None, description="query status")
    stream: Optional[bool] = Field(None, description="stream on or off")
    search_method: Optional[SearchMethod] = Field(
        SearchMethod.SIMILARITY,
        description="search method [default: similarity, options: similarity, mmr, keyword, hybrid]",
    )
    session_id: Optional[str] = Field(None, description="session id")
    inline_pdf_content: Optional[str] = Field(
        None,
        description="inline pdf content, when a pdf is uploaded just for the query",
    )
    adjust_type: Optional[str] = Field(
        None,
        description="adjust type, options: more_detail, less_detail, shorter_answer, longer_answer",
    )


# ---Sessions ----
class CreateSessionRequest(BaseModel):
    """Model for creating a new user session."""

    session_goal: Optional[str] = ""


class UpdateSessionRequest(BaseModel):
    """Model for updating user session information."""

    title: Optional[str] = None
    custom_config: Optional[dict] = None
    uploaded_pdfs: Optional[str] = None


class UserSessionUpdate(BaseModel):
    """Model for updating user session information."""

    user_email: Optional[EmailStr]
    title: Optional[str]
    uploaded_pdfs: Optional[str]


# ---- Template Request Models ----


class PlaceholderDefinition(BaseModel):
    """Model for placeholder definition in templates."""

    placeholder_type: str = Field(..., description="The placeholder name (e.g., 'name', 'date')")
    description: str = Field(..., description="Description of what this placeholder represents")
    default_value: str = Field("", description="Default value for the placeholder")
    is_required: bool = Field(True, description="Whether this placeholder is required")


class CreateTemplateRequest(BaseModel):
    """Request model for creating a new prompt template."""

    title: str = Field(..., description="Template title")
    description: Optional[str] = Field(None, description="Template description")
    template_content: str = Field(..., description="Template content with placeholders like {name}, {date}")
    use_case: str = Field(..., description="Use case category (SYSTEM, PERSONAL, etc.)")
    llms: Optional[List[str]] = Field(None, description="List of supported LLMs")
    placeholders: Optional[List[PlaceholderDefinition]] = Field(None, description="List of placeholder definitions")
    collection_id: Optional[UUID] = Field(None, description="Collection ID for collection-specific templates")


class UpdateTemplateRequest(BaseModel):
    """Request model for updating an existing prompt template."""

    title: Optional[str] = Field(None, description="Template title")
    description: Optional[str] = Field(None, description="Template description")
    template_content: Optional[str] = Field(None, description="Template content with placeholders")
    use_case: Optional[str] = Field(None, description="Use case category")
    llms: Optional[List[str]] = Field(None, description="List of supported LLMs")
    placeholders: Optional[List[PlaceholderDefinition]] = Field(None, description="List of placeholder definitions")
    collection_id: Optional[UUID] = Field(None, description="Collection ID for collection-specific templates")


class RenderTemplateRequest(BaseModel):
    """Request model for rendering a template with placeholder values."""

    placeholder_values: dict = Field(..., description="Dictionary of placeholder values")


class SearchTemplatesRequest(BaseModel):
    """Request model for searching templates."""

    search_term: str = Field(..., description="Search term to filter templates")
    use_case: Optional[str] = Field(None, description="Filter by use case")
    collection_id: Optional[UUID] = Field(None, description="Filter by collection ID")


# ---- Slash Commands Request ----
class SlashCommandRequest(BaseModel):
    """Model for executing a slash command."""

    command: str = Field(..., description="The slash command to execute, including the command name and any arguments (e.g., '/help' or '/websearch query text')", example="/help")


class SlashCommandRegistrationRequest(BaseModel):
    """Model for registering a new slash command."""

    name: str = Field(..., description="The name of the command (without the leading slash)", example="echo", min_length=1, max_length=50)
    description: str = Field(..., description="A brief description of what the command does", example="Echo back the input text", min_length=1, max_length=200)
    requires_args: bool = Field(False, description="Whether this command requires arguments to function properly")
    arg_description: Optional[str] = Field(None, description="Usage instructions for commands that require arguments", example="Usage: /echo <text>")
