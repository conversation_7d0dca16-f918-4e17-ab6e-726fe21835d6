from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field
from query_app.enums.feedback import FeedbackStatusEnum


class MessageResponse(BaseModel):
    """A simple response model that contains a message."""

    message: Optional[str]


class AddFeedbackResponse(BaseModel):
    """Response model for adding feedback."""

    feedback_id: UUID
    status: str


class FeedbackItem(BaseModel):
    """Represents a single feedback item."""

    id: UUID
    query_id: Optional[UUID]
    collection_id: Optional[UUID]
    feedback_type: str
    feedback_value: str
    feedback_text: Optional[str]
    status: FeedbackStatusEnum
    created_at: datetime
    last_updated: datetime

    class Config:
        """Configurations for the FeedbackItem model."""

        from_attributes = True


class GetFeedbackStatusResponse(BaseModel):
    """Response model for getting feedback status."""

    status: FeedbackStatusEnum


class GetFeedbacksResponse(BaseModel):
    """Response model for getting feedbacks."""

    feedbacks: list


# ---- Query Response ----
class QueryResponse(BaseModel):
    """Response model for a query."""

    query_id: Optional[str] = Field(None, description="query id")
    content: Optional[str] = Field(None, description="query content")
    retrieved_docs: Optional[dict] = Field(None, description="docs")
    message: Optional[str] = Field(None, description="query message")
    session_id: Optional[str] = Field(None, description="session id")


class QueryRelevantDocsResponse(BaseModel):
    """Response model for relevant documents in a query."""

    docs: Optional[List] = Field(None, description="relevant docs")
    message: Optional[str] = Field(None, description="query message")


class GetQueriesResponse(BaseModel):
    """Response model for getting queries."""

    queries: list


# ---- prompts Response ----


class Prompt(BaseModel):
    """Represents a prompt in the system."""

    id: str
    content: str
    description: Optional[str] = None
    user_id: Optional[str] = None
    use_case: Optional[str] = None
    title: Optional[str] = None
    created_at: datetime


# ---- Template Response Models (NEW) ----


class PlaceholderDefinitionResponse(BaseModel):
    """Response model for placeholder definition."""

    placeholder_type: str
    description: str
    default_value: str
    is_required: bool


class PromptTemplateResponse(BaseModel):
    """Response model for prompt template."""

    id: str
    title: str
    description: Optional[str] = None
    template_content: str
    use_case: str
    llms: List[str] = []
    placeholders: List[PlaceholderDefinitionResponse] = []
    user_id: Optional[str] = None
    collection_id: Optional[str] = None
    created_at: datetime
    last_updated: Optional[datetime] = None


class TemplateListResponse(BaseModel):
    """Response model for list of templates."""

    data: List[PromptTemplateResponse]
    total: Optional[int] = None
    page: int = 1
    per_page: int = 50


class RenderedTemplateResponse(BaseModel):
    """Response model for rendered template."""

    template_id: str
    rendered_content: str
    placeholder_values: dict


class TemplateUsageResponse(BaseModel):
    """Response model for template usage tracking."""

    id: str
    template_id: str
    user_id: str
    rendered_content: str
    placeholder_values: Optional[dict] = None
    created_at: datetime


# ---- Slash Commands Response ----
class SlashCommandResponse(BaseModel):
    """Response model for executing a slash command."""

    success: bool = Field(..., description="Whether the command executed successfully")
    command: str = Field(..., description="The name of the command that was executed", example="help")
    result: dict = Field(..., description="The result of the command execution, typically containing a 'message' field", example={"message": "Command executed successfully"})


class SlashCommandInfo(BaseModel):
    """Information about a single slash command."""

    name: str = Field(..., description="The name of the command (without the leading slash)", example="help")
    description: str = Field(..., description="A brief description of what the command does", example="Show available commands and their descriptions")
    requires_args: bool = Field(..., description="Whether this command requires arguments")
    arg_description: Optional[str] = Field(None, description="Usage instructions for commands that require arguments", example="Usage: /websearch <query>")


class SlashCommandListResponse(BaseModel):
    """Response model for listing available slash commands."""

    commands: List[SlashCommandInfo] = Field(..., description="List of available slash commands with their details")
