"""API endpoints for prompt template management."""

import logging
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from query_app.api.models.request import (
    CreateTemplateRequest,
    RenderTemplateRequest,
    UpdateTemplateRequest,
)
from query_app.api.models.response import (
    MessageResponse,
    PromptTemplateResponse,
    RenderedTemplateResponse,
    TemplateListResponse,
)
from query_app.api.utils.helpers import encrypt_email
from query_app.service_clients.prompt_service_client.prompt_requests import (
    PromptOrchestrationClient,
)
from query_app.services.auth import get_auth
from query_app.settings import settings

logger = logging.getLogger(__name__)
router = APIRouter()
prompt_service_client = PromptOrchestrationClient(settings.prompt_base_url)


@router.post("/templates", response_model=PromptTemplateResponse)
async def create_template(
    template_request: CreateTemplateRequest,
    auth=Depends(get_auth),
):
    """Create a new prompt template."""
    try:
        # Get user ID from auth
        user_email = auth.get("preferred_username", "")
        user_id = encrypt_email(user_email) if user_email else None

        template_data = {
            "title": template_request.title,
            "description": template_request.description,
            "template_content": template_request.template_content,
            "use_case": template_request.use_case,
            "llms": template_request.llms,
            "placeholders": [p.dict() for p in template_request.placeholders] if template_request.placeholders else None,
            "user_id": user_id,
            "collection_id": str(template_request.collection_id) if template_request.collection_id else None,
        }

        response = prompt_service_client.create_template(template_data)
        return PromptTemplateResponse(**response)

    except Exception as e:
        logger.error(f"Error creating template: {e}")
        raise HTTPException(status_code=500, detail=f"Error creating template: {str(e)}")


@router.get("/templates", response_model=TemplateListResponse)
async def get_templates(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    use_case: Optional[str] = Query(None),
    collection_id: Optional[UUID] = Query(None),
    auth=Depends(get_auth),
):
    """Get all templates with optional filtering."""
    try:
        response = prompt_service_client.get_all_templates(
            skip=skip,
            limit=limit,
            use_case=use_case,
            collection_id=collection_id,
        )
        return TemplateListResponse(**response)
    except Exception as e:
        logger.error(f"Error getting templates: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting templates: {str(e)}")


@router.get("/templates/{template_id}", response_model=PromptTemplateResponse)
async def get_template(
    template_id: UUID,
    auth=Depends(get_auth),
):
    """Get a specific template by ID."""
    try:
        response = prompt_service_client.get_template(str(template_id))
        return PromptTemplateResponse(**response)
    except Exception as e:
        logger.error(f"Error getting template: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting template: {str(e)}")


@router.put("/templates/{template_id}", response_model=PromptTemplateResponse)
async def update_template(
    template_id: UUID,
    template_request: UpdateTemplateRequest,
    auth=Depends(get_auth),
):
    """Update an existing template."""
    try:
        template_data = {}
        if template_request.title is not None:
            template_data["title"] = template_request.title
        if template_request.description is not None:
            template_data["description"] = template_request.description
        if template_request.template_content is not None:
            template_data["template_content"] = template_request.template_content
        if template_request.use_case is not None:
            template_data["use_case"] = template_request.use_case
        if template_request.llms is not None:
            template_data["llms"] = template_request.llms
        if template_request.placeholders is not None:
            template_data["placeholders"] = [p.dict() for p in template_request.placeholders]
        if template_request.collection_id is not None:
            template_data["collection_id"] = str(template_request.collection_id)

        response = prompt_service_client.update_template(str(template_id), template_data)
        return PromptTemplateResponse(**response)
    except Exception as e:
        logger.error(f"Error updating template: {e}")
        raise HTTPException(status_code=500, detail=f"Error updating template: {str(e)}")


@router.delete("/templates/{template_id}", response_model=MessageResponse)
async def delete_template(
    template_id: UUID,
    auth=Depends(get_auth),
):
    """Delete a template."""
    try:
        response = prompt_service_client.delete_template(str(template_id))
        return MessageResponse(message=response.get("message", "Template deleted successfully"))
    except Exception as e:
        logger.error(f"Error deleting template: {e}")
        raise HTTPException(status_code=500, detail=f"Error deleting template: {str(e)}")


@router.get("/templates/user/{user_email}", response_model=TemplateListResponse)
async def get_user_templates(
    user_email: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    use_case: Optional[str] = Query(None),
    auth=Depends(get_auth),
):
    """Get templates for a specific user."""
    try:
        response = prompt_service_client.get_user_templates(
            user_email=user_email,
            skip=skip,
            limit=limit,
            use_case=use_case,
        )
        return TemplateListResponse(**response)
    except Exception as e:
        logger.error(f"Error getting user templates: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting user templates: {str(e)}")


@router.get("/templates/system", response_model=TemplateListResponse)
async def get_system_templates(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    auth=Depends(get_auth),
):
    """Get system templates."""
    try:
        response = prompt_service_client.get_system_templates(skip=skip, limit=limit)
        return TemplateListResponse(**response)
    except Exception as e:
        logger.error(f"Error getting system templates: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting system templates: {str(e)}")


@router.post("/templates/{template_id}/render", response_model=RenderedTemplateResponse)
async def render_template(
    template_id: UUID,
    render_request: RenderTemplateRequest,
    auth=Depends(get_auth),
):
    """Render a template with placeholder values."""
    try:
        response = prompt_service_client.render_template(str(template_id), render_request.placeholder_values)
        return RenderedTemplateResponse(**response)
    except Exception as e:
        logger.error(f"Error rendering template: {e}")
        raise HTTPException(status_code=500, detail=f"Error rendering template: {str(e)}")


@router.get("/templates/search", response_model=TemplateListResponse)
async def search_templates(
    search_term: str = Query(..., description="Search term to filter templates"),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    use_case: Optional[str] = Query(None),
    collection_id: Optional[UUID] = Query(None),
    auth=Depends(get_auth),
):
    """Search templates by title, description, or content."""
    try:
        response = prompt_service_client.search_templates(
            search_term=search_term,
            skip=skip,
            limit=limit,
            use_case=use_case,
            collection_id=collection_id,
        )
        return TemplateListResponse(**response)
    except Exception as e:
        logger.error(f"Error searching templates: {e}")
        raise HTTPException(status_code=500, detail=f"Error searching templates: {str(e)}")


@router.get("/templates/users/unique", response_model=List[str])
async def get_unique_users_with_templates(
    auth=Depends(get_auth),
):
    """Get list of unique users who have templates."""
    try:
        return prompt_service_client.get_unique_users_with_templates()
    except Exception as e:
        logger.error(f"Error getting unique users: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting unique users: {str(e)}")
