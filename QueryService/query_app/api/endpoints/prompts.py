import logging
from typing import List

import requests
from fastapi import APIRouter, HTTPException, Query
from query_app.api.models.response import Prompt
from query_app.api.utils.helpers import encrypt_email
from query_app.service_clients.prompt_service_client.prompt_requests import (
    PromptOrchestrationClient,
)
from query_app.settings import settings

logger = logging.getLogger(__name__)

router = APIRouter()
prompt_service_client = PromptOrchestrationClient(settings.prompt_base_url)


@router.get("/prompts", response_model=List[Prompt])
async def get_prompts():
    """Get all available prompts"""
    try:
        return prompt_service_client.get_all_prompts()
    except requests.RequestException as e:
        raise HTTPException(status_code=500, detail=f"Error fetching prompts: {str(e)}")


@router.get("/prompts/search", response_model=List[Prompt])
async def search_prompts(search_term: str = Query(..., description="Search term to filter prompts")):
    """Search prompts by text"""
    try:
        return prompt_service_client.search_prompts(search_term)
    except requests.RequestException as e:
        raise HTTPException(status_code=500, detail=f"Error searching prompts: {str(e)}")


@router.get("/prompts/user/{user_email}", response_model=List[Prompt])
async def get_user_prompts(user_email: str):
    """Get all prompts for a specific user"""
    try:
        user_id = encrypt_email(user_email)
        return prompt_service_client.get_user_prompts(user_id)
    except requests.RequestException as e:
        raise HTTPException(status_code=500, detail=f"Error fetching user prompts: {str(e)}")


@router.get("/prompts/user/{user_email}/search", response_model=List[Prompt])
async def search_user_prompts(user_email: str, search_term: str = Query(..., description="Search term to filter prompts")):
    """Search prompts for a specific user"""
    try:
        user_id = encrypt_email(user_email)
        return prompt_service_client.search_user_prompts(user_id, search_term)
    except requests.RequestException as e:
        raise HTTPException(status_code=500, detail=f"Error searching user prompts: {str(e)}")


@router.get("/prompts/system", response_model=List[Prompt])
async def get_system_prompts():
    """Get all SYSTEM prompts."""
    try:
        prompts = prompt_service_client.get_system_prompts()
        return prompts
    except requests.RequestException as e:
        raise HTTPException(status_code=500, detail=f"Error fetching system prompts: {str(e)}")
