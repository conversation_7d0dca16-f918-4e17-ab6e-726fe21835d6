import json
import logging
from typing import Any

from fastapi import APIRouter, Depends, HTTPException
from fastapi.encoders import jsonable_encoder
from fastapi.params import Query
from query_app.api.models.request import CreateSessionRequest, UpdateSessionRequest
from query_app.crud import user_sessions as crud
from query_app.crud.user_sessions import get_sessions
from query_app.database.session import get_db
from query_app.services.auth import get_auth
from query_app.services.redis_connection import redis_conn
from query_app.utils.helpers import get_entra_auth_user_info
from sqlalchemy.orm import Session
from starlette.responses import JSONResponse

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/get_sessions_by_collection_id/{collection_id}")
async def api_get_sessions_by_collection(
    collection_id: str,
    db_session: Session = Depends(get_db),
    auth: Any = Depends(get_auth),
) -> JSONResponse:
    """
    Fetch all user sessions by collection ID and return as JSON response.
    """
    try:
        user_info, is_azure_user = get_entra_auth_user_info(auth)
        user_email = user_info.get("email")

        # 1) Fetch sessions (already sorted by created_at DESC in get_sessions_by_collection_id)
        sessions = crud.get_sessions_by_collection_id(db_session, collection_id, user_email)

        # 2) Get folder structure (alphabetical)
        folder_key = f"folder:{user_email}:{collection_id}"
        folder_data = redis_conn.get(folder_key)

        folder_structure = {}
        if folder_data:
            try:
                folder_structure = json.loads(folder_data.decode("utf-8"))
            except json.JSONDecodeError:
                logger.error(f"Error decoding folder data for user {user_email}, collection {collection_id}: {folder_data}")
                folder_structure = {}

        # Sort folder names in alphabetical order
        sorted_folder_names = sorted(folder_structure.keys(), key=str.lower)

        # 3) Initialize sessions_by_folder
        #    We'll add folders in alphabetical order first
        sessions_by_folder: dict[str, list[dict[str, Any]]] = {}
        for fname in sorted_folder_names:
            sessions_by_folder[fname] = []

        # Then ensure we have an 'unassigned' key (if you want it at the bottom)
        if "unassigned" not in sessions_by_folder:
            sessions_by_folder["unassigned"] = []

        favorite_sessions = set()

        for session in sessions:
            if session.get("is_favorite"):
                if "favorites" not in sessions_by_folder:
                    sessions_by_folder["favorites"] = []
                sessions_by_folder["favorites"].append(session)
                favorite_sessions.add(session["id"])

        for session in sessions:
            if session["id"] in favorite_sessions:
                continue

            assigned = False
            # Iterate in alphabetical order
            for folder_name in sorted_folder_names:
                if session["id"] in folder_structure.get(folder_name, []):
                    sessions_by_folder[folder_name].append(session)
                    assigned = True
                    break
            if not assigned:
                sessions_by_folder["unassigned"].append(session)

        return JSONResponse(content={"folders": sessions_by_folder}, status_code=200)
    except Exception as e:
        logger.error(f"Error in api_get_sessions_by_collection {collection_id}", exc_info=e)
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.post("/create_folder/{collection_id}/{folder_name}")
async def api_create_folder(collection_id: str, folder_name: str, auth: Any = Depends(get_auth)):
    """Create a new folder in Redis for a given collection."""
    try:
        if not folder_name:
            logger.error("Folder name cannot be empty")
            raise HTTPException(status_code=400, detail="Invalid folder name")
        if not collection_id or collection_id == "undefined":
            logger.error("Collection id cannot be empty")
            raise HTTPException(status_code=400, detail="Invalid collection ID")

        user_info, is_azure_user = get_entra_auth_user_info(auth)
        user_email = user_info.get("email")

        folder_key = f"folder:{user_email}:{collection_id}"
        folder_data = redis_conn.get(folder_key)

        folder_structure = {}
        if folder_data:
            try:
                folder_structure = json.loads(folder_data.decode("utf-8"))
            except json.JSONDecodeError:
                logger.error(f"Error decoding folder data: {folder_data}")
                folder_structure = {}

        if folder_name in folder_structure:
            logger.error(f"Folder {folder_name} already exists")
            raise HTTPException(status_code=409, detail="Folder already exists")

        folder_structure[folder_name] = []
        redis_conn.set(folder_key, json.dumps(folder_structure))

        return JSONResponse(content={"message": "Folder created successfully"}, status_code=200)
    except Exception as e:
        logger.error(f"Error creating folder: {e}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.post("/rename_folder/{collection_id}/{old_folder_name}/{new_folder_name}")
async def api_rename_folder(
    collection_id: str,
    old_folder_name: str,
    new_folder_name: str,
    auth: Any = Depends(get_auth),
):
    """Rename an existing folder in Redis for a given collection."""
    try:
        if not old_folder_name or not new_folder_name:
            logger.error("Invalid folder names", exc_info=True)
            raise HTTPException(status_code=400, detail="Invalid folder name")
        if not collection_id or collection_id == "undefined":
            logger.error(f"Invalid collection ID: {collection_id}")
            raise HTTPException(status_code=400, detail="Invalid collection ID")

        user_info, is_azure_user = get_entra_auth_user_info(auth)
        user_email = user_info.get("email")

        folder_key = f"folder:{user_email}:{collection_id}"
        folder_data = redis_conn.get(folder_key)

        folder_structure = {}
        if folder_data:
            try:
                folder_structure = json.loads(folder_data.decode("utf-8"))
            except json.JSONDecodeError:
                logger.error(f"Error decoding folder data: {folder_data}")
                folder_structure = {}

        if old_folder_name not in folder_structure:
            logger.info(f"Folder {old_folder_name} not found")
            raise HTTPException(status_code=404, detail="Folder not found")

        if new_folder_name in folder_structure:
            logger.info(f"Folder {new_folder_name} already exists")
            raise HTTPException(status_code=409, detail="Folder already exists")

        folder_contents = folder_structure.pop(old_folder_name)
        folder_structure[new_folder_name] = folder_contents
        redis_conn.set(folder_key, json.dumps(folder_structure))

        return JSONResponse(content={"message": "Folder renamed successfully"}, status_code=200)

    except Exception as e:
        logger.error(f"Error renaming folder: {e}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.post("/move_session_to_folder")
async def api_move_session_to_folder(
    folder_name: str = Query(...),
    session_id: str = Query(...),
    collection_id: str = Query(...),
    auth: Any = Depends(get_auth),
):
    """Move a session to a specified folder in Redis."""
    try:
        user_info, is_azure_user = get_entra_auth_user_info(auth)
        user_email = user_info.get("email")

        # Fetch current folder structure for the collection
        folder_key = f"folder:{user_email}:{collection_id}"
        folder_data = redis_conn.get(folder_key)

        folder_structure = {}
        if folder_data:
            folder_structure = json.loads(folder_data.decode("utf-8"))

        # Update folder structure
        for folder_sessions in folder_structure.values():
            if session_id in folder_sessions:
                folder_sessions.remove(session_id)

        if folder_name not in folder_structure:
            folder_structure[folder_name] = []
        folder_structure[folder_name].append(session_id)

        redis_conn.set(folder_key, json.dumps(folder_structure))

        return JSONResponse(content={"message": "Session moved successfully"}, status_code=200)
    except Exception as e:
        logger.error(f"Error: {e}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.delete("/delete_folder/{collection_id}/{folder_name}")
async def api_delete_folder(collection_id: str, folder_name: str, auth: Any = Depends(get_auth)):
    """Delete a folder in Redis for a given collection."""
    try:
        user_info, _ = get_entra_auth_user_info(auth)
        user_email = user_info.get("email")

        folder_key = f"folder:{user_email}:{collection_id}"
        folder_data = redis_conn.get(folder_key)

        folder_structure = {}
        if folder_data:
            folder_structure = json.loads(folder_data.decode("utf-8"))

        if folder_name not in folder_structure:
            raise HTTPException(status_code=404, detail="Folder not found")

        del folder_structure[folder_name]
        redis_conn.set(folder_key, json.dumps(folder_structure))

        return JSONResponse(content={"message": "Folder deleted successfully"}, status_code=200)
    except Exception as e:
        logger.error(f"Error deleting folder: {e}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.post("/remove_session_from_folder")
async def api_remove_session_from_folder(
    folder_name: str,
    session_id: str,
    collection_id: str = Query(...),
    auth: Any = Depends(get_auth),
):
    """
    Remove a session from a folder in Redis.
    """
    try:
        user_info, _ = get_entra_auth_user_info(auth)
        user_email = user_info.get("email")
        if not user_email:
            raise HTTPException(status_code=400, detail="Invalid user email")

        # Folder key includes collection_id
        folder_key = f"folder:{user_email}:{collection_id}"

        folder_data = redis_conn.get(folder_key)
        folder_structure = {}
        if folder_data:
            folder_structure = json.loads(folder_data.decode("utf-8"))

        if folder_name not in folder_structure:
            raise HTTPException(status_code=404, detail="Folder not found")

        if session_id not in folder_structure[folder_name]:
            raise HTTPException(status_code=404, detail="Session not found in folder")

        # Remove session from the folder
        folder_structure[folder_name].remove(session_id)

        # Update Redis
        redis_conn.set(folder_key, json.dumps(folder_structure))

        return JSONResponse(
            content={"message": "Session removed from folder successfully"},
            status_code=200,
        )
    except Exception as e:
        logger.error(f"Error: {e}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.get("/get_sessions")
async def api_get_sessions(
    db_session: Session = Depends(get_db),
    auth: Any = Depends(get_auth),
) -> JSONResponse:
    """
    Fetch all user sessions and their queries and return as JSON response.
    """
    try:
        user_info, is_azure_user = get_entra_auth_user_info(auth)
        user_email = user_info.get("email")
        sessions = get_sessions(db_session, user_email)
        return JSONResponse(content=sessions, status_code=200)
    except Exception as e:
        import traceback

        traceback.print_exc()
        logger.error(f"Error in api_get_sessions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.post("/create_session/{collection_id}")
async def api_create_session(
    collection_id: str,
    request: CreateSessionRequest,
    db_session: Session = Depends(get_db),
    auth: Any = Depends(get_auth),
) -> JSONResponse:
    """
    Create a new user session.
    """
    try:
        user_info, is_azure_user = get_entra_auth_user_info(auth)
        user_email = user_info.get("email")

        session_goal = request.session_goal
        session = crud.create_session(db_session, collection_id, user_email, session_goal)

        return JSONResponse(content=session, status_code=200)
    except Exception as e:
        import traceback

        traceback.print_exc()
        logger.error(f"Error in api_get_sessions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.delete("/delete_session/{session_id}")
async def api_delete_session(
    session_id: str,
    db_session: Session = Depends(get_db),
    auth: Any = Depends(get_auth),
) -> JSONResponse:
    """
    Delete a session by its ID.
    """
    try:
        # Get user info from the auth object
        user_info, is_azure_user = get_entra_auth_user_info(auth)
        user_email = user_info.get("email")

        # Check if session exists
        session = crud.get_session_by_id(db_session, session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # Ensure user owns the session
        if session.user_email != user_email:
            raise HTTPException(status_code=403, detail="Forbidden: You do not own this session")

        # Delete session
        crud.delete_session_by_id(db_session, session_id)

        # Remove sessions chat history
        key_pattern = f"chat_history:*:{session_id}:*"
        matching_keys = redis_conn.keys(key_pattern)
        if matching_keys:
            redis_conn.delete(*matching_keys)
            logger.info(f"Deleted Redis keys for session {session_id}: {matching_keys}")
        else:
            logger.info(f"No Redis keys found for session {session_id} with pattern {key_pattern}")

        return JSONResponse(content={"message": "Session deleted successfully"}, status_code=200)
    except Exception as e:
        logger.error(f"Error deleting session: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.put("/update_session/{session_id}")
async def api_update_session(
    session_id: str,
    request: UpdateSessionRequest,
    db_session: Session = Depends(get_db),
    auth: Any = Depends(get_auth),
) -> JSONResponse:
    """
    Update the title and/or uploaded PDFs of a session.
    """
    try:
        # Get user info from the auth object
        user_info, is_azure_user = get_entra_auth_user_info(auth)
        user_email = user_info.get("email")

        # Check if session exists
        session = crud.get_session_by_id(db_session, session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # Ensure user owns the session
        if session.user_email != user_email:
            raise HTTPException(status_code=403, detail="Forbidden: You do not own this session")

        # Update title if provided
        if request.title is not None or request.custom_config is not None:
            updated_session = crud.update_session_title(db_session, session_id, request.title, request.custom_config)

        # Update uploaded_pdfs if provided
        if request.uploaded_pdfs is not None:
            updated_session = crud.update_session_pdfs(db_session, session_id, request.uploaded_pdfs)

        return JSONResponse(content=jsonable_encoder(updated_session), status_code=200)
    except Exception as e:
        logger.error(f"Error updating session: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.get("/get_session/{session_id}")
async def api_get_session(
    session_id: str,
    db_session: Session = Depends(get_db),
    auth: Any = Depends(get_auth),
) -> JSONResponse:
    """
    Fetch a user session by its ID and return as JSON response.
    """
    try:
        user_info, _ = get_entra_auth_user_info(auth)
        user_email = user_info.get("email")
        session_obj = crud.get_session_by_id(db_session, session_id)
        if not session_obj:
            logger.error(f"Session not found: {session_id}", exc_info=True)
            raise HTTPException(status_code=404, detail="Session not found")
        if session_obj.user_email != user_email:
            logger.error(f"User {session_obj.user_email} does not belong to {user_email}", exc_info=True)
            raise HTTPException(status_code=403, detail="Forbidden: You do not own this session")
        return JSONResponse(content=session_obj.to_dict(), status_code=200)
    except Exception as e:
        logger.error(f"Error fetching session: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.put("/update_favorite_status/{session_id}")
async def api_update_favorite_status(
    session_id: str,
    is_favorite: bool,
    db_session: Session = Depends(get_db),
    auth: Any = Depends(get_auth),
) -> JSONResponse:
    """
    Update the favorite status of a session.
    """
    try:
        user_info, _ = get_entra_auth_user_info(auth)
        user_email = user_info.get("email")
        session_obj = crud.get_session_by_id(db_session, session_id)
        if not session_obj:
            raise HTTPException(status_code=404, detail="Session not found")
        if session_obj.user_email != user_email:
            raise HTTPException(status_code=403, detail="Forbidden: You do not own this session")
        session_obj.is_favorite = is_favorite
        db_session.commit()
        return JSONResponse(content=session_obj.to_dict(), status_code=200)
    except Exception as e:
        logger.error(f"Error updating favorite status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.post("/skip_chat_goals/{session_id}")
async def api_skip_chat_goals(
    session_id: str,
    db_session: Session = Depends(get_db),
    auth: Any = Depends(get_auth),
) -> JSONResponse:
    """
    Update the favorite status of a session.
    """
    try:
        user_info, _ = get_entra_auth_user_info(auth)
        user_email = user_info.get("email")
        folder_key = f"chat_goal_{user_email}_{session_id}"
        redis_conn.set(folder_key, "skipped")

        return JSONResponse(content={"session_id": session_id}, status_code=200)
    except Exception as e:
        logger.error(f"Error updating favorite status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.get("/is_chat_goal_skipped/{session_id}")
async def api_is_chat_goal_skipped(
    session_id: str,
    db_session: Session = Depends(get_db),
    auth: Any = Depends(get_auth),
) -> JSONResponse:
    """
    Check if chat goals are skipped for a session.
    """
    try:
        user_info, _ = get_entra_auth_user_info(auth)
        user_email = user_info.get("email")
        folder_key = f"chat_goal_{user_email}_{session_id}"
        skipped = redis_conn.get(folder_key)
        return JSONResponse(content={"skipped": skipped == b"skipped"}, status_code=200)
    except Exception as e:
        logger.error(f"Error fetching skipped chat goals: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.get("/list_folders/collection_id/{collection_id}")
async def api_list_folders(
    collection_id: str,
    auth: Any = Depends(get_auth),
) -> JSONResponse:
    """
    List all folders for a collection.
    """
    try:
        user_info, is_azure_user = get_entra_auth_user_info(auth)
        user_email = user_info.get("email")

        folder_key = f"folder:{user_email}:{collection_id}"
        folder_data = redis_conn.get(folder_key)

        folder_structure = {}
        if folder_data:
            folder_structure = json.loads(folder_data.decode("utf-8"))

        return JSONResponse(content={"folders": folder_structure}, status_code=200)
    except Exception as e:
        import traceback

        traceback.print_exc()
        logger.error(f"Error fetching folders: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.get("/session/{session_id}")
async def get_session(
    session_id: str,
    db_session: Session = Depends(get_db),
    auth: Any = Depends(get_auth),
) -> JSONResponse:
    """
    Fetch a user session by its ID and return as JSON response.
    """
    try:
        user_info, is_azure_user = get_entra_auth_user_info(auth)
        user_email = user_info.get("email")
        session = crud.get_session_by_id(db_session, session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        if session.user_email != user_email:
            raise HTTPException(status_code=403, detail="Forbidden: You do not own this session")
        return JSONResponse(content=jsonable_encoder(session), status_code=200)
    except Exception as e:
        import traceback

        traceback.print_exc()
        logger.error(f"Error fetching session: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")
