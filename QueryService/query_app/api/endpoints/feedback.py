import logging

from fastapi import APIRouter, Depends
from query_app.api.handlers.feedback import (
    handle_delete_feedback,
    handle_delete_feedbacks_by_query_id,
    handle_get_feedback_status,
    handle_get_feedbacks,
    handle_post_feedback,
)
from query_app.api.models.request import AddFeedbackRequest
from query_app.api.models.response import (
    AddFeedbackResponse,
    GetFeedbacksResponse,
    GetFeedbackStatusResponse,
    MessageResponse,
)
from query_app.database.session import get_db
from query_app.services.auth import get_auth
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post(
    "/feedback",
    response_model=AddFeedbackResponse,
    responses={
        400: {"model": MessageResponse},
        404: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_post_feedback(
    feedback_request: AddFeedbackRequest,
    db_session: Session = Depends(get_db),
    auth=Depends(get_auth),
) -> AddFeedbackResponse:
    """Endpoint to add feedback for a query."""
    return await handle_post_feedback(db_session, feedback_request)


@router.get(
    "/feedback/{feedback_id}/status",
    response_model=GetFeedbackStatusResponse,
    responses={
        400: {"model": MessageResponse},
        404: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_get_feedback_status(
    feedback_id: str,
    db_session: Session = Depends(get_db),
    auth=Depends(get_auth),
) -> GetFeedbackStatusResponse:
    """Endpoint to get the status of feedback by its ID."""
    return await handle_get_feedback_status(db_session, feedback_id)


@router.delete(
    "/feedback/{feedback_id}",
    response_model=MessageResponse,
    responses={
        400: {"model": MessageResponse},
        404: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_delete_feedback(
    feedback_id: str,
    db_session: Session = Depends(get_db),
    auth=Depends(get_auth),
) -> MessageResponse:
    """Endpoint to delete feedback by its ID."""
    return await handle_delete_feedback(db_session, feedback_id)


@router.get(
    "/feedbacks",
    response_model=GetFeedbacksResponse,
    responses={
        500: {"model": MessageResponse},
    },
)
async def api_get_feedbacks(
    db_session: Session = Depends(get_db),
    auth=Depends(get_auth),
) -> GetFeedbacksResponse:
    """Endpoint to retrieve all feedbacks."""
    return await handle_get_feedbacks(db_session)


@router.delete(
    "/query/{query_id}/feedbacks",
    response_model=MessageResponse,
    responses={
        400: {"model": MessageResponse},
        500: {"model": MessageResponse},
    },
)
async def api_delete_feedbacks_by_query_id(
    query_id: str,
    db_session: Session = Depends(get_db),
    auth=Depends(get_auth),
) -> MessageResponse:
    """Endpoint to delete all feedbacks associated with a specific query ID."""
    return await handle_delete_feedbacks_by_query_id(db_session, query_id)
