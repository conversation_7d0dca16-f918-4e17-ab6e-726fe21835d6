import logging
import os
import random
from typing import Union

from fastapi import APIRouter, Depends, File, HTTPException, UploadFile
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse, StreamingResponse
from openai import AzureOpenAI
from query_app.api.handlers.query import handle_post_query, handle_post_query_stream
from query_app.api.models.request import QueryRequest, QueryUpdateRequest
from query_app.api.models.response import (
    GetQueriesResponse,
    QueryRelevantDocsResponse,
    QueryResponse,
)
from query_app.crud.query import (
    delete_queries_by_collection_id,
    delete_query,
    get_queries,
    get_queries_by_session_id,
    get_query,
    get_relevant_docs_by_query_id,
    update_query,
)
from query_app.crud.user_sessions import get_session_by_id
from query_app.database.session import get_db
from query_app.enums.search import SearchMethodsEnum
from query_app.llm.models import (
    DEFAULT_EMBEDDING_MODEL,
    DEFAULT_LLM_MODEL,
    DEFAULT_SEARCH_METHOD,
)
from query_app.service_clients.loader_service_client.asynchronous import get_collection
from query_app.services.auth import get_auth
from query_app.services.hyde import Hyde
from query_app.services.semantic_search import SemanticSearch
from query_app.settings import settings
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

router = APIRouter()


async def _verify_collection_existance(collection_id=None, collection_name=None, headers=None):
    if collection_name:
        collection = await get_collection(collection_name=collection_name, headers=headers)
    else:
        collection = await get_collection(collection_id=collection_id, headers=headers)

    if not collection:
        raise HTTPException(status_code=404, detail="Collection not found")
    return collection


def _create_headers(auth):
    if isinstance(auth, dict):
        is_admin = auth.get("user") == "admin" and auth.get("role") == "admin_ui"
        local_dev = auth.get("user") == "developer" and auth.get("role") == "dev_local"
        headers = {"X-Admin-UI": "true"} if is_admin or local_dev else {}
        return headers
    elif not isinstance(auth, dict) and not auth.access_token:
        raise HTTPException(status_code=401, detail="Unauthorized")
    else:
        return {"Authorization": f"Bearer {auth.access_token}"}


@router.post("/collection/{collection_id}/query", response_model=QueryResponse)
async def api_post_query(
    collection_id: str,
    query_request: QueryRequest,
    db_session: Session = Depends(get_db),
    auth=Depends(get_auth),
) -> QueryResponse:
    """Query endpoint.

    Args:
        collection_id: collection identifier.
        query_request: query data.
        auth: Azure AD authentication.
        db_session: database session.

    Returns:
        JSONResponse: query_id, answer and retrieved_docs.
    """
    return await handle_post_query(collection_id, query_request, db_session, auth)


@router.put("/collection/{collection_id}/query_update", response_model=QueryResponse)
async def api_post_query_for_update(
    collection_id: str,
    query_request: QueryUpdateRequest,
    db_session: Session = Depends(get_db),
    auth=Depends(get_auth),
) -> Union[QueryResponse, StreamingResponse]:
    """Query endpoint.

    Args:
        collection_id: collection identifier.
        query_request: query data.
        auth: Azure AD authentication.
        db_session: database session.

    Returns:
        JSONResponse: query_id, answer and retrieved_docs.
    """
    return await handle_post_query_stream(db_session, collection_id, query_request, auth, update=True)


@router.post("/collection/{collection_id}/query_stream", response_model=QueryResponse)
async def api_post_query_stream(
    collection_id: str,
    query_request: QueryRequest,
    db_session: Session = Depends(get_db),
    auth=Depends(get_auth),
) -> Union[JSONResponse, StreamingResponse]:
    """Query endpoint.

    Args:
        collection_id: collection identifier.
        query_request: query data.
        auth: Azure AD authentication.
        db_session: database session.

    Returns:
        JSONResponse: query_id, answer and retrieved_docs.
    """
    return await handle_post_query_stream(db_session, collection_id, query_request, auth)


@router.post(
    "/collection/{collection_id}/query/retrieved_docs",
    response_model=QueryRelevantDocsResponse,
)
async def api_post_query_relevant_docs(
    collection_id: str,
    query_request: QueryRequest,
    db_session: Session = Depends(get_db),
    auth=Depends(get_auth),
) -> JSONResponse:
    """Fetch retrieved docs for question.

    Args:
        collection_id: collection identifier.
        query_request: query data.
        auth: Azure AD authentication.
        db_session: database session.

    Returns:
        JSONResponse: retrieved_docs.
    """
    headers = _create_headers(auth)
    collection = await _verify_collection_existance(collection_id=collection_id, headers=headers)
    collection_name = collection.get("name")
    custom_config = collection.get("custom_config", {})

    # get custom config if exists, otherwise use defaults
    _embedding_model_name = custom_config.get("embedding_model", DEFAULT_EMBEDDING_MODEL)
    _llm_model_name = custom_config.get("llm_model", DEFAULT_LLM_MODEL)
    _method = custom_config.get("method", DEFAULT_SEARCH_METHOD)

    # get custom system prompt if exists
    custom_system_prompt = query_request.custom_prompt
    if custom_system_prompt:
        logger.info(f"Using custom system prompt: {custom_system_prompt}")
        custom_config["system_prompt"] = custom_system_prompt
    else:
        custom_system_prompt = custom_config.get("system_prompt")
        logger.info(f"Using collection's system prompt: {custom_system_prompt}")

    logger.info(f"Query: {_method=} {_embedding_model_name=} {_llm_model_name=} {custom_system_prompt=}")

    retrieved_docs: list = []
    if _method == SearchMethodsEnum.SEMANTIC_SEARCH.value:
        semantic_search = SemanticSearch(
            collection_name=collection_name,
            embedding_model_name=_embedding_model_name,
            llm_model_name=_llm_model_name,
            custom_system_prompt=custom_system_prompt,
            top_k=query_request.top_k,
        )
        retrieved_docs, retrieved_docs_mmr, _ = semantic_search.retrieve_relevant_docs(query_request.content)
    elif _method == SearchMethodsEnum.HYDE.value:
        hyde = Hyde(
            collection_name=collection_name,
            embedding_model_name=_embedding_model_name,
            llm_model_name=_llm_model_name,
            custom_system_prompt=custom_system_prompt,
        )
        retrieved_docs, retrieved_docs_mmr = hyde.retrieve_relevant_docs(query_request.content)

    logger.info(f"retrieved_docs: {retrieved_docs}")

    response = QueryRelevantDocsResponse(message=None, docs=[doc.__dict__ for doc, score in retrieved_docs])
    return JSONResponse(content=jsonable_encoder(response), status_code=200)


@router.get("/collection/{collection_id}/queries", response_model=GetQueriesResponse)
async def api_get_queries(collection_id: str, db_session: Session = Depends(get_db), auth=Depends(get_auth)) -> JSONResponse:
    """Get all queries.

    Args:
        collection_id: collection identifier.
        auth: Azure AD authentication.
        db_session: database session.

    Returns:
        JSONResponse: queries.
    """
    headers = _create_headers(auth)
    await _verify_collection_existance(collection_id=collection_id, headers=headers)

    queries = get_queries(db_session, collection_id)
    response = GetQueriesResponse(queries=jsonable_encoder(queries))
    return JSONResponse(content=jsonable_encoder(response), status_code=200)


@router.post("/api/query_with_voice")
async def query_with_voice(audio: UploadFile = File(...), auth=Depends(get_auth)):
    """Endpoint to handle audio transcription and query processing."""
    logger.info(f"Received request with audio content type: {audio.content_type}")

    if audio.content_type not in ["audio/wav", "audio/mpeg"]:
        logger.error(f"Unsupported audio content type: {audio.content_type}")
        raise HTTPException(status_code=400, detail="Invalid audio format")

    audio_file = await audio.read()
    audio_size = len(audio_file)
    logger.info(f"Audio file size: {audio_size} bytes")

    client = AzureOpenAI(
        api_key=settings.openai_api_key,
        api_version="2024-02-01",
        azure_endpoint=settings.openai_api_base,
    )

    deployment_id = "whispter-test"
    temp_file = "./temp_audio.wav"

    try:
        with open(temp_file, "wb") as f:
            f.write(audio_file)

        with open(temp_file, "rb") as f:
            result = client.audio.transcriptions.create(file=f, model=deployment_id)

        transcription_text = result.text

        return JSONResponse(content={"transcription": transcription_text})

    except Exception as e:
        logger.exception("Error during transcription process", exc_info=e)
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if os.path.exists(temp_file):
            logger.info("Removing temporary audio file")
            os.remove(temp_file)


@router.get("/get_queries_by_session_id/{session_id}")
def get_queries_by_session_id_(session_id: str, db_session: Session = Depends(get_db), auth=Depends(get_auth)):
    """Get all queries by session id.

    Args:
        session_id: session identifier.
        auth: Azure AD authentication.
        db_session: database session.

    Returns:
        JSONResponse: queries.
    """
    try:
        queries = get_queries_by_session_id(db_session, session_id)
        return JSONResponse(content=queries, status_code=200)
    except Exception as e:
        logger.exception("Error during get_queries_by_session_id process", exc_info=e)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/conversationstarters/{session_id}")
async def get_demo_conversation_starters(session_id: str, auth=Depends(get_auth), db_session: Session = Depends(get_db)) -> JSONResponse:
    """Conversationstarters endpoint."""
    session = get_session_by_id(db_session, session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    conversation_starters = [
        "Bitte verändere durchgängig den Härtegrad der Verhandlung. Wähle Härtegrad 1,2,3.",
        "Formuliere 3 typische Einwände und liefere die dazugehörige Einwandbehandlung.",
        "Gib mir verschiedene Einstiegsformulierungen / Situationsdefinitionen, die sich direkt auf meine Verhandlungsziele beziehen.",
        "Was kann ich auf der Einstellungsebene (Mindset) beachten. Wie kann ich mich innerlich in ein positives Mindset bringen.",
        "Erarbeite für mich 3 Varianten einer zwingenden Argumentation, die aus einer Kombination aus Realität, Wertefragen, "
        "Interessenlagen erarbeitet und meine Forderung klar begründet.",
        "Ich bin in einer Position der Stärke / Schwäche – wie wirkt sich das auf meine Vorgehensweise insgesamt aus?",
        "Ich möchte die Verhandlung konstruktiv beenden und dem Verhandlungspartner signalisieren, dass er sich bewegen muss, bevor von meiner "
        "Seite weitere Schritte möglich sind.",
    ]

    conversation_starters = random.sample(conversation_starters, 3)

    return JSONResponse(content=conversation_starters, status_code=200)


@router.put("/{query_id}/toggle_favorite/{collection_id}")
async def toggle_query_favorite(
    query_id: str,
    collection_id: str,
    db_session: Session = Depends(get_db),
    auth=Depends(get_auth),
):
    """Toggle favorite status of a query.

    Args:
        query_id: query identifier.
        collection_id: collection identifier.
        db_session: database session.
        auth: Azure AD authentication.

    Returns:
        JSONResponse: updated query.
    """

    query = get_query(db_session, query_id, collection_id)

    if not query:
        raise HTTPException(status_code=404, detail="Query not found")

    new_favorite_status = not query.is_favorite

    update_query(
        db_session,
        query_id=query_id,
        collection_id=collection_id,
        is_favorite=new_favorite_status,
    )

    return JSONResponse(
        content={"query_id": query_id, "is_favorite": new_favorite_status},
        status_code=200,
    )


@router.delete("/collection/{collection_id}/queries", status_code=200)
async def api_delete_queries_by_collection_id(
    collection_id: str,
    db_session: Session = Depends(get_db),
    auth=Depends(get_auth),
):
    """Delete all queries associated with a collection."""
    headers = _create_headers(auth)
    await _verify_collection_existance(collection_id=collection_id, headers=headers)
    try:
        delete_queries_by_collection_id(db_session, collection_id)
        return JSONResponse(
            content={"message": f"All queries for collection {collection_id} deleted."},
            status_code=200,
        )
    except Exception as e:
        logger.exception("Error deleting queries", exc_info=e)
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/query/{query_id}", status_code=200)
async def api_delete_query(query_id: str, db_session: Session = Depends(get_db)):
    """Delete a specific query by its ID."""
    try:
        delete_query(db_session, query_id)
        return JSONResponse(
            content={"message": f"Query {query_id} deleted."},
            status_code=200,
        )
    except Exception as e:
        logger.exception("Error deleting query", exc_info=e)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/get_relevant_docs_by_query_id/{query_id}")
def get_relevant_docs_by_query_id_(query_id: str, db_session: Session = Depends(get_db), auth=Depends(get_auth)):
    """Get all queries by session id.

    Args:
        query_id: query identifier.
        auth: Azure AD authentication.
        db_session: database session.

    Returns:
        JSONResponse: docs.
    """
    try:
        docs = get_relevant_docs_by_query_id(db_session, query_id)
        return JSONResponse(content=docs, status_code=200)
    except Exception as e:
        logger.exception("Error during get_queries_by_session_id process", exc_info=e)
        raise HTTPException(status_code=500, detail=str(e))
