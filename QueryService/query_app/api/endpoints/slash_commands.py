from fastapi import <PERSON><PERSON><PERSON>er, Depends, HTTPException
from query_app.api.models.request import (
    SlashCommandRegistrationRequest,
    SlashCommandRequest,
)
from query_app.api.models.response import (
    MessageResponse,
    SlashCommandInfo,
    SlashCommandListResponse,
    SlashCommandResponse,
)
from query_app.services.auth import get_auth
from query_app.slash_commands import SlashCommand, slash_command_manager

router = APIRouter()


@router.post("/execute", response_model=SlashCommandResponse)
async def execute_slash_command(request: SlashCommandRequest, auth=Depends(get_auth)) -> SlashCommandResponse:
    """
    Execute a slash command.

    This endpoint allows users to execute slash commands such as /help, /clear, /websearch, etc.
    The command should include the command name and any required arguments.

    Args:
        request: The slash command request containing the command text
        auth: Entra ID authentication

    Returns:
        SlashCommandResponse: The result of executing the command

    Raises:
        HTTPException: If the command is invalid or execution fails

    Examples:
        - Execute help command: `{"command": "/help"}`
        - Execute websearch: `{"command": "/websearch artificial intelligence"}`
        - Execute clear: `{"command": "/clear"}`
    """
    try:
        result = slash_command_manager.execute_command(request.command)
        return SlashCommandResponse(**result)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/list", response_model=SlashCommandListResponse)
async def list_commands(auth=Depends(get_auth)) -> SlashCommandListResponse:
    """
    List all available slash commands.

    This endpoint returns a list of all registered slash commands with their descriptions,
    argument requirements, and usage instructions.

    Args:
        auth: Entra ID authentication

    Returns:
        SlashCommandListResponse: List of available commands with their details

    Examples:
        Response will include commands like:
        - help: Show available commands and their descriptions
        - clear: Clear the current chat history
        - websearch: Search the web for information (requires arguments)
    """
    commands = slash_command_manager.get_all_commands()
    command_infos = [
        SlashCommandInfo(name=cmd.name, description=cmd.description, requires_args=cmd.requires_args, arg_description=cmd.arg_description)
        for cmd in sorted(commands, key=lambda x: x.name)
    ]
    return SlashCommandListResponse(commands=command_infos)


@router.post("/register", response_model=MessageResponse)
async def register_command(request: SlashCommandRegistrationRequest, auth=Depends(get_auth)) -> MessageResponse:
    """
    Register a new slash command.

    This endpoint allows administrators to register new slash commands dynamically.
    The new command will be available immediately after registration.

    Args:
        request: The command registration request with command details
        auth: Entra ID authentication

    Returns:
        MessageResponse: Confirmation message of successful registration

    Raises:
        HTTPException: If the command name already exists or registration fails

    Examples:
        Register an echo command:
        ```json
        {
            "name": "echo",
            "description": "Echo back the input text",
            "requires_args": true,
            "arg_description": "Usage: /echo <text>"
        }
        ```
    """
    try:
        # Create a new command with a simple handler
        command = SlashCommand(
            name=request.name,
            handler=lambda args=None: {"message": f"Command {request.name} executed" + (f" with args: {args}" if args else "")},
            description=request.description,
            requires_args=request.requires_args,
            arg_description=request.arg_description,
        )
        slash_command_manager.register_command(command)
        return MessageResponse(message=f"Command /{request.name} registered successfully")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{command_name}", response_model=MessageResponse)
async def remove_command(command_name: str, auth=Depends(get_auth)) -> MessageResponse:
    """
    Remove a slash command.

    This endpoint allows administrators to remove existing slash commands.
    Once removed, the command will no longer be available for execution.

    Args:
        command_name: The name of the command to remove (without the leading slash)
        auth: Entra ID authentication

    Returns:
        MessageResponse: Confirmation message of successful removal

    Raises:
        HTTPException: If the command doesn't exist or removal fails

    Examples:
        Remove the echo command: DELETE /slash-commands/echo
    """
    try:
        slash_command_manager.remove_command(command_name)
        return MessageResponse(message=f"Command /{command_name} removed successfully")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
