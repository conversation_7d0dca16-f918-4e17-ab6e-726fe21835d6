import json
import logging

from fastapi.encoders import jsonable_encoder
from query_app.api.core.query import get_custom_config_from_collection
from query_app.api.models.request import QueryRequest
from query_app.api.models.response import QueryResponse
from query_app.api.utils.helpers import create_headers
from query_app.crud.query import get_query, insert_query, update_query
from query_app.crud.user_sessions import get_or_create_user_session_with_title
from query_app.service_clients.loader_service_client.asynchronous import (
    verify_collection_existence,
)
from query_app.services.semantic_search import SemanticSearch
from query_app.utils.helpers import adjust_content_mapper, markdown_to_html
from starlette.responses import StreamingResponse

logger = logging.getLogger(__name__)


async def handle_post_query(collection_id: str, query_request: QueryRequest, db_session, auth):
    """Handles a POST request to process a query against a specified collection."""
    headers = create_headers(auth)
    collection = await verify_collection_existence(collection_id=collection_id, headers=headers)
    (
        custom_config,
        _embedding_model_name,
        _llm_model_name,
        _method,
        custom_config_system_prompt,
        is_memory_on,
        query_history_ttl,
        reranking_on,
        reranker_small_llm_model_name,
        is_web_search_on,
        web_search_llm_model_name,
        is_slash_commands_on,
        system_prompt_length_limit,
        collection_search_method,
    ) = get_custom_config_from_collection(collection)

    session = get_or_create_user_session_with_title(
        db_session,
        auth,
        query_request.session_id,
        query_request.content,
        collection_id,
    )

    thread_id = query_request.session_id or session.id or collection_id
    logger.info(f"User session: {session.id=}, {session.title=}, {session.user_email=}")

    # get custom system prompt if exists
    custom_system_prompt = query_request.custom_prompt
    if custom_system_prompt:
        if len(custom_system_prompt) > system_prompt_length_limit:
            raise ValueError(f"Custom system prompt too long: {len(custom_system_prompt)} > {system_prompt_length_limit}")
        logger.info(f"Using custom system prompt: {custom_system_prompt}")
        custom_config["system_prompt"] = custom_system_prompt
    else:
        custom_system_prompt = custom_config_system_prompt
        logger.info(f"Using collection's system prompt: {custom_system_prompt}")

    logger.info(f"Query: {session.id=} {_method=} {_embedding_model_name=} {_llm_model_name=} {custom_system_prompt=}, {query_request.search_method=}")

    if not query_request.inline_pdf_content:
        query_request.inline_pdf_content = ""

    # log query details in db
    query_id = insert_query(
        db_session,
        query_request=query_request,
        collection_id=collection_id,
        session_id=session.id,
    )

    # retrieve data based on the method specified in collection custom config
    search_method = SemanticSearch(
        collection_name=collection.get("name"),
        embedding_model_name=_embedding_model_name,
        llm_model_name=_llm_model_name,
        custom_system_prompt=custom_system_prompt,
        search_method=query_request.search_method or collection_search_method,
        is_memory_on=is_memory_on,
        session_id=str(thread_id),
        streaming=False,
        query_history_ttl=query_history_ttl,
        re_rank=reranking_on,
        re_rank_small_llm_model_name=reranker_small_llm_model_name,
        check_web_service=is_web_search_on,
        web_llm_model_name=web_search_llm_model_name,
        user_email=session.user_email,
    )

    retrieved_docs, retrieved_docs_mmr, _ = search_method.retrieve_relevant_docs(query_request.content + query_request.inline_pdf_content)
    response_content = search_method.query(query_request.content + query_request.inline_pdf_content)
    answer = response_content["answer" if is_memory_on else "result"]
    # inject custom config into results for logging
    response_content["custom_config"] = custom_config

    # update query details
    update_query(
        db_session,
        query_id,
        collection_id=collection_id,
        answer=answer,
        raw_response=jsonable_encoder(response_content),
        retrieved_docs=jsonable_encoder(retrieved_docs),
        retrieved_docs_mmr=jsonable_encoder(retrieved_docs_mmr),
    )

    response = QueryResponse(
        query_id=str(query_id),
        content=answer,
        retrieved_docs={
            "docs": jsonable_encoder(retrieved_docs),
            "docs_mmr": jsonable_encoder(retrieved_docs_mmr),
        },
        message=None,
        session_id=str(thread_id),
    )

    if query_request.markdown_support_on:
        response.content = markdown_to_html(response.content)

    return response


async def handle_post_query_stream(db_session, collection_id, query_request, auth, update=False):
    """Handles a POST request to process a query against a specified collection with streaming response."""
    headers = create_headers(auth)
    collection = await verify_collection_existence(collection_id=collection_id, headers=headers)
    (
        custom_config,
        _embedding_model_name,
        _llm_model_name,
        _method,
        custom_config_system_prompt,
        is_memory_on,
        query_history_ttl,
        reranking_on,
        reranker_small_llm_model_name,
        is_web_search_on,
        web_search_llm_model_name,
        is_slash_commands_on,
        system_prompt_length_limit,
        collection_search_method,
    ) = get_custom_config_from_collection(collection)

    # SESSION CREATION
    if update:
        session = get_or_create_user_session_with_title(db_session, auth, query_request.session_id, collection_id=collection_id)
    else:
        session = get_or_create_user_session_with_title(db_session, auth, query_request.session_id, query_request.content, collection_id)

    # SYSTEM PROMPT
    custom_system_prompt = query_request.custom_prompt
    if custom_system_prompt:
        if len(custom_system_prompt) > system_prompt_length_limit:
            raise ValueError(f"Custom system prompt too long: {len(custom_system_prompt)} > {system_prompt_length_limit}")
        logger.info(f"Using custom system prompt: {custom_system_prompt}")
        custom_config["system_prompt"] = custom_system_prompt
    else:
        custom_system_prompt = custom_config.get("system_prompt")
        logger.info(f"Using collection's system prompt: {custom_system_prompt}")
    if session and session.custom_config.get("system_prompt"):
        if custom_system_prompt is None:
            custom_system_prompt = ""
        custom_system_prompt += session.custom_config.get("system_prompt")
        logger.info(f"Added session-specific system prompt for this session: {custom_system_prompt}")

    # QUERY CREATION if not update
    if update:
        query_id = query_request.query_id
        query = get_query(db_session, query_id, collection_id=collection_id)
        question = adjust_content_mapper(query_request.adjust_type, query.content, query.answer)
        raw_question = query.content
    else:
        raw_question = query_request.content
        question = raw_question + (query_request.inline_pdf_content or "")
        query_id = insert_query(
            db_session,
            query_request=query_request,
            collection_id=collection_id,
            session_id=session.id,
        )

    # GENERATE ANSWER
    thread_id = query_request.session_id or session.id or collection_id
    # retrieve data based on the method specified in collection custom config
    semantic_search = SemanticSearch(
        collection_name=collection.get("name"),
        embedding_model_name=_embedding_model_name,
        llm_model_name=_llm_model_name,
        custom_system_prompt=custom_system_prompt,
        session_id=str(thread_id),
        streaming=True,
        is_memory_on=is_memory_on,
        query_history_ttl=query_history_ttl,
        re_rank=reranking_on,
        re_rank_small_llm_model_name=reranker_small_llm_model_name,
        check_web_service=is_web_search_on,
        web_llm_model_name=web_search_llm_model_name,
        search_method=query_request.search_method or collection_search_method,
        user_email=session.user_email,
    )

    retrieved_docs, retrieved_docs_mmr, is_web_searched = semantic_search.retrieve_relevant_docs(raw_question)
    retrieved_docs = retrieved_docs if not is_web_searched else retrieved_docs_mmr

    async def generate_streaming_content():
        """Generates streaming content for the query response."""

        answer_chunks = []  # Collect chunks to derive answer
        async for chunk in semantic_search.query_stream(question, retrieved_docs):
            chunk = chunk if isinstance(chunk, str) else (chunk.content if hasattr(chunk, "content") else str(chunk))
            answer_chunks.append(chunk)
            yield json.dumps(
                {
                    "content": chunk,
                    "retrieved_docs": jsonable_encoder(retrieved_docs),
                    "docs_mmr": jsonable_encoder(retrieved_docs_mmr),
                    "query_id": str(query_id),
                    "session_id": str(session.id),
                    "is_web_searched": is_web_searched,
                }
            )

        result = "".join(answer_chunks)
        update_query(
            db_session,
            query_id,
            collection_id=collection_id,
            answer=result,
            raw_response=jsonable_encoder(result),
            retrieved_docs=jsonable_encoder(retrieved_docs),
            retrieved_docs_mmr=jsonable_encoder(retrieved_docs_mmr),
        )

    return StreamingResponse(
        generate_streaming_content(),
        media_type="text/plain",
        status_code=200,
        headers={"X-Sessions-Id": str(session.id)},
    )
