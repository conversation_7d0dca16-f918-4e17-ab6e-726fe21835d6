"""Handles feedback-related operations such as adding, retrieving, and deleting feedback."""

import logging

from fastapi import HTTPException
from query_app.api.models.request import AddFeedbackRequest
from query_app.api.models.response import (
    AddFeedbackResponse,
    FeedbackItem,
    GetFeedbacksResponse,
    GetFeedbackStatusResponse,
    MessageResponse,
)
from query_app.crud.feedback import (
    delete_feedback,
    delete_feedbacks_by_query_id,
    get_feedback,
    get_feedbacks,
    insert_feedback,
)
from query_app.enums.feedback import FeedbackStatusEnum
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


async def handle_post_feedback(
    db: Session,
    feedback_request: AddFeedbackRequest,
) -> AddFeedbackResponse:
    """Handles the addition of feedback."""
    feedback_request.status = FeedbackStatusEnum.ADDED.value
    try:
        feedback_id = insert_feedback(db, feedback_request)
        feedback = get_feedback(db, feedback_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception:
        logger.exception("Unexpected error while inserting feedback")
        raise HTTPException(status_code=500, detail="Unexpected error")

    if not feedback:
        raise HTTPException(status_code=400, detail="Unable to add feedback")

    return AddFeedbackResponse(feedback_id=feedback.id, status=feedback.status)


async def handle_get_feedback_status(
    db: Session,
    feedback_id: str,
) -> GetFeedbackStatusResponse:
    """Handles retrieval of feedback status by ID."""
    try:
        feedback = get_feedback(db, feedback_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid feedback ID")

    if not feedback:
        raise HTTPException(status_code=404, detail="Feedback not found")

    return GetFeedbackStatusResponse(status=FeedbackStatusEnum(feedback.status))


async def handle_delete_feedback(db: Session, feedback_id: str) -> MessageResponse:
    """Handles deletion of feedback by ID."""
    try:
        deleted = delete_feedback(db, feedback_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid feedback ID")
    except Exception:
        logger.exception("failed to delete feedback")
        raise HTTPException(status_code=500, detail="Internal server error")

    if not deleted:
        raise HTTPException(status_code=404, detail="Feedback not found")

    return MessageResponse(message="ok")


async def handle_get_feedbacks(db: Session) -> GetFeedbacksResponse:
    """Handles retrieval of all feedbacks."""
    try:
        feedbacks = get_feedbacks(db)
    except Exception:
        logger.exception("failed to fetch feedbacks")
        raise HTTPException(status_code=500, detail="failed to fetch feedbacks")

    feedback_items = [FeedbackItem.model_validate(fb) for fb in feedbacks]
    return GetFeedbacksResponse(feedbacks=feedback_items)


async def handle_delete_feedbacks_by_query_id(db: Session, query_id: str) -> MessageResponse:
    """Handles deletion of feedbacks associated with a specific query ID."""
    try:
        deleted_count = delete_feedbacks_by_query_id(db, query_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception:
        logger.exception("error deleting feedbacks by query_id")
        raise HTTPException(status_code=500, detail="internal server error")

    return MessageResponse(message=f"{deleted_count} feedback(s) deleted for query {query_id}")
