from query_app.enums.search import ChatHistoryTTL, SearchMethod
from query_app.llm.enums import ReRankLLMModels, WebLLMModels
from query_app.llm.models import (
    DEFAULT_EMBEDDING_MODEL,
    DEFAULT_LLM_MODEL,
    DEFAULT_SEARCH_METHOD,
)


def get_custom_config_from_collection(collection: dict):
    """Get custom config from collection."""
    custom_config = collection.get("custom_config", {})
    embedding_model_name = custom_config.get("embedding_model", DEFAULT_EMBEDDING_MODEL)
    llm_model_name = custom_config.get("llm_model", DEFAULT_LLM_MODEL)
    method = custom_config.get("method", DEFAULT_SEARCH_METHOD)
    system_prompt = custom_config.get("system_prompt")
    is_memory_on = custom_config.get("is_memory_on", True)
    query_history_ttl = custom_config.get("query_history_ttl", ChatHistoryTTL.ONE_MONTH.value)
    reranking_on = custom_config.get("reranking_on", False)
    reranker_small_llm_model_name = custom_config.get("reranker_small_llm_model", ReRankLLMModels.GPT4OMINI.value)
    is_web_search_on = custom_config.get("is_web_search_on", False)
    web_search_llm_model_name = custom_config.get("web_search_llm_model", WebLLMModels.GPT4OMINI.value)
    is_slash_commands_on = custom_config.get("is_slash_commands_on", True)
    system_prompt_length_limit = custom_config.get("system_prompt_length_limit", 6000)
    collection_search_method = custom_config.get("collection_search_method", SearchMethod.SIMILARITY.value)
    return (
        custom_config,
        embedding_model_name,
        llm_model_name,
        method,
        system_prompt,
        is_memory_on,
        query_history_ttl,
        reranking_on,
        reranker_small_llm_model_name,
        is_web_search_on,
        web_search_llm_model_name,
        is_slash_commands_on,
        system_prompt_length_limit,
        collection_search_method,
    )
