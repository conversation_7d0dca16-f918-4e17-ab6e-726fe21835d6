"""Document-related status and event enumerations."""

from enum import Enum


class DocumentStatusEnum(str, Enum):
    """Enumeration of possible document statuses."""

    ADDED = "added"
    """Document has been added to the system."""

    LOADING = "loading"
    """Document is currently being loaded."""

    READY_TO_BE_INDEXED = "ready_to_be_indexed"
    """Document is ready for the embedding and indexing process."""

    EXTRACTING_EMBEDDINGS = "extracting_embeddings"
    """Document is currently undergoing embedding extraction."""

    INDEXED = "indexed"
    """Document has been successfully indexed."""

    FAILED = "failed"
    """An error occurred during document processing."""

    DELETED = "deleted"
    """Document has been deleted."""


class DocumentEventsEnum(str, Enum):
    """Enumeration of document-related processing events."""

    DELETE_REQUEST = "delete_request"
    """Request to delete the document."""

    LOAD_REQUEST = "loading_request"
    """Request to load the document."""

    LOAD_FINISHED = "loading_finished"
    """Document loading completed successfully."""

    LOAD_FAILED = "loading_failed"
    """Document loading failed."""

    EMBEDDING_REQUEST = "embedding_request"
    """Request to start embedding extraction."""

    EMBEDDING_FINISHED = "embedding_finished"
    """Embedding extraction completed successfully."""

    EMBEDDING_FAILED = "embedding_failed"
    """Embedding extraction failed."""
