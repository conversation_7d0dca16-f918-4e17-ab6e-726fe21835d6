from dataclasses import dataclass
from enum import Enum
from typing import Optional


class SearchMethodsEnum(str, Enum):
    """Enum for different search methods used in the application."""

    SEMANTIC_SEARCH = "semantic_search"
    HYDE = "hyde"


class SearchMethod(str, Enum):
    """Enum for different search methods used in the application."""

    SIMILARITY = "similarity"
    MMR = "mmr"
    KEYWORD = "keyword"
    HYBRID = "hybrid"
    BM25 = "bm25"
    FUSED = "fused"
    RERANK = "rerank"
    ELASTICSEARCH = "elastic search"


@dataclass
class QueryExecutionConfig:
    """Configuration for query execution."""

    embedding_model: str
    llm_model: str
    method: str
    system_prompt: Optional[str]
    search_method: Optional[str]
    custom_config: dict


class ChatHistoryTTL(Enum):
    """Enum for different time-to-live (TTL) values for chat history."""

    ONE_DAY = 60 * 60 * 24  # 86400 sec
    ONE_WEEK = 60 * 60 * 24 * 7  # 604800 sec
    TWO_WEEKS = 60 * 60 * 24 * 14  # 1209600 sec
    THREE_WEEKS = 60 * 60 * 24 * 21  # 1814400 sec
    ONE_MONTH = 60 * 60 * 24 * 30  # 2592000 sec
    SIX_MONTHS = 60 * 60 * 24 * 180  # 15552000 sec
    ONE_YEAR = 60 * 60 * 24 * 365  # 31536000 sec
