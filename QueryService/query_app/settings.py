from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """App settings."""

    # auth
    is_debug: bool = False
    is_entra_id_auth_on: bool = True

    app_host: str = "0.0.0.0"
    app_port: int = 8003

    # OpenAI
    embedding_ada_model_name: str = "text-embedding-ada-002"
    embedding_ada_deployment_name: str = "ada3"
    gpt4_model_name: str = "gpt-4"
    gpt4_deployment_name: str = "gpt4"
    gpt41_model_name: str = "gpt-4.1"
    gpt41_deployment_name: str = "gpt-4.1"
    gpt4o2_model_name: str = "gpt-4o"
    gpt4o2_deployment_name: str = "gpt4o"
    gpt4o_model_name: str = "gpt-4o"
    gpt4o_deployment_name: str = "gpt4o"
    gpt4o_mini_model_name: str = "gpt-4o-mini"
    gpt4o_mini_deployment_name: str = "gpt4o-mini"

    openai_api_type: str = ""
    openai_api_version: str = ""
    azure_openai_endpoint: str = ""
    openai_api_key: str = ""

    # Open Source
    llama3_1_instruct_model_name: str = "Meta-Llama-3.1-70B-Instruct"
    mixtral_model_name: str = "Mistral-8B"
    phi4_model_name: str = "Phi-4"
    bgem3_model_name: str = "bge-m3"
    opensource_llm_api_base: str = ""
    opensource_llm_api_key: str = ""

    # default values
    default_model_temperature: float = 0.7
    default_llm_multi_answer_count: int = 4
    default_embedding_model: str = "text-embedding-ada-002"
    default_llm_model: str = "gpt-4o"
    default_search_method: str = "semantic_search"
    langchain_debug: bool = False
    restricted_indexing: bool = True  # to avoid bottleneck in openai services
    evaluator_log_dir: str = "/data/evaluation_logs/"
    max_token_count: int = 4096
    max_token_count_for_small_models: int = 1024
    semantic_search_top_k: int = 5
    hyde_top_k: int = 5
    hyde_generation_max_token_count: int = 256

    # chunker
    chunk_size: int = 1000  # Reduced from 2048 for better precision
    chunk_overlap: int = 150  # Increased overlap for better context
    separators: list = [
        "\n\n",
        "\n",
        r"(?<=\. )",  # <--- raw string!
        " ",
        "",
        ".",
        ",",
        "\u200b",  # Zero-width space
        "\uff0c",  # Fullwidth comma
        "\u3001",  # Ideographic comma
        "\uff0e",  # Fullwidth full stop
        "\u3002",  # Ideographic full stop
    ]

    # Enhanced search settings
    enable_cross_encoder: bool = True  # Enabled for better reranking
    cross_encoder_model: str = "BAAI/bge-reranker-base"
    enable_knowledge_graph: bool = False
    graph_db_url: str = "bolt://localhost:7687"
    enable_elasticsearch: bool = False
    elasticsearch_url: str = "http://localhost:9200"

    # Advanced chunking settings
    enable_semantic_chunking: bool = False
    semantic_chunking_model: str = "all-MiniLM-L6-v2"
    enable_adaptive_chunking: bool = False

    # Search method weights
    similarity_weight: float = 0.4
    mmr_weight: float = 0.3
    keyword_weight: float = 0.2
    graph_weight: float = 0.1

    # Query classification
    enable_query_classification: bool = True

    # Azure Data Web Service Configuration
    azure_ai_api_key: str = ""  # Set via environment variable - used for Azure OpenAI API authentication
    azure_ai_endpoint: str = "https://aifoundry-fbeta.cognitiveservices.azure.com/"  # Azure OpenAI endpoint for constructing deployment URLs

    # Web search settings for AzureDataWebService
    enable_azure_ai_search: bool = True  # Enable Azure Data Web Service or fallback to DuckDuckGo
    web_search_max_results: int = 5  # Maximum number of search results to retrieve
    web_search_decision_model: str = "gpt-4.1"  # Model name for web search decision making
    web_search_decision_deployment: str = "gpt-4.1"  # Azure deployment name
    web_search_api_version: str = "2025-01-01-preview"  # Azure OpenAI API version for web search

    # Context-aware search
    enable_context_aware_search: bool = True
    session_history_limit: int = 5

    # dramatiq
    dramatiq_name_space: str = "embedding-dramatiq"
    dramatiq_task_time_limit_ms: int = 3600000  # 60 minutes
    dramatiq_task_max_retries: int = 5
    dramatiq_task_max_age_ms: int = 86400000  # 24 hour
    dramatiq_queue_prefix: str = "prod"
    redis_host: str = "redis"
    redis_port: int = 6379
    dramatiq_redis_db: int = 0
    embeddings_redis_db: int = 1
    redis_query_history_db: int = 2
    redis_url: str = f"redis://{redis_host}:{redis_port}"
    redis_dramatiq_url: str = f"redis://{redis_host}:{redis_port}/{dramatiq_redis_db}"
    redis_embeddings_url: str = f"redis://{redis_host}:{redis_port}/{embeddings_redis_db}"
    redis_query_history_url: str = f"redis://{redis_host}:{redis_port}/{redis_query_history_db}"

    # rabbitmq
    rabbitmq_user: str = "guest"
    rabbitmq_password: str = "guest"
    rabbitmq_host: str = "rabbitmq"
    rabbitmq_port: int = 5672
    rabbitmq_url: str = f"amqp://{rabbitmq_user}:{rabbitmq_password}@{rabbitmq_host}:{rabbitmq_port}"
    rabbitmq_vhost: str = "/"

    # postgres
    postgres_host: str = "postgres_query"
    postgres_port: int = 5432
    postgres_db: str = "postgres_query"
    postgres_user: str = "postgres_query"
    postgres_password: str = "postgres_query"
    db_url: str = f"postgresql+psycopg://{postgres_user}:{postgres_password}@{postgres_host}:{postgres_port}/${postgres_db}"

    # Vector DB
    pgvector_host: str = "postgres_indexer"
    pgvector_port: int = 5432
    pgvector_db: str = "postgres_indexer"
    pgvector_user: str = "postgres_indexer"
    pgvector_password: str = "postgres_indexer"
    pgvector_url: str = f"postgresql+psycopg://{pgvector_user}:{pgvector_password}@{pgvector_host}:{pgvector_port}/{pgvector_db}"

    # ElasticSearch
    elastic_enabled: bool = True
    elastic_host: str = "elasticsearch"
    elastic_port: int = 9200

    # Azure AD
    app_client_id: str = ""
    app_client_secret: str = ""
    tenant_id: str = ""
    openapi_client_id: str = ""
    scope_description: str = "user_impersonation"
    client_ui_url: str = "http://localhost:8005"

    # Service Clients
    indexer_base_url: str = "http://indexer:8001"
    loader_base_url: str = "http://loader:8002"
    query_base_url: str = "http://query:8003"
    prompt_base_url: str = "https://prompt-service.dev.fbeta.tech/api"

    # hash key
    hash_key: str = ""

    # Microsoft Entra ID
    @property
    def scope_name(self) -> str:
        """Returns the scope name for Microsoft Entra ID authentication."""
        return f"api://{self.app_client_id}/{self.scope_description}"

    @property
    def scopes(self) -> dict:
        """Returns the scopes required for Microsoft Entra ID authentication."""
        return {
            self.scope_name: self.scope_description,
        }

    @property
    def openapi_authorization_url(self) -> str:
        """Returns the authorization URL for Microsoft Entra ID OpenAPI."""
        return f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/authorize"

    @property
    def openapi_token_url(self) -> str:
        """Returns the token URL for Microsoft Entra ID OpenAPI."""
        return f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"

    @property
    def elastic_url(self) -> str:
        """Return full URL for Elasticsearch."""
        return f"http://{self.elastic_host}:{self.elastic_port}"

    class Config(object):
        """Config for pydantic base settings."""

        case_sensitive = False


settings = Settings()
