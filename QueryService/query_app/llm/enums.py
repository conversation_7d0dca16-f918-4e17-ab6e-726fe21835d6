from enum import Enum


class OPENAI_EMBEDDING_MODELS(str, Enum):
    """Enum for embedding models used in the application."""

    ADA002 = "text-embedding-ada-002"


class OPENAI_MODELS(str, Enum):
    """Enum for OpenAI models used in the application."""

    GPT4 = "gpt-4"
    GPT41 = "gpt-4.1"
    GPT4O2 = "gpt-4o-2"
    GPT4O = "gpt-4o"
    GPT4OMINI = "gpt-4o-mini"


class OS_MODELS(str, Enum):
    """Enum for Open Source models."""

    LLAMA3170BINSTRUCT = "Meta-Llama-3.1-70B-Instruct"
    MIXTRAL8B = "Mistral-8B"
    PHI4 = "Phi-4"


class OS_EMBEDDING_MODELS(str, Enum):
    """Enum for Open-source embedding-only models."""

    BGEM3 = "bge-m3"


class ReRankLLMModels(str, Enum):
    """Enum for re-ranking LLM models used in the application."""

    GPT4OMINI = "gpt-4o-mini"
    LLAMA270CHATHF = "llama-2-70b-chat-hf"
    LLAMA370CHATHF = "Meta-Llama-3-70B-Instruct"
    LLAMA3170BINSTRUCT = "Meta-Llama-3.1-70B-Instruct"


MIXTRAL8B = "Mistral-8B"


class WebLLMModels(str, Enum):
    """Enum for web LLM models used in the application."""

    GPT4OMINI = "gpt-4o-mini"
    LLAMA270CHATHF = "llama-2-70b-chat-hf"
    LLAMA370CHATHF = "Meta-Llama-3-70B-Instruct"
    LLAMA3170BINSTRUCT = "Meta-Llama-3.1-70B-Instruct"


MIXTRAL8B = "Mistral-8B"
