"""Common module for managing LLM models and embeddings in the QueryService application."""

import logging
from dataclasses import dataclass
from typing import Optional

from langchain_openai import (
    AzureChatOpenAI,
    AzureOpenAIEmbeddings,
    ChatOpenAI,
    OpenAIEmbeddings,
)
from query_app.llm.enums import (
    OPENAI_EMBEDDING_MODELS,
    OPENAI_MODELS,
    OS_EMBEDDING_MODELS,
    OS_MODELS,
)
from query_app.settings import settings

logger = logging.getLogger(__name__)


@dataclass
class Model:
    """Model dataclass."""

    name: str
    api_base: Optional[str] = ""
    api_key: Optional[str] = ""
    model_name: Optional[str] = ""
    deployment_name: Optional[str] = ""
    api_type: Optional[str] = ""
    api_version: Optional[str] = ""
    model_temperature: Optional[float] = settings.default_model_temperature
    multi_answer_count: Optional[int] = settings.default_llm_multi_answer_count
    streaming: bool = False
    encoding_format: Optional[str] = "float"

    def __post_init__(self):
        """Model post-initialization method to set up the LLM and embedding models.
        This method initializes the chat model, multi-answer LLM, and embedding model based on the model type.
        It checks the model name against predefined enums to determine the appropriate model type and initializes
        the corresponding LangChain models.
        If the model is an OpenAI model, it initializes the AzureChatOpenAI or ChatOpenAI models accordingly.
        If the model is an embedding model, it initializes the OpenAIEmbeddings or AzureOpenAIEmbeddings models.
        If the model is not recognized, it raises a ValueError.
        This method also handles the streaming option, setting it to True or False based on the `streaming` attribute.
        It uses the `settings` module to get configuration values such as `max_token_count` and `max_token_count_for_small_models`.
        :return: None
        :raises ValueError: If the model name is not recognized in the predefined enums.
        :rtype: None
        :raises Exception: If there is an issue initializing the embedding model.
        :rtype: None
        :note: The `streaming` attribute determines whether the model supports streaming responses.
        :note: The `multi_answer_count` attribute determines how many answers the multi-answer LLM should return.
        :note: The `encoding_format` attribute is used for embedding models to specify the format of the embeddings.
        :note: The `api_base`, `api_key`, `model_name`, and `deployment_name` attributes are used to configure the model.
        :note: The `api_type` and `api_version` attributes are used for OpenAI models to specify the API type and version.
        :note: The `model_temperature` attribute is used to set the temperature for the model, affecting the randomness of the responses.
        :note: The `name` attribute is used to identify the model and should match one of the predefined enums.
        :note: The `name` attribute should be one of the values defined in the `EMBEDDING_MODELS`, `OPENAI_MODELS`, or `OS_MODELS` enums.
        """
        streaming = True if self.streaming else False
        n_value = 1 if streaming else self.multi_answer_count

        if self.name in OS_EMBEDDING_MODELS._value2member_map_:
            self.embedding_model = OpenAIEmbeddings(
                openai_api_key=self.api_key,
                openai_api_base=self.api_base,
                model=self.model_name,
                # encoding_format=self.encoding_format,
            )

        elif self.name in OS_MODELS._value2member_map_:
            self.chat_model = ChatOpenAI(
                openai_api_key=self.api_key,
                openai_api_base=self.api_base,
                model_name=self.model_name,
                max_tokens=settings.max_token_count,
                streaming=streaming,
            )
            self.multi_llm = ChatOpenAI(
                openai_api_key=self.api_key,
                openai_api_base=self.api_base,
                model_name=self.model_name,
                n=n_value,
                max_tokens=settings.max_token_count,
                streaming=streaming,
            )

        elif self.name in OPENAI_MODELS._value2member_map_:
            max_token = settings.max_token_count_for_small_models if self.model_name == settings.gpt4_model_name else settings.max_token_count
            if not self.deployment_name:
                raise ValueError("deployment_name should be defined for openai models")

            self.chat_model = AzureChatOpenAI(
                openai_api_key=self.api_key,
                azure_endpoint=self.api_base,
                model=self.model_name,  # Use 'model' instead of 'model_name' for cost tracking
                temperature=self.model_temperature,
                azure_deployment=self.deployment_name,
                max_tokens=max_token,
                streaming=streaming,
                openai_api_version=self.api_version,
                openai_api_type=self.api_type,
            )
            self.multi_llm = AzureChatOpenAI(
                openai_api_key=self.api_key,
                azure_endpoint=self.api_base,
                model=self.model_name,  # Use 'model' instead of 'model_name' for cost tracking
                temperature=self.model_temperature,
                azure_deployment=self.deployment_name,
                n=n_value,
                max_tokens=max_token,
                streaming=streaming,
                openai_api_version=self.api_version,
                openai_api_type=self.api_type,
            )
        elif self.name in OPENAI_EMBEDDING_MODELS._value2member_map_:
            self.embedding_model = AzureOpenAIEmbeddings(
                openai_api_key=self.api_key,
                model=self.model_name,
                azure_endpoint=self.api_base,
                azure_deployment=self.deployment_name,
                # encoding_format=self.encoding_format,
            )
        else:
            raise ValueError("undefined model")


MODEL_PRESETS = {
    OPENAI_EMBEDDING_MODELS.ADA002.value: Model(
        name=OPENAI_EMBEDDING_MODELS.ADA002.value,
        model_name=settings.embedding_ada_model_name,
        deployment_name=settings.embedding_ada_deployment_name,
        api_key=settings.openai_api_key,
        api_base=settings.azure_openai_endpoint,
    ),
    OPENAI_MODELS.GPT4.value: Model(
        name=OPENAI_MODELS.GPT4.value,
        model_name=settings.gpt4_model_name,
        deployment_name=settings.gpt4_deployment_name,
        api_key=settings.openai_api_key,
        api_base=settings.azure_openai_endpoint,
        api_type=settings.openai_api_type,
        api_version=settings.openai_api_version,
    ),
    OPENAI_MODELS.GPT41.value: Model(
        name=OPENAI_MODELS.GPT41.value,
        model_name=settings.gpt41_model_name,
        deployment_name=settings.gpt41_deployment_name,
        api_key=settings.openai_api_key,
        api_base=settings.azure_openai_endpoint,
        api_type=settings.openai_api_type,
        api_version=settings.openai_api_version,
    ),
    OPENAI_MODELS.GPT4O2.value: Model(
        name=OPENAI_MODELS.GPT4O2.value,
        model_name=settings.gpt4o2_model_name,
        deployment_name=settings.gpt4o2_deployment_name,
        api_key=settings.openai_api_key,
        api_base=settings.azure_openai_endpoint,
        api_type=settings.openai_api_type,
        api_version=settings.openai_api_version,
    ),
    OPENAI_MODELS.GPT4O.value: Model(
        name=OPENAI_MODELS.GPT4O.value,
        model_name=settings.gpt4o_model_name,
        deployment_name=settings.gpt4o_deployment_name,
        api_key=settings.openai_api_key,
        api_base=settings.azure_openai_endpoint,
        api_type=settings.openai_api_type,
        api_version=settings.openai_api_version,
    ),
    OPENAI_MODELS.GPT4OMINI.value: Model(
        name=OPENAI_MODELS.GPT4OMINI.value,
        model_name=settings.gpt4o_mini_model_name,
        deployment_name=settings.gpt4o_mini_deployment_name,
        api_key=settings.openai_api_key,
        api_base=settings.azure_openai_endpoint,
        api_type=settings.openai_api_type,
        api_version=settings.openai_api_version,
    ),
    OS_MODELS.MIXTRAL8B.value: Model(
        name=OS_MODELS.MIXTRAL8B.value,
        model_name=settings.mixtral_model_name,
        api_key=settings.opensource_llm_api_key,
        api_base=settings.opensource_llm_api_base,
    ),
    OS_MODELS.PHI4.value: Model(
        name=OS_MODELS.PHI4.value,
        model_name=settings.phi4_model_name,
        api_key=settings.opensource_llm_api_key,
        api_base=settings.opensource_llm_api_base,
    ),
    OS_EMBEDDING_MODELS.BGEM3.value: Model(
        name=OS_EMBEDDING_MODELS.BGEM3.value,
        model_name=settings.bgem3_model_name,
        api_key=settings.opensource_llm_api_key,
        api_base=settings.opensource_llm_api_base,
        encoding_format="float",
    ),
    OS_MODELS.LLAMA3170BINSTRUCT.value: Model(
        name=OS_MODELS.LLAMA3170BINSTRUCT.value,
        model_name=settings.llama3_1_instruct_model_name,
        api_key=settings.opensource_llm_api_key,
        api_base=settings.opensource_llm_api_base,
    ),
    f"{OPENAI_MODELS.GPT4.value}-stream": Model(
        name=OPENAI_MODELS.GPT4.value,
        model_name=settings.gpt4_model_name,
        deployment_name=settings.gpt4_deployment_name,
        api_key=settings.openai_api_key,
        api_base=settings.azure_openai_endpoint,
        api_type=settings.openai_api_type,
        api_version=settings.openai_api_version,
        streaming=True,
    ),
    f"{OPENAI_MODELS.GPT41.value}-stream": Model(
        name=OPENAI_MODELS.GPT41.value,
        model_name=settings.gpt41_model_name,
        deployment_name=settings.gpt41_deployment_name,
        api_key=settings.openai_api_key,
        api_base=settings.azure_openai_endpoint,
        api_type=settings.openai_api_type,
        api_version=settings.openai_api_version,
        streaming=True,
    ),
    f"{OPENAI_MODELS.GPT4O2.value}-stream": Model(
        name=OPENAI_MODELS.GPT4O2.value,
        model_name=settings.gpt4o2_model_name,
        deployment_name=settings.gpt4o2_deployment_name,
        api_key=settings.openai_api_key,
        api_base=settings.azure_openai_endpoint,
        api_type=settings.openai_api_type,
        api_version=settings.openai_api_version,
        streaming=True,
    ),
    f"{OPENAI_MODELS.GPT4O.value}-stream": Model(
        name=OPENAI_MODELS.GPT4O.value,
        model_name=settings.gpt4o_model_name,
        deployment_name=settings.gpt4o_deployment_name,
        api_key=settings.openai_api_key,
        api_base=settings.azure_openai_endpoint,
        api_type=settings.openai_api_type,
        api_version=settings.openai_api_version,
        streaming=True,
    ),
    f"{OPENAI_MODELS.GPT4OMINI.value}-stream": Model(
        name=OPENAI_MODELS.GPT4OMINI.value,
        model_name=settings.gpt4o_mini_model_name,
        deployment_name=settings.gpt4o_mini_deployment_name,
        api_key=settings.openai_api_key,
        api_base=settings.azure_openai_endpoint,
        api_type=settings.openai_api_type,
        api_version=settings.openai_api_version,
        streaming=True,
    ),
    f"{OS_MODELS.LLAMA3170BINSTRUCT.value}-stream": Model(
        name=OS_MODELS.LLAMA3170BINSTRUCT.value,
        model_name=settings.llama3_1_instruct_model_name,
        api_key=settings.opensource_llm_api_key,
        api_base=settings.opensource_llm_api_base,
        streaming=True,
    ),
    f"{OS_MODELS. MIXTRAL8B.value}-stream": Model(
        name=OS_MODELS.MIXTRAL8B.value,
        model_name=settings.mixtral_model_name,
        api_key=settings.opensource_llm_api_key,
        api_base=settings.opensource_llm_api_base,
        streaming=True,
    ),
    f"{OS_MODELS.PHI4.value}-stream": Model(
        name=OS_MODELS.PHI4.value,
        model_name=settings.phi4_model_name,
        api_key=settings.opensource_llm_api_key,
        api_base=settings.opensource_llm_api_base,
        streaming=True,
    ),
    f"{OS_EMBEDDING_MODELS.BGEM3.value}-stream": Model(
        name=OS_EMBEDDING_MODELS.BGEM3.value,
        model_name=settings.bgem3_model_name,
        api_key=settings.opensource_llm_api_key,
        api_base=settings.opensource_llm_api_base,
        streaming=True,
    ),
}

DEFAULT_EMBEDDING_MODEL = settings.default_embedding_model
DEFAULT_LLM_MODEL = settings.default_llm_model
DEFAULT_SEARCH_METHOD = settings.default_search_method
