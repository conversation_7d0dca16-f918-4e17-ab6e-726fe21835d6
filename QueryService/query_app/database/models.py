import pytz
from query_app.database import Base
from query_app.utils.helpers import generate_uuid, markdown_to_html
from sqlalchemy import BOOLEAN, JSON, Column, DateTime, ForeignKey, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func


class UserSession(Base):
    """Model - session."""

    __tablename__ = "user_sessions"

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=generate_uuid,
        unique=True,
        nullable=False,
    )
    collection_id = Column(UUID(as_uuid=True), nullable=False)
    user_email = Column(String, nullable=False)
    title = Column(String, nullable=False)
    uploaded_pdfs = Column(String, nullable=True)
    created_at = Column(DateTime, default=func.now())
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())
    custom_config = Column(JSON, nullable=True, default=dict)
    is_favorite = Column(BOOLEAN, nullable=True, default=False)
    goal = Column(String, nullable=True, default="")

    # Relationship with Query
    queries = relationship("Query", back_populates="session", lazy="subquery", cascade="all, delete-orphan")

    def to_dict(self):
        """Convert the UserSession instance to a dictionary."""
        return {
            "id": str(self.id),
            "collection_id": str(self.collection_id),
            "user_email": self.user_email,
            "title": self.title,
            "uploaded_pdfs": self.uploaded_pdfs,
            "created_at": self.created_at.isoformat(),
            "last_updated": self.last_updated.isoformat(),
            "is_favorite": self.is_favorite,
            "goal": self.goal,
        }


class Query(Base):
    """Model - query.

    Attributes:
        id (UUID): query identifier.
        request_id (UUID, optional): request identifier.
        collection_id (UUID, optional): collection identifier.
        content (str): content of the query.
        answer (str): answer.
        raw_request (JSON): raw request data.
        raw_response (JSON): raw response data.
        retrieved_docs (JSON): relevant documents data.
        retrieved_docs_mmr (JSON): MMR relevant documents data.
        created_at (DateTime): creation timestamp.
        last_updated (DateTime): update timestamp.
    """

    __tablename__ = "queries"

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=generate_uuid,
        unique=True,
        nullable=False,
    )
    session_id = Column(UUID(as_uuid=True), ForeignKey("user_sessions.id"), nullable=False)
    request_id = Column(UUID(as_uuid=True), primary_key=False, unique=False, nullable=True)
    collection_id = Column(UUID(as_uuid=True), unique=False, nullable=True)
    content = Column(String)
    answer = Column(String)
    raw_request = Column(JSON)
    raw_response = Column(JSON)
    retrieved_docs = Column(JSON)
    retrieved_docs_mmr = Column(JSON)
    created_at = Column(DateTime, default=func.now())
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())
    is_favorite = Column(BOOLEAN, nullable=True, default=False)

    # Relationship with UserSession
    session = relationship("UserSession", back_populates="queries", lazy="subquery")

    # Relationship with Feedback
    feedbacks = relationship("Feedback", back_populates="query", lazy="subquery")

    def to_dict(self, markdown_on=True):
        """Convert the Query instance to a dictionary."""
        EUROPE_TZ = pytz.timezone("Europe/Berlin")
        created_at_local = self.created_at.astimezone(EUROPE_TZ) if self.created_at else None
        updated_at_local = self.last_updated.astimezone(EUROPE_TZ) if self.last_updated else None

        # Process the answer based on markdown_on flag
        if markdown_on and self.answer:
            processed_answer = markdown_to_html(self.answer)
        else:
            processed_answer = self.answer

        return {
            "id": str(self.id),
            "content": self.content,
            "session_id": str(self.session_id),
            "request_id": str(self.request_id) if self.request_id else None,
            "collection_id": str(self.collection_id) if self.collection_id else None,
            "answer": processed_answer,
            "raw_request": self.raw_request,
            "raw_response": self.raw_response,
            "retrieved_docs": self.retrieved_docs,
            "retrieved_docs_mmr": self.retrieved_docs_mmr,
            "is_favorite": self.is_favorite,
            "created_at": created_at_local.isoformat() if created_at_local else None,
            "last_updated": updated_at_local.isoformat() if updated_at_local else None,
            "session_title": self.session.title,
            "len_of_retrieved_docs": len(self.retrieved_docs) if self.retrieved_docs else 0,
            "has_feedback": bool(self.feedbacks),
        }

    def retrieve_docs(self):
        """Retrieve relevant documents from the query."""
        return {
            "retrieved_docs": self.retrieved_docs,
            "retrieved_docs_mmr": self.retrieved_docs_mmr,
            "len_of_retrieved_docs": (len(self.retrieved_docs) if self.retrieved_docs else 0),
        }


class Feedback(Base):
    """Model - feedback.

    Attributes:
        id (UUID): feedback identifier.
        query_id (UUID, optional): query identifier.
        feedback_type (str): feedback type.
        feedback_value (str): feedback value.
        feedback_text (str, optional): feedback extra description.
        status (str): feedback status.
        created_at (DateTime): creation timestamp.
        last_updated (DateTime): update timestamp.
    """

    __tablename__ = "feedbacks"

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=generate_uuid,
        unique=True,
        nullable=False,
    )
    query_id = Column(UUID(as_uuid=True), ForeignKey("queries.id"), unique=False, nullable=True)
    collection_id = Column(UUID(as_uuid=True), unique=False, nullable=True)
    feedback_type = Column(String)
    feedback_value = Column(String)
    feedback_text = Column(String, nullable=True)
    status = Column(String)
    created_at = Column(DateTime, default=func.now())
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationship with Query
    query = relationship("Query", back_populates="feedbacks", lazy="subquery")

    def to_dict(self):
        """Convert the Feedback instance to a dictionary."""
        return {
            "id": str(self.id),
            "query_id": str(self.query_id),
            "collection_id": str(self.collection_id),
            "feedback_type": self.feedback_type,
            "feedback_value": self.feedback_value,
            "feedback_text": self.feedback_text,
            "status": self.status,
            "created_at": self.created_at.isoformat(),
            "last_updated": self.last_updated.isoformat(),
        }
