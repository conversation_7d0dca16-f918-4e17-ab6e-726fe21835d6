import logging

from query_app.database import Base, engine, models

logger = logging.getLogger(__name__)


def init_db() -> None:
    """Initalize db."""
    logger.info("Initializing database.")
    logger.info("Table creation started.")
    logger.info(f"Importing models for table creation {models}")
    # Create all tables in the database
    Base.metadata.create_all(bind=engine)
    logger.info(
        "Table creation completed. These are the tables created: %s",
        Base.metadata.sorted_tables,
    )
