from typing import Optional
from uuid import UUID

import httpx
from fastapi import HTTPException
from query_app.settings import settings


async def get_collection(
    collection_id: Optional[UUID] = None,
    collection_name: Optional[str] = None,
    headers: Optional[dict] = None,
) -> Optional[dict]:
    """Fetches a collection by either its ID or name from the loader service."""
    if collection_id:
        url = f"{settings.loader_base_url}/collection?collection_id={collection_id}"
    elif collection_name:
        url = f"{settings.loader_base_url}/collection?collection_name={collection_name}"
    else:
        return None

    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, headers=headers)
            if response.status_code == 200:
                return response.json().get("collection", response.json())
        except httpx.HTTPError:
            return None
    return None


async def verify_collection_existence(collection_id=None, collection_name=None, headers=None):
    """Verifies if a collection exists by either its ID or name."""
    if collection_name:
        collection = await get_collection(collection_name=collection_name, headers=headers)
    else:
        collection = await get_collection(collection_id=collection_id, headers=headers)

    if not collection:
        raise HTTPException(status_code=404, detail="Collection not found")
    return collection
