""" Prompt Orchestration Client"""

from typing import Any, Dict, List, Optional
from uuid import UUID

import requests


class PromptOrchestrationClient:
    """Client for interacting with the prompt orchestration service."""

    def __init__(self, base_url: str):
        """Initialize the client with the base URL of the prompt orchestration service."""
        self.base_url = base_url.rstrip("/")

    # Prompt operations
    def get_all_prompts(self) -> List[Dict]:
        """Get all available prompts"""
        response = requests.get(f"{self.base_url}/prompts", verify=False)
        response.raise_for_status()
        return response.json().get("data", [])

    def search_prompts(self, search_term: str) -> List[Dict]:
        """Search prompts by text"""
        response = requests.get(f"{self.base_url}/prompts/search", params={"search_term": search_term}, verify=False)
        response.raise_for_status()
        return response.json().get("data", [])

    def get_user_prompts(self, user_id: str) -> List[Dict]:
        """Get all prompts for a specific user"""
        response = requests.get(f"{self.base_url}/prompts/user/{user_id}", verify=False)
        response.raise_for_status()
        return response.json().get("data", [])

    def search_user_prompts(self, user_id: str, search_term: str) -> List[Dict]:
        """Search prompts for a specific user"""
        response = requests.get(f"{self.base_url}/prompts/user/{user_id}/search", params={"search_term": search_term}, verify=False)
        response.raise_for_status()
        return response.json().get("data", [])

    def get_system_prompts(self):
        """Fetch only SYSTEM prompts from the prompt service."""
        response = requests.get(f"{self.base_url}/prompts?prompt_type=SYSTEM")
        response.raise_for_status()
        return response.json().get("data", [])

    # Template operations
    def get_all_templates(self, skip: int = 0, limit: int = 50, use_case: Optional[str] = None, collection_id: Optional[UUID] = None) -> Dict:
        """Get all templates with optional filtering"""
        params: Dict[str, Any] = {"skip": skip, "limit": limit}
        if use_case:
            params["use_case"] = use_case
        if collection_id:
            params["collection_id"] = str(collection_id)

        response = requests.get(f"{self.base_url}/templates", params=params, verify=False)
        response.raise_for_status()
        return response.json()

    def get_template(self, template_id: str) -> Dict:
        """Get a specific template by ID"""
        response = requests.get(f"{self.base_url}/templates/{template_id}", verify=False)
        response.raise_for_status()
        return response.json()

    def create_template(self, template_data: Dict) -> Dict:
        """Create a new template"""
        response = requests.post(f"{self.base_url}/templates", json=template_data, verify=False)
        response.raise_for_status()
        return response.json()

    def update_template(self, template_id: str, template_data: Dict) -> Dict:
        """Update an existing template"""
        response = requests.put(f"{self.base_url}/templates/{template_id}", json=template_data, verify=False)
        response.raise_for_status()
        return response.json()

    def delete_template(self, template_id: str) -> Dict:
        """Delete a template"""
        response = requests.delete(f"{self.base_url}/templates/{template_id}", verify=False)
        response.raise_for_status()
        return response.json()

    def render_template(self, template_id: str, placeholder_values: Dict) -> Dict:
        """Render a template with placeholder values"""
        response = requests.post(f"{self.base_url}/templates/{template_id}/render", json={"placeholder_values": placeholder_values}, verify=False)
        response.raise_for_status()
        return response.json()

    def search_templates(self, search_term: str, skip: int = 0, limit: int = 50, use_case: Optional[str] = None, collection_id: Optional[UUID] = None) -> Dict:
        """Search templates by title, description, or content"""
        params: Dict[str, Any] = {"search_term": search_term, "skip": skip, "limit": limit}
        if use_case:
            params["use_case"] = use_case
        if collection_id:
            params["collection_id"] = str(collection_id)

        response = requests.get(f"{self.base_url}/templates/search", params=params, verify=False)
        response.raise_for_status()
        return response.json()

    def get_user_templates(self, user_email: str, skip: int = 0, limit: int = 50, use_case: Optional[str] = None) -> Dict:
        """Get templates for a specific user"""
        params: Dict[str, Any] = {"skip": skip, "limit": limit}
        if use_case:
            params["use_case"] = use_case

        response = requests.get(f"{self.base_url}/templates/user/{user_email}", params=params, verify=False)
        response.raise_for_status()
        return response.json()

    def get_system_templates(self, skip: int = 0, limit: int = 50) -> Dict:
        """Get system templates"""
        params: Dict[str, Any] = {"skip": skip, "limit": limit}
        response = requests.get(f"{self.base_url}/templates/system", params=params, verify=False)
        response.raise_for_status()
        return response.json()

    def get_unique_users_with_templates(self) -> List[str]:
        """Get list of unique users who have templates"""
        response = requests.get(f"{self.base_url}/templates/users/unique", verify=False)
        response.raise_for_status()
        return response.json()
