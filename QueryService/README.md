# Query Service – **Deep-Dive Guide**

_(v1 · May 2025 – matches the tone & structure of the Loader / Indexer docs)_

> Drop straight into `docs/query_deep_dive.md`.
> **Audience:** backend devs & DevOps; assumes you already skimmed the Loader / Indexer guides.

---

## Table of Contents

1. [Mission & Core Responsibilities]()

2. [High-level Architecture]()

3. [Features at a Glance]()

4. [Query-Answer Pipeline]()

5. [Re-ranking Strategies]()

6. [Chat Sessions, Memory & Redis]()

7. [APIs]()

8. [Data Model]()

9. [Extensibility Hooks]()

10. [Running Locally]()


---

## Mission & Core Responsibilities

The **Query Service** turns raw user questions into helpful, source-grounded answers.
It orchestrates multiple subsystems:

- **Retrieval** – semantic, keyword, MMR or HYDE (hallucination-based) search

- **Re-ranking** – optional second stage using small or large LLMs

- **LLM Generation** – answers with GPT-4o / Claude / Llama 3 etc.

- **Chat Memory** – long-term context per user session held in Redis

- **Web-Augmentation** – DuckDuckGo fallback when internal docs are insufficient

- **Feedback Loop** – thumbs-up / down stored for evaluation & fine-tuning


Everything is exposed through a clean **FastAPI** surface; no CPU-heavy work
resides on the request path – only network I/O to PGVector & OpenAI.

---

## High-level Architecture

```text
                   ┌──────────────┐
    client  ──────►│ FastAPI      │──┐
   (browser/SDK)   │  gateway     │  │REST
                   └────┬─────────┘  │
                        │auth (AAD)  │
                        ▼            │
                ┌──────────────┐     │ SQL (metadata)
                │   Postgres   │◄────┘
                │ (pgvector)   │
                └─▲────────────┘
                  │vectors
    Redis (chat)  │
         ┌────────┴───────┐
         │Session memory  │
         └────────────────┘
             ▲   ▲
             │   │
             │   │web search (DuckDuckGo)
             │   └───────┐
             │           ▼
             │   ┌────────────────┐
             └──►│  Re-Ranker LLM │
                 └────────────────┘
                          │top-k chunks
                          ▼
                  ┌────────────┐
                  │ Chat LLM   │ (GPT-4o , Llama 3, etc.)
                  └────────────┘
```

---

## Features at a Glance

|Category|What it does|
|---|---|
|**Multi-strategy search**|Similarity · MMR · Keyword · Hybrid · HYDE|
|**LLM-based re-rank**|`small-llm` (point-wise) or `self-rerank` (list-wise)|
|**Session memory**|Long-term chat history (TTL configurable up to 1 year)|
|**Web fallback**|DuckDuckGo → LLM decides if external search is needed|
|**Voice input**|`/api/query_with_voice` transcribes WAV/MP3 via Azure Whisper|
|**Folders & Favorites**|Redis-backed foldering system for user sessions|
|**Feedback API**|Like/Dislike + free-form comments; status tracking|
|**Typed models**|Pydantic v2 everywhere; OpenAPI auto-generated|
|**Cloud-native**|CORS, AAD PKCE, health probes, env-driven config|
|**Observability**|Structured log fmt, OpenAI token/cost metering, per-request trace IDs|

---

## Query-Answer Pipeline

|Step|Component|Key Code|Notes|
|---|---|---|---|
|**1**|**FastAPI** handler|`api_post_query*` (many variants – sync / stream / update)|Auth + payload validation|
|**2**|**Config resolver**|`get_custom_config_from_collection()`|Embedding / LLM / method / system-prompt per collection|
|**3**|**Retriever**|`SemanticSearch.retrieve_relevant_docs()`|Picks search_method; returns `first_pass_k` × chunks|
|**4**|**Optional re-rank**|`ReRank.__call__()`|SMALL_LLM ↔ SELF_RERANK ↔ (future) CROSS_ENCODER|
|**5**|**Web decision**|`WebService.is_need_web_search()`|LLM votes **yes/no**; if _yes_ → prepend web docs|
|**6**|**Prompt build**|custom/system prompt + suffix (with/without memory)||
|**7**|**LLM generation**|`SemanticSearch.query()` or `.query_stream()`|Streams tokens back if `/query_stream`|
|**8**|**Persistence**|`insert_query` / `update_query`|Stores question, answer, retrieved chunks, raw OpenAI JSON|
|**9**|**Token metering**|`TokenLimiter.store_token_usage()`|Stores usage (+ cost) keyed by timestamp & thread|

**Streaming flow** returns an SSE-like plain-text stream:

```jsonc
{"content":"Hallo ","query_id":"…","retrieved_docs":[…]}
```

Each chunk arrives as soon as the LLM emits a token.

---

## Re-ranking Strategies

|Strategy enum|Algorithm|LLM model|Cost|When to use|
|---|---|---|---|---|
|`SMALL_LLM` (default)|Point-wise 0-10 score|GPT-4o-mini|💲|General – fast & cheap|
|`SELF_RERANK`|List-wise ordering|GPT-4o (or Claude)|💲💲|Fewer chunks, better global ordering|
|_(future)_ CROSS_ENCODER|bi-encoder similarity|`bge-reranker-base`|🆓|Offline or on-prem only|

Implementation: `query_app/services/rerank.py`
Switch on/off per collection or per request (`re_rank=true`).

## 🔍 Re-Ranking – Why & How

_(Deep-dive supplement to the earlier "Re-ranking Strategies" table)_

### 1. Problem Statement

Pure vector **similarity search** often returns the _closest_ chunks in
embedding-space, but:

- The top-k might all repeat the same sentence (low diversity).

- A noisy chunk with many shared tokens ("GmbH AG Ltd Inc…") can outrank a short but highly relevant paragraph.

- Users want **passage-level** answers, yet similarity is computed at the **chunk** level.

- When the collection grows, we must cut from _hundreds_ of candidates to the 4-8 passages shown beside the answer — picking the wrong 4 makes the LLM hallucinate.


Hence the **second-stage re-ranker**:

```
first-pass search  (k = 32-128)
          │
          ▼
re-ranker scores / orders
          │           (diversity, semantic, global context…)
          ▼
final top-k (4-8) sent to prompt
```

### 2. Two Algorithms shipping today

|Strategy|How it works|Latency (32→4)|$ Token Cost|Strengths|Caveats|
|---|---|---|---|---|---|
|**SMALL_LLM**|1. Build a _one-chunk_ prompt:"_Question … ‖ Chunk …_"2. Cheap chat-model (`gpt-4o-mini`) returns **one number 0-10** per chunk.|400 ms – 1 s|~150 tokens|Robust numeric score, easy tie-breaking|Adds parallel API calls (batched though)|
|**SELF_RERANK**|1. Concatenate _all_ first-pass chunks in one prompt (with indices).2. Larger model (`gpt-4o`) returns a **comma-list of indices** ordered by usefulness.|1.5-2 s|< 500 tokens|List-wise → captures cross-chunk redundancy; only 1 API call|Needs context window big enough for 32 chunks|

_(“CROSS_ENCODER" with sentence-transformers is ready behind a feature-flag; turn it on when GPU inference is available.)_

#### Prompt examples — _generated each call_

**SMALL_LLM**

```
You are an expert relevance grader.

Question: Why does ISO 27001 require a SoA?

Candidate chunk:
[…] Each control shall be listed in the Statement-of-Applicability […]

### Instruction
Return one relevance score on its own line, nothing else.
The score MUST be a number from 0 (irrelevant) to 10 (perfect). Decimals allowed.

### Output
```

**SELF_RERANK** (k = 8 shown)

```
Below are 8 context chunks retrieved for a question.
Rank them from best (0) to worst (7) according to how well they help answer the question.
Return only a comma-separated list of zero-based indices.

Question: Why does ISO 27001 require a SoA?

Contexts:
[0] The Statement of Applicability (SoA) documents …
[1] ISO/IEC 27001:2022 clause 6.1.3 c) mandates …
[…]
Answer:
```

### 3. Scoring & Tie-breakers

After the raw scores/ordering come back:

1. **Primary sort** – negative score (higher first).

2. **Tiebreaker** (configurable): longer chunks win (`len(page_content)`), on the assumption longer explanations carry more evidence.

3. **Stable sort** – original index so results are deterministic across runs.


```python
def _sort_by_score(docs, scores):
    zipped = list(zip(scores, docs, range(len(docs))))
    if TIEBREAKER_USE_LEN:
        zipped = [(s, d, i, len(d.page_content)) for s, d, i in zipped]
        zipped.sort(key=lambda x: (-x[0], -x[3], x[2]))
    else:
        zipped.sort(key=lambda x: (-x[0], x[2]))
    return zipped[:final_k]
```

### 4. Integration in the Retrieval Chain

```mermaid
flowchart LR
    subgraph LangChain
        R1[Retriever (PGVector)] --> |k=64| Rerank
        Rerank --> |top-k 4| Prompt
    end
```

- `SemanticSearch.retrieve_relevant_docs()` decides **first_pass_k** (multiplier × final_k).

- The re-ranker is wrapped in `ReRankRetriever`, so the rest of LangChain
    thinks it's just another retriever.

- For **streaming** answers we still call the re-ranker first, then hand the final list to `query_stream()` so the user doesn't see irrelevant chunks scrolling by.


### 5. Configuration Matrix

|Level|Flag / Field|Example|Effect|
|---|---|---|---|
|**Collection**|`custom_config.re_rank = true``custom_config.re_rank_strategy = "self-rerank"`|Set once per collection – ideal for high-stakes datasets|Enabled for all queries|
|**Single request**|`POST /query … "re_rank": true`|UI toggle|Overrides collection default|
|**Env vars**|`RERANK_SMALL_LLM_MODEL=gpt-4o-mini``RERANK_BIG_LLM_MODEL=gpt-4o`|Change model globally|Only restart worker|

### 6. Performance & Cost benchmarks (4×A100)

|Corpus size|First-pass k|Strategy|P50 Latency|OpenAI Cost|Quality uplift*|
|---|---|---|---|---|---|
|50 k chunks|64|None|420 ms|0|baseline|
|||SMALL_LLM|950 ms|$0.0007|**+12 F1**|
|||SELF_RERANK|1.7 s|$0.0023|**+18 F1**|

* macro-averaged F1 on internal RAG-bench (100 Q/A pairs).

### 7. Extending / Hacking

- **Plug a local cross-encoder:**

    ```python
    from sentence_transformers import CrossEncoder
    self.ce = CrossEncoder("ms-marco-TinyBERT-L-2-v2", device="cuda")
    ```

    then add a branch in `ReRank.__call__`.

- **Custom score fusion:**
    Replace `_sort_by_score` with a weighted blend of
    `model_score * 0.8 + length_norm * 0.2`.

- **GPU batching:**
    The _Small-LLM_ path already batches prompts; increase
    `FIRST_PASS_POOL_MULT` or tweak `openai-python` `batch_size`.


---

### TL;DR

Re-ranking is the **safety net** that:

- Filters out near-duplicates & off-topic snippets ⚖️

- Dramatically boosts exact-answer recall (+10-20 pp in tests) 🎯

- Trades a few hundred milliseconds and pennies per request for a **much better** final answer 💡


Turn it on for customer-facing collections; leave it off for throw-away test sets where speed beats fidelity.

---

## Chat Sessions, Memory & Redis

- **Sessions** (`user_sessions` table)
    _One chat-thread inside one collection._
    Stored columns: `id`, `title`, `custom_config`, `is_favorite`, timestamps.

- **Folders** (`folder:<email>:<collection_id>` in Redis)
    JSON dict _folder → [session_ids]_ for UI grouping.

- **Memory** (`chat_history:<collection>_<search>:{session_id}:*`)
    RedisChatMessageHistory keeps the last _N_ messages (TTL 1 day → 1 year).
    Toggle with `is_memory_on` (collection custom config).


> **Tip:** killing Redis won't break answering – only removes context.

---

## APIs

_(Only the top-level groups – see `/docs` for params & examples)_

|Path prefix|What it does|
|---|---|
|**/collection/{id}/query**|One-shot sync answer (json)|
|**/collection/{id}/query_stream**|SSE-style token stream|
|**/collection/{id}/query_update**|"Rewrite / regenerate" existing query|
|**/collection/{id}/query/retrieved_docs**|Return only top-k chunks (for UI inspection)|
|**/feedback**|CRUD for thumbs/corrections|
|**/sessions**|Foldering, favorites, CRUD|
|**/prompts**|Talks to Prompt-Orchestrator micro-service|
|**/query_with_voice**|Speech-to-text + answer|

Auth: same AAD bearer **or** `X-Admin-UI: true` bypass (for local dev).

---

## Data Model

|Table / store|Purpose|Key columns|
|---|---|---|
|**queries** (PG)|History of questions & answers|`id`, `collection_id`, `session_id`, `content`, `answer`, `is_favorite`|
|**feedbacks** (PG)|Like/Dislike + free text|`id`, `query_id`, `status`, `comment`, `created_at`|
|**user_sessions** (PG)|Chat threads|`id`, `collection_id`, `user_email`, `title`, `custom_config`|
|**Redis keys**|Chat memory & foldering|see previous section|

---

## Extensibility Hooks

- **Custom search** – add a method to `VectorDB._initialize_search_method()`
    and update the enum in `SearchMethod`.

- **New re-ranking model** – implement `ReRank.*` branch, then expose enum.

- **LLM swap** – add to `MODEL_PRESETS` (embedding or chat) – zero code changes.

- **Alternative web engine** – subclass `WebService` (Bing, Brave, etc.)
    and replace DuckDuckGo tools.


Everything else (retry budgets, TTLs, model names) is ENV-driven.

---

## Running Locally

|Step|Command|
|---|---|
|**1 · Env**|`cp .env.example .env && edit` – set OpenAI key + PG creds|
|**2 · Infra**|`docker compose up -d redis postgres_query`|
|**3 · Dependencies**|`pip install -r requirements.txt`|
|**4 · Migrations**|`python -m query_app.database.migrate`|
|**5 · API**|`uvicorn query_app.main:app --reload --port 8003`|

Optional **Whisper** voice transcription requires the `whisper-test` deployment registered in your Azure OpenAI resource.

Full stack with all siblings:

```bash
docker compose up --build loader loader-worker indexer indexer-worker query query-worker admin-ui
```

Ports: Query API **8003**, others as per previous guides.

---

# Query API Reference

Below you'll find a **compact but complete** reference to every endpoint involved in asking questions, getting answers, and managing a user's chat history.
No global *Table of Contents*—just the meat.

---

## 0 · Common Concepts

| Item               | Details                                                                                                                                                                         |
| ------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Base URL**       | `https://<host>/api` (same host used by Loader / Indexer services)                                                                                                              |
| **Auth**           | Azure AD (access-token in the `Authorization` header).<br>Local or admin mode: omit the bearer token and instead send `X-Admin-UI: true`.                                       |
| **Collection ID**  | Always a **UUID** in the path: `/collection/{collection_id}/…`                                                                                                                  |
| **Session ID**     | Another UUID that groups a series of user queries (thread-like).                                                                                                                |
| **Query ID**       | UUID generated by the service for each question asked.                                                                                                                          |
| **Search methods** | `similarity` (default) · `mmr` · `keyword` · `hybrid` (see `search_method` field)                                                                                               |
| **Streaming**      | Endpoints that return a `StreamingResponse` emit **text chunks** separated by newline—but wrapped in one-line JSON objects so you always know which query the chunk belongs to. |

---

## 1 · Ask a Question

### 1.1 POST `/collection/{collection_id}/query`

Single-shot query; entire answer returned synchronously.

```http
POST /collection/07c6…8e57/query
Authorization: Bearer <token>
Content-Type: application/json
```

```jsonc
{
  "content": "Summarise the latest project requirements",
  "custom_prompt": "Answer as an experienced PM",
  "search_method": "mmr",           // optional
  "session_id":   "bae3…5f6c",      // optional – creates one if absent
  "inline_pdf_content": "",         // optional page text pasted from a one-off PDF
  "markdown_support_on": true       // format answer as Markdown
}
```

#### Success (200)

```jsonc
{
  "query_id": "dd78…f33d",
  "session_id": "bae3…5f6c",
  "content": "Here is the summary…",
  "retrieved_docs": {
    "docs":      [ { /* doc,score */ } ],
    "docs_mmr":  [ { /* doc,score */ } ]
  }
}
```

---

### 1.2 POST `/collection/{collection_id}/query_stream`

Same inputs as **1.1** but returns a **server-sent stream**.
Typical use when UX wants to display the assistant typing.

*Content-Type*: `text/plain`
Every line is a JSON object:

```jsonc
{"content":"The first token…","query_id":"dd78…","session_id":"bae3…"}
{"content":" …second token","query_id":"dd78…","session_id":"bae3…"}
...
```

Once the stream closes the backend has automatically stored the full answer in the DB.

---

### 1.3 PUT `/collection/{collection_id}/query_update`

Re-ask an **existing** query but modify the answer strategy:

```jsonc
{
  "query_id":   "dd78…f33d",
  "adjust_type":"more_detail",            // or less_detail / shorter_answer / longer_answer
  "custom_prompt":"Now reply in bullet-points",
  "stream": true                          // opt-in streaming again
}
```

Returns either JSON (non-stream) or a streaming body exactly like **1.2**.

---

### 1.4 POST `/collection/{collection_id}/query/retrieved_docs`

"Peek" at what the retriever would return **without** running the LLM:

```jsonc
{
  "content": "Why did revenue spike in Q3?",
  "search_method": "keyword"       // optional override
}
```

Response 200:

```jsonc
{
  "docs":[
    {
      "page_content":"…",
      "metadata":{"source":"file.pdf#p4","score":0.87}
    }
  ]
}
```

Great for debug/UIs that show citations before the answer.

---

### 1.5 POST `/api/query_with_voice`

Upload an audio file (`audio/wav` or `audio/mpeg`).
Under the hood the endpoint:

1. Saves file to a temp dir
2. Sends it to a **Whisper** deployment on Azure OpenAI
3. Streams back the plain transcription

```bash
curl -H "Authorization: Bearer $TOKEN" \
     -F "audio=@voice_question.mp3" \
     https://…/api/query_with_voice
```

Returns:

```json
{ "transcription": "What are our OKRs for next quarter?" }
```

Use the transcription as `content` in an ordinary query call.

---

## 2 · Read / Manage Past Queries

| Verb & Path                                           | Purpose                                                 |
| ----------------------------------------------------- | ------------------------------------------------------- |
| **GET** `/collection/{id}/queries`                    | List every query in the collection (current user only). |
| **PUT** `/{query_id}/toggle_favorite/{collection_id}` | Flip the *favorite* flag; returns the new state.        |
| **DELETE** `/query/{query_id}`                        | Permanently wipe a single query record.                 |
| **DELETE** `/collection/{id}/queries`                 | Bulk-delete **all** queries for that collection.        |
| **GET** `/get_relevant_docs_by_query_id/{query_id}`   | Fetch the stored retrieval set for an older answer.     |

---

## 3 · Sessions & Folders in a Nutshell

Sessions are first-class: a user can create, rename, favorite, move them into arbitrary **folders** (folder data lives in Redis, not Postgres).

*Only highlights are listed; see code for full CRUD endpoints.*

| Action                                | Endpoint (method)                                                  |
| ------------------------------------- | ------------------------------------------------------------------ |
| Create session                        | `/create_session/{collection_id}` POST                             |
| Get sessions grouped by folder        | `/get_sessions_by_collection_id/{cid}` GET                         |
| Toggle "favorite"                     | `/update_favorite_status/{session_id}` PUT                         |
| Folder CRUD                           | `/create_folder … /rename_folder … /delete_folder …`               |
| Move / remove session within a folder | `/move_session_to_folder` POST / `remove_session_from_folder` POST |

The service automatically cleans up **chat history** in Redis when you delete a session (`DELETE /delete_session/{id}`).

---

## 4 · Behind-the-Scenes (How Queries Are Answered)

1. **Vector Retrieval** — top-k documents pulled from PGVector using the requested `search_method`.
2. **Optional Re-ranking** — if collection config enables it, chunks go through a *second-stage LLM* (cheap "mini-GPT-4o") that rescoring each chunk 0-10.
3. **LLM Call** — prompt = *system prompt* + user question + retrieved context ( + chat history if memory ON ).
4. **Streaming** — if the caller chose streaming, tokens are forwarded immediately.
5. **Metrics** — total tokens & cost logged (`TokenLimiter`) and answer indexed back into Postgres.

---

### Error Codes Cheatsheet

| HTTP Code | Likely Cause                                                                      |
| --------- | --------------------------------------------------------------------------------- |
| **400**   | Malformed field (e.g. `search_method=foobar`) or wrong MIME (`query_with_voice`). |
| **401**   | Missing/expired Azure AD token.                                                   |
| **403**   | You tried to access or mutate a session/query that isn't yours.                   |
| **404**   | Wrong `collection_id`, `query_id`, or `session_id`.                               |
| **409**   | Folder name clash when creating/renaming.                                         |
| **500**   | Anything uncaught—see server logs; response body will echo exception text.        |

---

### Quick Curl Example (Streaming)

```bash
curl -N -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"content":"List the top 3 benefits","stream":true}' \
     https://<host>/api/collection/07c6…8e57/query_stream
```

You'll see a live trickle of JSON lines until completion.

---

## Slash Commands

The system includes a powerful slash command system that allows for quick actions and commands through the chat interface. This document explains how to use, add, remove, and update slash commands.

### Using Slash Commands

1. Type `/` in the chat input field
2. A list of available commands will appear
3. Use arrow keys or mouse to select a command
4. Press Enter or click to execute the command

### Available Commands

- `/help` - Show available commands and their descriptions
- `/clear` - Clear the current chat history
- `/websearch <query>` - Search the web for information
- `/save <filename>` - Save the current chat to a file

### API Endpoints

All slash command endpoints require Entra ID authentication using `Authorization: Bearer <token>` header.

#### Execute a Command

**POST** `/slash-commands/execute`

```bash
curl -X POST "http://localhost:8003/slash-commands/execute" \
  -H "Authorization: Bearer <your-entra-id-token>" \
  -H "Content-Type: application/json" \
  -d '{"command": "/help"}'
```

Response:
```json
{
  "success": true,
  "command": "help",
  "result": {
    "message": "Available commands:\n/help: Show available commands..."
  }
}
```

#### List Available Commands

**GET** `/slash-commands/list`

```bash
curl -X GET "http://localhost:8003/slash-commands/list" \
  -H "Authorization: Bearer <your-entra-id-token>"
```

Response:
```json
{
  "commands": [
    {
      "name": "help",
      "description": "Show available commands and their descriptions",
      "requires_args": false,
      "arg_description": null
    },
    {
      "name": "websearch",
      "description": "Search the web for information",
      "requires_args": true,
      "arg_description": "Usage: /websearch <query>"
    }
  ]
}
```

#### Register a New Command

**POST** `/slash-commands/register`

```bash
curl -X POST "http://localhost:8003/slash-commands/register" \
  -H "Authorization: Bearer <your-entra-id-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "echo",
    "description": "Echo back the input text",
    "requires_args": true,
    "arg_description": "Usage: /echo <text>"
  }'
```

Response:
```json
{
  "message": "Command /echo registered successfully"
}
```

#### Remove a Command

**DELETE** `/slash-commands/{command_name}`

```bash
curl -X DELETE "http://localhost:8003/slash-commands/echo" \
  -H "Authorization: Bearer <your-entra-id-token>"
```

Response:
```json
{
  "message": "Command /echo removed successfully"
}
```

### Managing Commands

#### Adding New Commands (Backend)

Add to the `_register_default_commands` method in `QueryService/query_app/slash_commands.py`:

```python
self.register_command(SlashCommand(
    name="command_name",
    handler=your_handler_function,
    description="Description of what the command does",
    requires_args=False,  # Set to True if command needs arguments
    arg_description="Usage: /command_name <arg>"  # Optional
))
```

#### Authentication

All slash command endpoints require Entra ID authentication:
- Obtain an access token from your Entra ID tenant
- Include the token in the Authorization header: `Bearer <token>`
- The system uses `auth=Depends(get_auth)` for all endpoints

### Data Models

The system uses properly structured Pydantic models:

**Request Models** (`query_app/api/models/request.py`):
- `SlashCommandRequest` - Command execution
- `SlashCommandRegistrationRequest` - Command registration

**Response Models** (`query_app/api/models/response.py`):
- `SlashCommandResponse` - Execution results
- `SlashCommandListResponse` - Available commands
- `SlashCommandInfo` - Individual command details
- `MessageResponse` - Simple confirmations

### Error Handling

The system handles various error cases:
- **400 Bad Request**: Invalid command names, missing required arguments
- **401 Unauthorized**: Missing or invalid authentication
- **404 Not Found**: Command doesn't exist (for removal)
- **500 Internal Server Error**: Command execution failures

### Command Structure

Each command consists of:
- `name`: The command identifier (used after /)
- `handler`: Function that executes the command
- `description`: What the command does
- `requires_args`: Whether the command needs arguments
- `arg_description`: Usage instructions for commands with arguments

### Best Practices

1. **Command Naming**:
   - Use lowercase letters
   - Use descriptive names
   - Keep names short but clear (1-50 characters)

2. **Descriptions**:
   - Be clear and concise (1-200 characters)
   - Include usage examples
   - Explain what the command does

3. **Arguments**:
   - Make argument requirements clear
   - Provide usage examples in `arg_description`
   - Validate arguments properly

4. **Authentication**:
   - Always include proper Entra ID tokens
   - Handle 401 errors gracefully
   - Use proper authorization headers

5. **Error Handling**:
   - Provide clear error messages
   - Handle edge cases
   - Log errors appropriately
