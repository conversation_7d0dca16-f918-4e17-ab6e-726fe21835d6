<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <link rel="stylesheet" href="/static/css/globals.css"/>
    <link rel="stylesheet" href="/static/css/styleguide-fbeta.css"/>
    <link rel="stylesheet" href="/static/css/style.css"/>
    <title>_fbeta AI Interface</title>
</head>
<body>
<div class="COLLECTIONS" id="COLLECTIONS">
    <div class="cone">
        <img class="img" src="https://c.animaapp.com/7jiuBGHy/img/cone-01-2.png" loading="lazy" alt="decorative cone"/>
    </div>
    <div class="cone-wrapper">
        <img class="cone-2" src="https://c.animaapp.com/7jiuBGHy/img/cone-01-2-1.png" loading="lazy" alt="decorative background element"/>
    </div>
    <a href="https://fbeta.de/kontakt/" class="setting-item" target="_blank">
        <div class="setting-item">
            <img class="help-circle" src="https://c.animaapp.com/7jiuBGHy/img/help-circle.svg"/>
            <div class="help-documentation">Hilfe &amp; Dokumentation</div>
        </div>
    </a>
    <div class="collection-items"></div>
</div>

<!-- Wrap left-sidebar, center chat, and right sidebar in ONE container -->
<div class="layout-container" id="CHAT">

    <!-- LEFT SIDEBAR -->
    <div class="sidebar">
        <div class="sidebar-content">
            <div class="left-side-bar-burger-icon-wrapper">
                <svg class="left-side-bar-burger-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 16 12">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M0 0.75C0 0.551088 0.0790175 0.360322 0.21967 0.21967C0.360322 0.0790175 0.551088 0 0.75 0H15.25C15.4489 0 15.6397 0.0790175 15.7803 0.21967C15.921 0.360322 16 0.551088 16 0.75C16 0.948912 15.921 1.13968 15.7803 1.28033C15.6397 1.42098 15.4489 1.5 15.25 1.5H0.75C0.551088 1.5 0.360322 1.42098 0.21967 1.28033C0.0790175 1.13968 0 0.948912 0 0.75ZM0 6C0 5.80109 0.0790175 5.61032 0.21967 5.46967C0.360322 5.32902 0.551088 5.25 0.75 5.25H15.25C15.4489 5.25 15.6397 5.32902 15.7803 5.46967C15.921 5.61032 16 5.80109 16 6C16 6.19891 15.921 6.38968 15.7803 6.53033C15.6397 6.67098 15.4489 6.75 15.25 6.75H0.75C0.551088 6.75 0.360322 6.67098 0.21967 6.53033C0.0790175 6.38968 0 6.19891 0 6ZM0 11.25C0 11.0511 0.0790175 10.8603 0.21967 10.7197C0.360322 10.579 0.551088 10.5 0.75 10.5H15.25C15.4489 10.5 15.6397 10.579 15.7803 10.7197C15.921 10.8603 16 11.0511 16 11.25C16 11.4489 15.921 11.6397 15.7803 11.7803C15.6397 11.921 15.4489 12 15.25 12H0.75C0.551088 12 0.360322 11.921 0.21967 11.7803C0.0790175 11.6397 0 11.4489 0 11.25Z"
                          fill="white"/>
                </svg>

            </div>

            <div class="add-new-session-icon-wrapper" id="addNewSessionButton">
                <svg class="add-new-session-icon" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor">
                    <g id="add">
                        <path id="icon" d="M9.1665 10.8334H4.1665V9.16669H9.1665V4.16669H10.8332V9.16669H15.8332V10.8334H10.8332V15.8334H9.1665V10.8334Z"/>
                    </g>
                </svg>
                <div class="button-text">Neuer Chat</div>
            </div>
            <div class="sessions-favorite">
            </div>
            <div class="session-scroll">
                <div class="sessions-recent" id="sessions-recent">
                </div>
            </div>
        </div>
        <div class="prompt-actions">
            <button id="openPromptServiceModal" style="margin-top:auto;width:100%;">Prompt-Service</button>
        </div>


    </div><!-- END .sidebar -->

    <!-- CENTER CHAT -->
    <div class="chat-interface">
        <header class="header">
            <div class="frame-2">
                <div class="logo-section">
                    <img id="fbeta-logo-chat" src="/static/img/fbeta-logo.png" alt="fbeta-logo" class="logo" style="width: 125px;height: 60px;margin-top: 40px;">
                </div>
                <div class="user-controls"></div>
            </div>
            <div class="secondary-button-wrapper">
                <div class="logout-button-wrapper">
                    <span id="userName" class="user-name"></span>
                    <div class="verticalline"></div>
                    <button class="logout-button" id="logout-button">Abmelden</button>
                </div>
            </div>
        </header>

        <div class="chat-window" id="chat-window">
            <!-- Chat messages go here -->
        </div>

        <!-- Input pinned at top with conversation-starters -->
        <div class="chat-input-part">
            <div class="conversation-starters" id="conversation-starters">
            </div>
            <div class="input-field" id="input-field">
                <div class="frame-5">
                    <div class="button-text-wrapper" id="collection-name-selector" title="Sammlungen">
                        <div class="collection-name-in-chat" id="collection-name-in-chat"></div>
                    </div>
                    <svg id="uploadIconButton" width="24" height="24" viewBox="0 0 19 18" fill="none"
                         xmlns="http://www.w3.org/2000/svg" onclick="triggerFileSelection()"
                         style="cursor: pointer;">
                        <g id="paperclip-01">
                            <path id="paperclip"
                                  d="M13.6329 6.0998L8.30803 11.4247C7.67485 12.0579 6.64824 12.0579 6.01506 11.4247C5.38054 10.7902 5.38207 9.76096 6.01846 9.12832L10.1478 5.0234L11.1848 3.9864C12.4465 2.72464 14.4922 2.72464 15.754 3.9864C17.0157 5.24815 17.0157 7.29386 15.754 8.55561L14.7326 9.57702L10.8614 13.4482C8.85899 15.534 5.82477 15.8514 3.70332 13.8148C1.60784 11.8032 1.96302 8.78723 4.08176 6.66848L7.93765 2.81201"
                                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </g>
                    </svg>
                    <input type="file" id="pdfInput" accept=".pdf,.docx,.xlsx,.csv" multiple style="display: none;" onchange="uploadPDF()"/>
                    <textarea id="questionInput" class="input-section" placeholder="Frage eingeben..."></textarea>
                    <div id="slashCommandSuggestions" class="slash-command-suggestions"></div>
                </div>
                <div class="send-icon-wrapper">
                    <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-left;-2px">
                        <g id="SendIcon">
                            <path id="Icon" d="M15.1668 1.83325L7.8335 9.16658M15.1668 1.83325L10.5002 15.1666L7.8335 9.16658M15.1668 1.83325L1.8335 6.49992L7.8335 9.16658"
                                  stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
                        </g>
                    </svg>
                </div>

            </div>
        </div>


        <div id="messageModal" style="display: none;" class="modal">
            <div class="modal-content">
                <p id="modalMessage" class="modal-message"></p>
                <br>
                <div class="modal-actions">
                    <button class="modal-button modalAcceptButton" onclick="closeMessageModal()">Okay</button>
                </div>
            </div>
        </div>

        <div id="YesNoModal" style="display: none;" class="modal">
            <div class="modal-content">
                <h3 id="YesNoModalTitle" class="modal-title"></h3>
                <br>
                <br>
                <p id="YesNoModalMessage" class="modal-message"></p>
                <br>
                <div class="modal-actions">
                    <button class="modal-button modalCancelButton" onclick="closeYesNoModal()">Nein</button>
                    <button class="modal-button modalAcceptButton" onclick="confirmYesNoModal()">Ja</button>
                </div>
            </div>
        </div>

        <div id="RenameSessionModal" class="modal" style="display: none;">
            <div class="modal-content">
                <h3 class="modal-title">Session umbenennen</h3>
                <br>
                <br>
                <input type="text" id="RenameSessionModalInput" placeholder="Neuen Namen eingeben" class="modal-input"/>
                <br>
                <br>
                <br>
                <div class="modal-actions">
                    <button id="RenameSessionModalRenameButton" class="modal-button modalAcceptButton">Umbenennen</button>
                    <button id="RenameSessionModalCancelButton" class="modal-button modalCancelButton">Abbrechen</button>
                </div>
            </div>
        </div>

        <div id="CreateFolderModal" class="modal" style="display: none;">
            <div class="modal-content">
                <br>
                <h3 class="modal-title">Neuen Ordner erstellen</h3>
                <br>
                <input type="text" id="CreateFolderModalInput" placeholder="Ordnernamen eingeben" class="modal-input"/>
                <br>
                <br>
                <div class="modal-actions">
                    <button id="CreateFolderModalCreateButton" class="modal-button modalAcceptButton">Erstellen</button>
                    <button onclick="closeCreateFolderModal()" class="modal-button modalCancelButton">Abbrechen</button>
                </div>
            </div>
        </div>
        <div id="promptServiceModal" class="prompt-modal" style="display:none;">
            <div class="prompt-modal-content">
                <span id="closePromptServiceModal" class="close-button" style="float:right;cursor:pointer;font-size:24px;">&times;</span>
                <h2>Prompt‑Service</h2>

                <!-- Tabs -->
                <div style="margin-bottom:1rem;display:flex;gap:.5rem;border-bottom:1px solid #ccc;">
                    <button id="promptsTab" class="tab-button active" onclick="switchTab('prompts')">Prompts</button>
                    <button id="templatesTab" class="tab-button" onclick="switchTab('templates')">Templates</button>
                </div>

                <!-- Prompts Tab Content -->
                <div id="promptsContent" class="tab-content active">
                    <!-- search / filter bar -->
                    <div style="margin-bottom:1rem;display:flex;gap:.5rem;">
                        <input id="promptSearchInput" type="text" placeholder="Suchbegriff eingeben …" style="flex:1;padding:.45rem;"/>
                        <button id="searchPromptsButton" class="button-primary">Suchen</button>
                    </div>

                    <!-- result table -->
                    <div style="overflow:auto;max-height:60vh;">
                        <table id="promptTable" style="width:100%;border-collapse:collapse;">
                            <thead>
                            <tr>
                                <th style="text-align:left;">Title</th>
                                <th style="text-align:left;">Content</th>
                                <th style="text-align:left;">Action</th>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>

                    <!-- currently selected prompts – each on its own line -->
                    <h3 style="margin-top:1.5rem;">Gewählte Prompts</h3>
                    <pre id="selectedPromptsContainer" class="selected-prompts"
                         style="background:#f7f7f7;padding:.75rem;border-radius:6px;white-space:pre-wrap;max-height:25vh;overflow:auto;"></pre>
                </div>

                <!-- Templates Tab Content -->
                <div id="templatesContent" class="tab-content">
                    <!-- search / filter bar -->
                    <div style="margin-bottom:1rem;display:flex;gap:.5rem;">
                        <input id="templateSearchInput" type="text" placeholder="Search templates..." style="flex:1;padding:.45rem;"/>
                        <select id="templateUseCaseSelect" style="padding:.45rem;">
                            <option value="">All Use Cases</option>
                            <option value="SYSTEM">System</option>
                            <option value="PERSONAL">Personal</option>
                            <option value="EMAIL">Email</option>
                            <option value="REPORT">Report</option>
                            <option value="ANALYSIS">Analysis</option>
                            <option value="OTHER">Other</option>
                        </select>
                        <button id="searchTemplatesButton" class="button-primary">Search</button>
                    </div>

                    <!-- result table -->
                    <div style="overflow:auto;max-height:60vh;">
                        <table id="templateTable" style="width:100%;border-collapse:collapse;">
                            <thead>
                            <tr>
                                <th style="text-align:left;">Title</th>
                                <th style="text-align:left;">Content</th>
                                <th style="text-align:left;">Placeholders</th>
                                <th style="text-align:left;">Action</th>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>

                    <!-- currently selected templates -->
                    <h3 style="margin-top:1.5rem;">Selected Templates</h3>
                    <pre id="selectedTemplatesContainer" class="selected-prompts"
                         style="background:#f7f7f7;padding:.75rem;border-radius:6px;white-space:pre-wrap;max-height:25vh;overflow:auto;"></pre>
                </div>
            </div>
        </div>

        <!-- Template Render Modal -->
        <div id="templateRenderModal" class="prompt-modal" style="display:none;">
            <div class="prompt-modal-content">
                <span class="close-button" style="float:right;cursor:pointer;font-size:24px;" onclick="document.getElementById('templateRenderModal').style.display='none'">&times;</span>
                <h2>Render Template</h2>
                <div class="template-render-content">
                    <!-- Dynamic content will be inserted here -->
                </div>
            </div>
        </div>

        <!-- Session Loading Modal -->
        <div id="SessionLoadingModal" class="modal" style="display: none;">
            <div class="modal-content">
                <!-- Spinner -->
                <div class="modal-spinner"></div>

                <h3 class="modal-title">Session wird geladen...</h3>
                <br>
                <p class="modal-message">
                    Sehr gut! Wir erstellen eine neue Sitzung für dich mit diesem neuen Problem! <br>
                    Sobald unsere Sprachmodell-Antwort kommt, wird deine Sitzung erstellt.
                </p>

                <br>

                <div class="modal-actions">
                    <button onclick="closeSessionLoadingModal()" class="modal-button modalCancelButton">Abbrechen</button>
                </div>
            </div>
        </div>

        <div id="logoutModal" class="logout-modal" style="display: none;">
            <div class="div">
                <div class="logout-icon-wrapper">
                    <img src="/static/img/right-sidebar/Logout.svg" alt="Abmelde-Symbol">
                </div>
                <div class="logout-content">
                    <br>
                    <div class="heading-wrapper">
                        <span class="heading">Abmelden</span>
                    </div>
                    <br>
                    <div class="logout-text-wrapper">
                        <p class="logout-text">Sind Sie sicher, dass Sie sich abmelden möchten?</p>
                    </div>
                </div>
                <br>
                <br>
                <div class="logout-actions">
                    <button class="logout-cancel-wrapper" onclick="closeLogoutModal()">
                        <span class="logout-cancel">Abbrechen</span>
                    </button>
                    <button class="logout-yes-wrapper" onclick="confirmLogout()">
                        <span class="logout-yes">Ja, abmelden</span>
                    </button>
                </div>
            </div>
        </div>

        <div id="UploadedFilesModal" class="modal" style="display: none;">
            <div class="modal-content">
                <h3 class="modal-title">Hochgeladene PDFs</h3>
                <br>
                <div id="uploaded-files-list" class="modal-list"></div>
                <br>
                <div class="modal-actions">
                    <button onclick="closeUploadedFilesModal()" class="modal-button modalCancelButton">Schließen</button>
                </div>
            </div>
        </div>


    </div>

    <!-- RIGHT SIDEBAR (Relevant Documents) -->
    <div class="relevant-documents" id="relevant-documents">
        <div class="element-icons-close-wrapper">
            <button class="closeRelevantDocumentsButton" onclick="closeRelevantDocumentsSideBar()"><img class="img-2" src="/static/img/right-sidebar/close-icon.svg"/></button>
        </div>
        <div class="relevant-docs-wrapper" id="relevant-docs-wrapper">
        </div>
    </div><!-- END .relevant-documents -->

</div><!-- END .layout-container -->


<div id="collectionDropdown">
</div>

<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/dompurify@2.4.0/dist/purify.min.js"></script>

<script src="/static/js/main-fbeta.js"></script>
</body>
</html>
