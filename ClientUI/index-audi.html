<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <link rel="stylesheet" href="/static/css/globals.css"/>
    <link rel="stylesheet" href="/static/css/styleguide-audi.css"/>
    <link rel="stylesheet" href="/static/css/style.css"/>
    <title>_fbeta AI Interface</title>
</head>
<body>
<div class="COLLECTIONS" id="COLLECTIONS">
    <div class="cone">
        <img class="img" src="https://c.animaapp.com/7jiuBGHy/img/cone-01-2.png" loading="lazy" alt="decorative cone"/>
    </div>
    <div class="cone-wrapper">
        <img class="cone-2" src="https://c.animaapp.com/7jiuBGHy/img/cone-01-2-1.png" loading="lazy" alt="decorative background element"/>
    </div>
    <a href="https://fbeta.de/kontakt/" class="setting-item" target="_blank">
        <div class="setting-item">
            <img class="help-circle" src="https://c.animaapp.com/7jiuBGHy/img/help-circle.svg"/>
            <div class="help-documentation">Hilfe &amp; Dokumentation</div>
        </div>
    </a>
    <div class="collection-items"></div>
</div>

<!-- Wrap left-sidebar, center chat, and right sidebar in ONE container -->
<div class="layout-container" id="CHAT">

    <!-- LEFT SIDEBAR -->
    <div class="sidebar">
        <div class="left-side-bar-burger-icon-wrapper">
            <svg class="left-side-bar-burger-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="12" viewBox="0 0 16 12">
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M0 0.75C0 0.551088 0.0790175 0.360322 0.21967 0.21967C0.360322 0.0790175 0.551088 0 0.75 0H15.25C15.4489 0 15.6397 0.0790175 15.7803 0.21967C15.921 0.360322 16 0.551088 16 0.75C16 0.948912 15.921 1.13968 15.7803 1.28033C15.6397 1.42098 15.4489 1.5 15.25 1.5H0.75C0.551088 1.5 0.360322 1.42098 0.21967 1.28033C0.0790175 1.13968 0 0.948912 0 0.75ZM0 6C0 5.80109 0.0790175 5.61032 0.21967 5.46967C0.360322 5.32902 0.551088 5.25 0.75 5.25H15.25C15.4489 5.25 15.6397 5.32902 15.7803 5.46967C15.921 5.61032 16 5.80109 16 6C16 6.19891 15.921 6.38968 15.7803 6.53033C15.6397 6.67098 15.4489 6.75 15.25 6.75H0.75C0.551088 6.75 0.360322 6.67098 0.21967 6.53033C0.0790175 6.38968 0 6.19891 0 6ZM0 11.25C0 11.0511 0.0790175 10.8603 0.21967 10.7197C0.360322 10.579 0.551088 10.5 0.75 10.5H15.25C15.4489 10.5 15.6397 10.579 15.7803 10.7197C15.921 10.8603 16 11.0511 16 11.25C16 11.4489 15.921 11.6397 15.7803 11.7803C15.6397 11.921 15.4489 12 15.25 12H0.75C0.551088 12 0.360322 11.921 0.21967 11.7803C0.0790175 11.6397 0 11.4489 0 11.25Z"
                      fill="white"/>
            </svg>

        </div>

        <div class="add-new-session-icon-wrapper" id="addNewSessionButton">
            <svg class="add-new-session-icon" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor">
                <g id="add">
                    <path id="icon" d="M9.1665 10.8334H4.1665V9.16669H9.1665V4.16669H10.8332V9.16669H15.8332V10.8334H10.8332V15.8334H9.1665V10.8334Z"/>
                </g>
            </svg>
            <div class="button-text">Neuer Chat</div>
        </div>
        <div class="sessions-favorite">
        </div>
        <div class="sessions-recent" id="sessions-recent">
        </div>
    </div><!-- END .sidebar -->

    <!-- CENTER CHAT -->
    <div class="chat-interface">
        <header class="header">
            <div class="frame-2">
                <div class="logo-section">
                    <img id="dak-logo-chat" src="/static/img/audi-bkk-logo.png" alt="Dak logo" class="logo" style="width: 450px;height: 60px;margin-top: 40px;">
                </div>
                <div class="user-controls"></div>
            </div>
            <div class="secondary-button-wrapper">
                <div class="logout-button-wrapper">
                    <span id="userName" class="user-name"></span>
                    <div class="verticalline"></div>
                    <button class="logout-button" id="logout-button">Abmelden</button>
                </div>
            </div>
        </header>

        <div class="chat-window" id="chat-window">
            <!-- Chat messages go here -->
        </div>

        <!-- Input pinned at top with conversation-starters -->
        <div class="chat-input-part">
            <div class="conversation-starters" id="conversation-starters">
            </div>
            <div class="input-field" id="input-field">
                <div class="frame-5">
                    <div class="button-text-wrapper" id="collection-name-selector" title="Sammlungen">
                        <div class="collection-name-in-chat" id="collection-name-in-chat"></div>
                    </div>
                    <svg id="uploadIconButton" width="24" height="24" viewBox="0 0 19 18" fill="none"
                         xmlns="http://www.w3.org/2000/svg" onclick="triggerFileSelection()"
                         style="cursor: pointer;">
                        <g id="paperclip-01">
                            <path id="paperclip"
                                  d="M13.6329 6.0998L8.30803 11.4247C7.67485 12.0579 6.64824 12.0579 6.01506 11.4247C5.38054 10.7902 5.38207 9.76096 6.01846 9.12832L10.1478 5.0234L11.1848 3.9864C12.4465 2.72464 14.4922 2.72464 15.754 3.9864C17.0157 5.24815 17.0157 7.29386 15.754 8.55561L14.7326 9.57702L10.8614 13.4482C8.85899 15.534 5.82477 15.8514 3.70332 13.8148C1.60784 11.8032 1.96302 8.78723 4.08176 6.66848L7.93765 2.81201"
                                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </g>
                    </svg>
                    <input type="file" id="pdfInput" accept=".pdf,.docx,.xlsx,.csv" multiple style="display: none;" onchange="uploadPDF()"/>
                    <textarea id="questionInput" class="input-section" placeholder="Frage eingeben..."></textarea>

                </div>
                <div class="send-icon-wrapper">
                    <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-left;-2px">
                        <g id="SendIcon">
                            <path id="Icon" d="M15.1668 1.83325L7.8335 9.16658M15.1668 1.83325L10.5002 15.1666L7.8335 9.16658M15.1668 1.83325L1.8335 6.49992L7.8335 9.16658"
                                  stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
                        </g>
                    </svg>
                </div>

            </div>
        </div>


        <div id="messageModal" style="display: none;" class="modal">
            <div class="modal-content">
                <p id="modalMessage" class="modal-message"></p>
                <br>
                <div class="modal-actions">
                    <button class="modal-button modalAcceptButton" onclick="closeMessageModal()">Okay</button>
                </div>
            </div>
        </div>

        <div id="YesNoModal" style="display: none;" class="modal">
            <div class="modal-content">
                <h3 id="YesNoModalTitle" class="modal-title"></h3>
                <br>
                <br>
                <p id="YesNoModalMessage" class="modal-message"></p>
                <br>
                <div class="modal-actions">
                    <button class="modal-button modalCancelButton" onclick="closeYesNoModal()">Nein</button>
                    <button class="modal-button modalAcceptButton" onclick="confirmYesNoModal()">Ja</button>
                </div>
            </div>
        </div>

        <div id="RenameSessionModal" class="modal" style="display: none;">
            <div class="modal-content">
                <h3 class="modal-title">Session umbenennen</h3>
                <br>
                <br>
                <input type="text" id="RenameSessionModalInput" placeholder="Neuen Namen eingeben" class="modal-input"/>
                <br>
                <br>
                <br>
                <div class="modal-actions">
                    <button id="RenameSessionModalRenameButton" class="modal-button modalAcceptButton">Umbenennen</button>
                    <button id="RenameSessionModalCancelButton" class="modal-button modalCancelButton">Abbrechen</button>
                </div>
            </div>
        </div>

        <div id="CreateFolderModal" class="modal" style="display: none;">
            <div class="modal-content">
                <br>
                <h3 class="modal-title">Neuen Ordner erstellen</h3>
                <br>
                <input type="text" id="CreateFolderModalInput" placeholder="Ordnernamen eingeben" class="modal-input"/>
                <br>
                <br>
                <div class="modal-actions">
                    <button id="CreateFolderModalCreateButton" class="modal-button modalAcceptButton">Erstellen</button>
                    <button onclick="closeCreateFolderModal()" class="modal-button modalCancelButton">Abbrechen</button>
                </div>
            </div>
        </div>

        <div id="chatGoalModal" class="modal" style="display: none;">
            <div class="modal-content">
                <button class="close-button" onclick="closeChatGoalModal()">✖</button>
                <br>
                <h3 class="modal-title">Zielsetzung für Gespräch festlegen</h3>
                <div class="chat-goal-form">
                    <p>
                        <label for="lieferant">Lieferant:</label>
                        <input type="text" id="lieferant" placeholder="z.B. Firma XYZ" class="modal-input">
                    </p>
                    <br>
                    <p>
                        <label for="verhandlungsziel">Verhandlungsziel:</label>
                        <input type="text" id="verhandlungsziel" placeholder="z.B. Preisreduktion um 10%" class="modal-input">
                    </p>
                    <br>
                    <div class="modal-actions">
                        <button onclick="saveChatGoal()" class="modal-button modalAcceptButton">Bestätigen</button>
                        <button onclick="closeChatGoalModal()" class="modal-button modalCancelButton">Abbrechen</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Session Loading Modal -->
        <div id="SessionLoadingModal" class="modal" style="display: none;">
            <div class="modal-content">
                <!-- Spinner -->
                <div class="modal-spinner"></div>

                <h3 class="modal-title">Session wird geladen...</h3>
                <br>
                <p class="modal-message">
                    Sehr gut! Wir erstellen eine neue Sitzung für dich mit diesem neuen Problem! <br>
                    Sobald unsere Sprachmodell-Antwort kommt, wird deine Sitzung erstellt.
                </p>

                <br>

                <div class="modal-actions">
                    <button onclick="closeSessionLoadingModal()" class="modal-button modalCancelButton">Abbrechen</button>
                </div>
            </div>
        </div>

        <div id="logoutModal" class="logout-modal" style="display: none;">
            <div class="div">
                <div class="logout-icon-wrapper">
                    <img src="/static/img/right-sidebar/Logout.svg" alt="Abmelde-Symbol">
                </div>
                <div class="logout-content">
                    <br>
                    <div class="heading-wrapper">
                        <span class="heading">Abmelden</span>
                    </div>
                    <br>
                    <div class="logout-text-wrapper">
                        <p class="logout-text">Sind Sie sicher, dass Sie sich abmelden möchten?</p>
                    </div>
                </div>
                <br>
                <br>
                <div class="logout-actions">
                    <button class="logout-cancel-wrapper" onclick="closeLogoutModal()">
                        <span class="logout-cancel">Abbrechen</span>
                    </button>
                    <button class="logout-yes-wrapper" onclick="confirmLogout()">
                        <span class="logout-yes">Ja, abmelden</span>
                    </button>
                </div>
            </div>
        </div>


    </div>

    <!-- RIGHT SIDEBAR (Relevant Documents) -->
    <div class="relevant-documents" id="relevant-documents">
        <div class="element-icons-close-wrapper">
            <button onclick="closeRelevantDocumentsSideBar()" style="background-color: black;"><img class="img-2" src="/static/img/right-sidebar/close-icon.svg"/></button>
        </div>
        <div class="relevant-docs-wrapper" id="relevant-docs-wrapper">
        </div>
    </div><!-- END .relevant-documents -->

</div><!-- END .layout-container -->


<!-- Various modals remain the same -->


<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="/static/js/main.js"></script>
</body>
</html>
