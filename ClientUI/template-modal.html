<!-- Template Modal -->
<div id="templateModal" class="prompt-modal" style="display:none;">
    <div class="prompt-modal-content">
        <span id="closeTemplateModal" class="close-button" style="float:right;cursor:pointer;font-size:24px;">&times;</span>
        <h2>Template Service</h2>

        <!-- search / filter bar -->
        <div style="margin-bottom:1rem;display:flex;gap:.5rem;">
            <input id="templateSearchInput" type="text" placeholder="Search templates..." style="flex:1;padding:.45rem;"/>
            <select id="templateUseCaseSelect" style="padding:.45rem;">
                <option value="">All Use Cases</option>
                <option value="SYSTEM">System</option>
                <option value="PERSONAL">Personal</option>
                <option value="EMAIL">Email</option>
                <option value="REPORT">Report</option>
                <option value="ANALYSIS">Analysis</option>
                <option value="OTHER">Other</option>
            </select>
            <button id="searchTemplatesButton" class="button-primary">Search</button>
        </div>

        <!-- result table -->
        <div style="overflow:auto;max-height:60vh;">
            <table id="templateTable" style="width:100%;border-collapse:collapse;">
                <thead>
                <tr>
                    <th style="text-align:left;">Title</th>
                    <th style="text-align:left;">Content</th>
                    <th style="text-align:left;">Placeholders</th>
                    <th style="text-align:left;">Action</th>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>

        <!-- currently selected templates -->
        <h3 style="margin-top:1.5rem;">Selected Templates</h3>
        <pre id="selectedTemplatesContainer" class="selected-prompts"
             style="background:#f7f7f7;padding:.75rem;border-radius:6px;white-space:pre-wrap;max-height:25vh;overflow:auto;"></pre>
    </div>
</div>

<!-- Template Render Modal -->
<div id="templateRenderModal" class="prompt-modal" style="display:none;">
    <div class="prompt-modal-content">
        <span class="close-button" style="float:right;cursor:pointer;font-size:24px;" onclick="document.getElementById('templateRenderModal').style.display='none'">&times;</span>
        <h2>Render Template</h2>
        <div class="template-render-content">
            <!-- Dynamic content will be inserted here -->
        </div>
    </div>
</div>

<!-- Template Button -->
<button id="openTemplateModal" class="button-secondary" style="margin-left: 10px;">
    📝 Templates
</button>
