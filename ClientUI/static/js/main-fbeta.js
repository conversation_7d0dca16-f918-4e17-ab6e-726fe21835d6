// Description: Main JavaScript file for the application.
// This file contains functions for handling user interactions, fetching data from the server,
// and updating the UI. It includes functions for managing collections, sessions, and chat interactions.
// It also handles modals for creating folders, renaming sessions, and displaying messages.

document.addEventListener('DOMContentLoaded', () => {
    openCollectionsPage();      // show COLLECTIONS
    closeChatPage();            // hide CHAT
    closeRelevantDocumentsSideBar();
    fetchCollections();
    attachLogoutHandler();
    loadSlashCommands();        // load slash commands
    document.addEventListener('click', () => {
        closeAllDropdowns();
    });

    const logoutButton = document.getElementById("logout-button");
    if (logoutButton) {
        logoutButton.addEventListener("click", openLogoutModal);
    }

});


// ==============================
// INITIALIZATION & GLOBAL VARIABLES
// ==============================
let lastQueryId = null;
let collectionsData = [];
let currentSelectedCollectionData = {};
let selectedSessionData = {};
let selectedSessionId = null;
let allSessionsData = {};
let yesNoModalConfirmCallback = null;
let pdfContent = "";
let inlinePdfFiles = null;
let queries = [];
let currentUtterance = null;
let selectedFolder = null;
const urlParams = new URLSearchParams(window.location.search);
const accessToken = urlParams.get('access_token');
const selectedPrompts = []; // [{id,title,content,usecase,token_count}, …]
let searchDebounceTimer;

// ==============================
// SECURITY FUNCTIONS
// ==============================
function safeText(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// ==============================
// TOKEN & AUTHENTICATION
// ==============================
if (accessToken) {
    localStorage.setItem('access_token', accessToken);
    window.history.replaceState({}, document.title, window.location.pathname);
}

async function fetchWithAuth(url, options = {}) {
    const token = localStorage.getItem('access_token');
    if (token) {
        options.headers = {
            ...options.headers,
            'Authorization': `Bearer ${token}`
        };
    }
    try {
        const response = await fetch(url, options);
        if (response.status === 401) {
            const loginResponse = await fetch('/api/query/login-url');
            const loginData = await loginResponse.json();
            if (loginData.login_url) {
                window.location.href = loginData.login_url;
            } else {
                throw new Error('Login URL not found');
            }
        }
        return response;
    } catch (error) {
        console.error('Error in fetchWithAuth:', error);
        throw error;
    }
}

function getAuthToken() {
    return localStorage.getItem('access_token');
}

// ==============================
// PAGE DISPLAY FUNCTIONS
// ==============================

function openCollectionsPage() {
    const collections = document.getElementById('COLLECTIONS');
    if (collections) collections.style.display = 'flex';
}


function selectFolder(folderName, folderHeader) {
    if (selectedFolder === folderName) {
        selectedFolder = null;

        document.querySelectorAll('.folder-header').forEach(header => {
            header.classList.remove('selected-folder');
        });
        return;
    }

    document.querySelectorAll('.folder-header').forEach(header => {
        header.classList.remove('selected-folder');
    });

    folderHeader.classList.add('selected-folder');
    selectedFolder = folderName;
}


function openCreateFolderModal(collectionId) {
    const createFolderModal = document.getElementById('CreateFolderModal');
    const folderNameInput = document.getElementById('CreateFolderModalInput');

    folderNameInput.value = ''; // Clear previous input
    createFolderModal.style.display = 'block';

    const createButton = document.getElementById('CreateFolderModalCreateButton');
    createButton.onclick = async () => {
        const folderName = folderNameInput.value.trim();
        if (!folderName) {
            showMessageModal('Bitte geben Sie einen Ordnernamen ein.');
            return;
        }

        try {
            const response = await fetchWithAuth(`/api/query/create_folder/${collectionId}/${encodeURIComponent(folderName)}`, {
                method: 'POST'
            });
            if (!response.ok) {
                const err = await response.json();
                if (err.detail) {
                    showMessageModal(err.detail);
                } else {
                    showMessageModal('Ordner konnte nicht erstellt werden');
                }
                return;
            }
            fetchSessionsAndRender(collectionId);
            closeCreateFolderModal();
        } catch (error) {
            console.error('Fehler beim Erstellen des Ordners:', error);
            showMessageModal('Fehler beim Erstellen des Ordners. ' + error.message);
        }
    };
}

function closeCreateFolderModal() {
    const createFolderModal = document.getElementById('CreateFolderModal');
    if (createFolderModal) {
        createFolderModal.style.display = 'none';
    }
}


function openSessionLoadingModal() {
    document.getElementById("SessionLoadingModal").style.display = "block";
}

function closeSessionLoadingModal() {
    document.getElementById("SessionLoadingModal").style.display = "none";
}


function openYesNoModal(title, message, onConfirmCallback) {
    document.getElementById('YesNoModalTitle').innerText = title;
    document.getElementById('YesNoModalMessage').innerText = message;
    yesNoModalConfirmCallback = onConfirmCallback;
    document.getElementById('YesNoModal').style.display = 'block';
}

function closeYesNoModal() {
    document.getElementById('YesNoModal').style.display = 'none';
    yesNoModalConfirmCallback = null;
}

function confirmYesNoModal() {
    if (typeof yesNoModalConfirmCallback === 'function') {
        yesNoModalConfirmCallback();
    }
    closeYesNoModal();
}


function closeCollectionsPage() {
    const collections = document.getElementById('COLLECTIONS');
    if (collections) collections.style.display = 'none';
}

function openChatPage() {
    const main = document.getElementById('CHAT');
    if (main) main.style.display = 'flex';
}

function closeChatPage() {
    const main = document.getElementById('CHAT');
    if (main) main.style.display = 'none';
}

function closeRelevantDocumentsSideBar() {
    const sidebar = document.getElementById('relevant-documents');
    if (sidebar) sidebar.style.display = 'none';
}


function showMessageModal(message) {
    const modalMessage = document.getElementById('modalMessage');
    modalMessage.textContent = message;
    const messageModal = document.getElementById('messageModal');
    messageModal.style.display = 'block';
}

function closeMessageModal() {
    const messageModal = document.getElementById('messageModal');
    messageModal.style.display = 'none';
}


function openRenameSessionModal(session) {
    const modal = document.getElementById('RenameSessionModal');
    const input = document.getElementById('RenameSessionModalInput');
    input.value = session.title || '';
    modal.style.display = 'flex';

    const renameButton = document.getElementById('RenameSessionModalRenameButton');
    const cancelButton = document.getElementById('RenameSessionModalCancelButton');

    // Clear any old event listeners
    renameButton.onclick = null;
    cancelButton.onclick = null;

    renameButton.onclick = () => {
        const newName = input.value.trim();
        if (newName) {
            renameSession(session.id, newName);
        }
        modal.style.display = 'none';
    };

    cancelButton.onclick = () => {
        modal.style.display = 'none';
    };
}

async function waitForElementById(id, maxRetries = 30, interval = 100) {
    for (let i = 0; i < maxRetries; i++) {
        const el = document.getElementById(id);
        if (el) return el;
        await new Promise(resolve => setTimeout(resolve, interval));
    }
    return null;
}


async function sendQueryStreaming(noSessionNoFolderSelected = false) {
    inlinePdfFiles = null;
    const sessionId = selectedSessionData.id;

    const questionInput = document.getElementById('questionInput');
    let question = questionInput.value.trim();

    if (!question) {
        showMessageModal('Bitte geben Sie eine Frage ein.');
        return;
    }

    if (!currentSelectedCollectionData) {
        showMessageModal('Bitte wählen Sie eine gültige Sammlung');
        return;
    }

    let payload = {
        content: question,
        inline_pdf_content: pdfContent,
    };

    // Build custom prompt from selected prompts OR templates (not both)
    let customPrompt = '';

    if (selectedTemplates.length > 0) {
        // Templates take precedence over prompts
        const templateStr = selectedTemplates.map(t => t.rendered_content || t.template_content || '').join(' ').trim();
        customPrompt = templateStr;
    } else if (selectedPrompts.length > 0) {
        // Only use prompts if no templates are selected
        const promptStr = selectedPrompts.map(p => p.content || '').join(' ').trim();
        customPrompt = promptStr;
    }

    if (customPrompt) {
        payload['custom_prompt'] = customPrompt;
    }

    if (selectedSessionData && selectedSessionData !== null && selectedSessionData.id && selectedSessionData.id !== null) {
        payload['session_id'] = selectedSessionData.id;
    } else {
        openSessionLoadingModal();
        setTimeout(() => {
            fetchSessionsAndRender(currentSelectedCollectionData.id);
        }, 2000);
    }

    const queriesContainer = document.getElementById('chat-window');
    const userMessageContainer = document.createElement('div');
    userMessageContainer.classList.add('user-message-container');

    const userBubble = document.createElement('div');
    userBubble.classList.add('user-message-bubble');
    userBubble.innerHTML = `<p class="query-text-style">${question}</p>`;
    userMessageContainer.appendChild(userBubble);
    queriesContainer.appendChild(userMessageContainer);
    scrollToBottom('chat-window');

    const aiMessageId = `ai-message-${Date.now()}`;
    setTimeout(() => {
        const aiElement = document.getElementById(aiMessageId);
        if (aiElement) {
            aiElement.scrollIntoView({behavior: 'smooth', block: 'start'});
        }
    }, 100);

    const aiMessage = document.createElement('div');
    aiMessage.classList.add('ai-message-bubble');
    aiMessage.id = aiMessageId;

    aiMessage.innerHTML = `
      <div class="chat-wrapper">
        <div>
          <svg class="svg-icon-query-answer" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 22" fill="currentColor">
            <path d="M18.1104 10.7774C19.9436 12.6106 22.9991 12.9162 22.9991 12.9162C22.9991 12.9162 19.7944 13.4023 18.1104 14.7494C16.4265 16.0965 16.2772 19.9437 16.2772 19.9437C16.2772 19.9437 15.9716 16.5827 14.1383 14.7494C12.3051 12.9162 9.55518 12.9162 9.55518 12.9162C9.55518 12.9162 12.1828 12.4884 14.1383 10.7774C16.0938 9.06632 16.2772 5.88867 16.2772 5.88867C16.2772 5.88867 16.2732 8.94 18.1104 10.7774Z"/>
            <path d="M8.72166 3.76317C9.72158 4.79936 11.3882 4.97206 11.3882 4.97206C11.3882 4.97206 9.64017 5.24685 8.72166 6.00825C7.80316 6.76965 7.72171 8.94412 7.72171 8.94412C7.72171 8.94412 7.55502 7.04444 6.55506 6.00825C5.55511 4.97206 4.05518 4.97206 4.05518 4.97206C4.05518 4.97206 5.48844 4.73028 6.55506 3.76317C7.62168 2.79606 7.72171 1 7.72171 1C7.72171 1 7.71951 2.72466 8.72166 3.76317Z"/>
            <path d="M5.66649 15.9849C6.66641 17.021 8.33303 17.1937 8.33303 17.1937C8.33303 17.1937 6.585 17.4685 5.66649 18.2299C4.74798 18.9913 4.66654 21.1658 4.66654 21.1658C4.66654 21.1658 4.49984 19.2661 3.49989 18.2299C2.49993 17.1937 1 17.1937 1 17.1937C1 17.1937 2.43327 16.952 3.49989 15.9849C4.5665 15.0177 4.66654 13.2217 4.66654 13.2217C4.66654 13.2217 4.66433 14.9463 5.66649 15.9849Z"/>
          </svg>
        </div>
        <div>
          <p class="query-text-style" style="white-space: break-spaces;" id="${aiMessageId}-text"></p>
          <br>
          <span class="change" style="display: none;"></span>
          <p class="query-text-style" id="${aiMessageId}-timestamp" style="display:none;"></p>
          <div class="loading-container" id="${aiMessageId}-loading">
            <div class="loading-bar"></div>
            <div class="loading-bar"></div>
            <div class="loading-bar"></div>
            <br>
            <p class="loading-text">Antwort wird generiert ...</p>
          </div>
        </div>
      </div>
    `;
    queriesContainer.appendChild(aiMessage);
    scrollToBottom('chat-window');

    const collectionId = currentSelectedCollectionData.id;
    try {
        questionInput.value = '';
        pdfContent = "";

        const response = await fetchWithAuth(`/api/query/collection/${collectionId}/query_stream`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify(payload),
        });
        if (!response.ok) {
            throw new Error(`[sendQueryStreaming] Server responded with status ${response.status}`);
        }
        if (!response.body) {
            throw new Error('[sendQueryStreaming] No readable stream from server');
        }
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let partialChunk = '';
        const aiTextElement = document.getElementById(`${aiMessageId}-text`);
        let firstChunkReceived = false;


        while (true) {
            const {done, value} = await reader.read();
            if (done) break;

            partialChunk += decoder.decode(value, {stream: true});

            try {
                while (true) {
                    const sepIndex = partialChunk.indexOf('}{');
                    if (sepIndex === -1) break;

                    const jsonStr = partialChunk.slice(0, sepIndex + 1);
                    partialChunk = partialChunk.slice(sepIndex + 1);

                    const parsed = JSON.parse(jsonStr);
                    if (parsed.query_id) lastQueryId = parsed.query_id;
                    if (selectedSessionId !== parsed.session_id) {
                        selectedSessionId = parsed.session_id;
                    }

                    // Remove spinner on first chunk:
                    if (!firstChunkReceived) {
                        const loadingContainer = document.getElementById(`${aiMessageId}-loading`);
                        if (loadingContainer) loadingContainer.remove();
                        firstChunkReceived = true;
                    }

                    const chunkContent = parsed.content || '';
                    aiTextElement.innerHTML += chunkContent;
                    scrollToBottom('chat-window');
                }
            } catch {
                // Keep waiting for next chunk if partial parse fails.
            }
        }

        if (partialChunk.trim()) {
            try {
                const lastObj = JSON.parse(partialChunk);
                if (lastObj.query_id) lastQueryId = lastObj.query_id;
                if (!firstChunkReceived) {
                    const loadingContainer = document.getElementById(`${aiMessageId}-loading`);
                    if (loadingContainer) loadingContainer.remove();
                    firstChunkReceived = true;
                }
                const chunkContent = lastObj.content || '';
                aiTextElement.innerHTML += chunkContent;
            } catch {
                // ignore leftover parse errors
            }
        }


        if (selectedSessionData.id !== null) {
            closeSessionLoadingModal();
            selectSession(selectedSessionId);
            selectedSessionId = null;
        }
        await fetchSessionQueries(selectedSessionData.id);

        if (lastQueryId) {
            const scrollTargetId = `query-${lastQueryId}`;
            setTimeout(async () => {
                const targetEl = await waitForElementById(scrollTargetId, 10, 50);
                if (targetEl) {
                    targetEl.scrollIntoView({behavior: 'smooth', block: 'start'});
                } else {
                    scrollToBottom('chat-window');
                }
            }, 100);
        }


    } catch (err) {
        console.error('[sendQueryStreaming] Error:', err);
    }
}

// ==============================
// COLLECTIONS & SESSIONS
// ==============================

async function fetchCollections() {
    try {
        const response = await fetchWithAuth('/api/loader/collections');
        const data = await response.json();
        collectionsData = data.collections || [];
        const user_name = data.user_info.user_name;
        const user_email = data.user_info.email;
        if (user_email) {
            localStorage.setItem("user_email", user_email);
            console.log("📥 Email from backend stored:", user_email);
        } else {
            console.warn("⚠️ No email found in user_info");
        }
        document.getElementById('userName').innerHTML = user_name;
        const frameElement = document.querySelector('.collection-items');
        if (collectionsData.length === 0) {
            frameElement.innerHTML = '<h1 style="color: #aaaaaa; margin-left: 15px;">No collections available. Please contact support.</h1>';
            return;
        }
        frameElement.innerHTML = '';
        collectionsData.forEach(collection => {
            const item = document.createElement('div');
            item.classList.add('div');
            item.dataset.collectionId = collection.id;

            const title = document.createElement('div');
            title.classList.add('templates');
            title.textContent = collection.name;
            const emptyLine = document.createElement('br');
            const description = document.createElement('p');
            description.classList.add('certainly-i-d-be');
            description.innerHTML = collection.description || 'Short description of the selected department/area';

            item.appendChild(title);
            item.appendChild(emptyLine);
            item.appendChild(description);

            item.addEventListener('click', (event) => selectCollection(event, collection.id));

            frameElement.appendChild(item);
        });
    } catch (error) {
        console.error('Error fetching collections:', error);
    }
}

function applyCollectionNameToChat(collectionName) {
    const chatCollectionName = document.getElementById('collection-name-in-chat');
    if (chatCollectionName) {
        chatCollectionName.textContent = collectionName;
    } else {
        console.error('chatCollectionName not found');
        chatCollectionName.textContent = "Unknown";
    }

}

function selectCollection(event, collectionId) {
    closeCollectionsPage();
    document.querySelectorAll('.collection-items .div').forEach(div => {
        div.classList.remove('selected');
    });

    if (event) {
        const selectedDiv = event.currentTarget;
        selectedDiv.classList.add('selected');
    }


    const selectedCollection = collectionsData.find(c => c.id === collectionId);
    if (selectedCollection) {
        currentSelectedCollectionData = selectedCollection;
        fetchSessionsAndRender(collectionId);
        applyCollectionNameToChat(selectedCollection.name)
        closeCollectionsPage();
        openChatPage();
        openingPageMessage();
    } else {
        currentSelectedCollectionData = {};
    }
}

function openingPageMessage() {
    const queriesContainer = document.getElementById('chat-window');
    if (!queriesContainer) return;
    queriesContainer.innerHTML = '';
    if (queries.length === 0) {
        queriesContainer.innerHTML = `
      <div class="no-queries-text" id="no-queries-text">
      </div>
    `;
    }
}


async function fetchSessionsAndRender(collectionId) {
    try {
        const response = await fetchWithAuth(`/api/query/get_sessions_by_collection_id/${collectionId}`);
        if (!response.ok) {
            throw new Error(`Failed to fetch sessions (status: ${response.status})`);
        }
        const result = await response.json();

        renderSessionsSidebar(result);
        allSessionsData = result;

    } catch (error) {
        console.error('Error fetching sessions:', error);
        showMessageModal(`Error: ${error.message}`);
    }
}

function createCollapsibleHeader(titleText, contentContainer) {
    const wrapperDiv = document.createElement('div');
    wrapperDiv.classList.add('session-item');
    wrapperDiv.dataset.folderName = titleText;

    const headerDiv = document.createElement('div');
    headerDiv.classList.add('text-wrapper-3');
    headerDiv.style.display = 'flex';
    headerDiv.style.alignItems = 'center';
    headerDiv.style.justifyContent = 'space-between';
    headerDiv.textContent = titleText;

    const arrowIcon = document.createElement('img');
    arrowIcon.src = "/static/img/left-sidebar/chevron-up.svg";
    arrowIcon.classList.add('folder-toggle-arrow');

    let isOpen = true;
    arrowIcon.addEventListener('click', () => {
        isOpen = !isOpen;
        if (isOpen) {
            contentContainer.style.display = 'block';
            arrowIcon.src = "/static/img/left-sidebar/chevron-up.svg";
        } else {
            contentContainer.style.display = 'none';
            arrowIcon.src = "/static/img/left-sidebar/chevron-down.svg";
        }
    });

    wrapperDiv.appendChild(headerDiv);
    wrapperDiv.appendChild(arrowIcon);

    if (titleText === "Aktuelles") {
        const plusIcon = document.createElement('img');
        plusIcon.classList.add('add-folder-icon');
        plusIcon.src = "/static/img/left-sidebar/add.svg";
        plusIcon.style.width = "20px";
        plusIcon.style.height = "20px";
        plusIcon.style.cursor = "pointer";
        plusIcon.addEventListener('click', (event) => {
            event.stopPropagation();
            openCreateFolderModal(currentSelectedCollectionData.id);
        });

        plusIcon.style.display = "block";
        arrowIcon.style.display = "block";

        wrapperDiv.appendChild(plusIcon);
    }

    return wrapperDiv;
}


function renderSessionsSidebar(data) {

    const foldersData = data.folders;
    if (!foldersData) {
        console.error('No "folders" key found in response');
        return;
    }

    // 1) Favorites Container
    const favoritesContainer = document.querySelector('.sessions-favorite');
    favoritesContainer.innerHTML = '';

    const favoritesContent = document.createElement('div');
    favoritesContent.style.display = 'block';
    const favoritesHeader = createCollapsibleHeader('Favoriten', favoritesContent);

    favoritesContainer.appendChild(favoritesHeader);
    favoritesContainer.appendChild(favoritesContent);

    // 2) Recents Container
    const recentsContainer = document.querySelector('.sessions-recent');
    recentsContainer.innerHTML = '';
    const recentsContent = document.createElement('div');
    recentsContent.style.display = 'block';
    const recentsHeader = createCollapsibleHeader('Aktuelles', recentsContent);
    favoritesContainer.appendChild(recentsHeader);
    recentsContainer.appendChild(recentsContent);

    const favorites = foldersData.favorites || [];
    favorites.forEach(session => {
        const sessionItem = createSessionItem(session);
        favoritesContent.appendChild(sessionItem);
    });

    const folderKeys = Object.keys(foldersData).filter(
        (key) => key !== 'favorites' && key !== 'unassigned'
    );
    folderKeys.forEach(folderName => {
        const folderSessions = foldersData[folderName] || [];
        const folderElement = createFolderItem(folderName, folderSessions);
        recentsContent.appendChild(folderElement);
    });

    let unassigned = foldersData.unassigned || [];
    unassigned = unassigned.filter(session => !session.is_favorite);

    unassigned.forEach(session => {
        const sessionItem = createSessionItem(session);
        recentsContent.appendChild(sessionItem);
    });
}


// ==============================
// SESSION ITEM CREATION & DROPDOWN (Sidebar)
// ==============================

function createThreeDotButton() {
    const threeDot = document.createElement('div');
    threeDot.classList.add('three-dot');
    const dot1 = document.createElement('div');
    dot1.classList.add('three-dot-ellipse');
    const dot2 = document.createElement('div');
    dot2.classList.add('three-dot-div');
    const dot3 = document.createElement('div');
    dot3.classList.add('three-dot-ellipse-2');
    threeDot.appendChild(dot1);
    threeDot.appendChild(dot2);
    threeDot.appendChild(dot3);
    return threeDot;
}

// ==============================
// UPLOADED FILES MODAL
// ==============================

function openUploadedFilesModal(session) {
    const modal = document.getElementById('UploadedFilesModal');

    if (modal) {
        fetchUploadedFiles(session);
        modal.style.display = 'flex';
    }
}

function closeUploadedFilesModal() {
    const modal = document.getElementById('UploadedFilesModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

async function fetchUploadedFiles(session) {
    const container = document.getElementById("uploaded-files-list");
    if (!container) return;
    container.innerHTML = "";
    const uploadedFiles = session.uploaded_pdfs || '';
    if (!uploadedFiles) {
        container.innerHTML = '<p class="text-wrapper">Keine Dateien gefunden.</p>';
        return;
    }
    const fileList = uploadedFiles.split(', ');
    fileList.forEach(file => {
        const listItem = document.createElement('p');
        listItem.classList.add('text-wrapper');
        listItem.textContent = `📁 ${file}`;
        container.appendChild(listItem);
    });
}


// ==============================
// DROPDOWN MENU FOR SESSION ITEM (UPDATED)
// ==============================

function createDropdownMenu(session) {
    const dropdown = document.createElement('div');
    dropdown.classList.add('dropdown');
    dropdown.innerHTML = `
        <div class="dropdown-div">
            <div class="menuitem rename-session">
                <img class="img" src="/static/img/left-sidebar/dropdown-marker.svg" alt="Umbenennen" />
                <div class="text-wrapper">Umbenennen</div>
            </div>
            <div class="menuitem favorite-session">
                <img class="img" src="/static/img/left-sidebar/dropdown-favorite.svg" alt="Favorit" />
                <div class="text-wrapper">Favorit</div>
            </div>
            <div class="menuitem download-session">
                <img class="img" src="/static/img/left-sidebar/dropdown-download.svg" alt="Als Datei herunterladen" />
                <div class="text-wrapper">Als Datei herunterladen</div>
            </div>
            <div class="menuitem show-files">
                <img class="img" src="/static/img/left-sidebar/folder-black.svg" alt="Dateien anzeigen" />
                <div class="text-wrapper">Hochgeladene Dateien anzeigen</div>
            </div>
            <div class="menuitem delete-session">
                <img class="img" src="/static/img/left-sidebar/dropdown-bin.svg" alt="Löschen" />
                <div class="div">Verlaufseintrag löschen</div>
            </div>
        </div>
    `;
    dropdown.style.display = 'none';
    if (session.uploaded_pdfs) {
        dropdown.querySelector('.show-files').addEventListener('click', function (event) {
            event.stopPropagation();
            openUploadedFilesModal(session);
        });
    }
    return dropdown;
}

function closeAllDropdowns() {
    document.querySelectorAll('.dropdown').forEach(dropdown => {
        dropdown.style.display = 'none';
    });

    document.querySelectorAll('.adjust-dropdown').forEach(adjustDropdown => {
        adjustDropdown.style.display = 'none';
    });
}

// ==============================
// SESSION SELECTION & CHAT UPDATE
// ==============================

async function selectSession(sessionId) {
    if (selectedSessionData.id === sessionId) {
        document.querySelectorAll('.session-item').forEach(item => {
            item.classList.remove('selected');
        });

        const selectedSessionItem = document.querySelector(`.session-item[data-session-id="${sessionId}"]`);
        if (selectedSessionItem) {
            selectedSessionItem.classList.add('selected');
        }

        return;
    }

    selectedSessionData = {id: sessionId};

    document.querySelectorAll('.session-item').forEach(item => {
        item.classList.remove('selected');
    });

    const selectedSessionItem = document.querySelector(`.session-item[data-session-id="${sessionId}"]`);
    if (selectedSessionItem) {
        selectedSessionItem.classList.add('selected');
    }

    document.querySelectorAll('.session-button')?.forEach(button => {
        button.classList.remove('selected');
    });
    const sessionButton = document.querySelector(`.session-button[data-session-id="${sessionId}"]`);
    if (sessionButton) {
        sessionButton.classList.add('selected');
    }

    const chatWindow = document.querySelector('.chat-window');
    let queriesContainer = document.getElementById('chat-window');
    if (queriesContainer) {
        queriesContainer.innerHTML = '';
    } else {
        queriesContainer = document.createElement('div');
        queriesContainer.id = 'chat-window';
        chatWindow.insertBefore(queriesContainer, chatWindow.firstChild);
    }


    const unassignedSessions = allSessionsData?.folders?.unassigned || [];
    const isUnassigned = unassignedSessions.some(session => session.id === sessionId);


    // Get session queries
    fetchSessionQueries(sessionId);
}

async function fetchSessionQueries(sessionId) {
    try {
        const response = await fetchWithAuth(`/api/query/get_queries_by_session_id/${sessionId}`);
        const queries = await response.json();

        if (!Array.isArray(queries)) {
            console.error('Expected an array, but got:', queries);
            return;
        }
        displayQueries(sessionId, queries);
    } catch (error) {
        console.error('Error fetching session queries:', error);
        showMessageModal(`Fehler beim Abrufen der Abfragen: ${error.message}`);
    }
}


function onSessionItemDragOver(e) {
    e.preventDefault();
}

async function onSessionItemDrop(e, recentsSessionId) {
    e.preventDefault();
    const draggedSessionId = e.dataTransfer.getData("text/plain");
    if (!draggedSessionId) return;
    const cId = currentSelectedCollectionData.id;
    try {
        const getResp = await fetchWithAuth(`/api/query/get_sessions_by_collection_id/${cId}`);
        if (!getResp.ok) throw new Error('Failed to get data');
        const data = await getResp.json();
        const folders = data.folders;
        if (!folders) return;
        const folderNames = Object.keys(folders).filter(k => k !== 'favorites' && k !== 'unassigned');
        let folderName = null;
        folderNames.forEach(f => {
            const found = folders[f].find(x => x.id === draggedSessionId);
            if (found) folderName = f;
        });
        if (folderName) {
            const url = `/api/query/remove_session_from_folder?folder_name=${encodeURIComponent(folderName)}&session_id=${encodeURIComponent(draggedSessionId)}&collection_id=${encodeURIComponent(cId)}`;
            await fetchWithAuth(url, {method: 'POST'});
            await fetchSessionsAndRender(cId);
        }
    } catch (err) {
        console.error('onSessionItemDrop error:', err);
        showMessageModal('Fehler beim Entfernen der Sitzung aus dem Ordner');
    }
}

async function updateFavoriteStatus(sessionId, isFavorite) {
    try {
        const url = `/api/query/update_favorite_status/${sessionId}?is_favorite=${isFavorite}`;
        const response = await fetchWithAuth(url, {method: 'PUT'});
        if (!response.ok) {
            throw new Error('Error updating favorite status');
        }
        if (currentSelectedCollectionData.id) {
            fetchSessionsAndRender(currentSelectedCollectionData.id);
        }
    } catch (error) {
        console.error('Error updating favorite status:', error);
        showMessageModal('Fehler beim Aktualisieren des Favoritenstatus');
    }
}

function deleteFolder(collectionId, folderName) {
    openYesNoModal(
        "Ordner löschen",
        `Bist du sicher, dass du den Ordner "${folderName}" löschen möchtest?`,
        async function () {
            try {
                const response = await fetchWithAuth(`/api/query/delete_folder/${collectionId}/${encodeURIComponent(folderName)}`, {
                    method: 'DELETE',
                });

                if (!response.ok) {
                    throw new Error('Error deleting folder');
                }

                if (currentSelectedCollectionData.id) {
                    fetchSessionsAndRender(currentSelectedCollectionData.id);
                }
            } catch (error) {
                console.error('Error deleting folder:', error);
                showMessageModal('Fehler beim Löschen des Ordners');
            }
        }
    );
}

function renameFolder(collectionId, oldFolderName) {
    const modal = document.getElementById('RenameSessionModal');
    const input = document.getElementById('RenameSessionModalInput');

    input.value = oldFolderName;
    modal.style.display = 'flex';

    const renameButton = document.getElementById('RenameSessionModalRenameButton');
    const cancelButton = document.getElementById('RenameSessionModalCancelButton');

    renameButton.onclick = async () => {
        const newFolderName = input.value.trim();
        if (newFolderName && newFolderName !== oldFolderName) {
            try {
                const response = await fetchWithAuth(
                    `/api/query/rename_folder/${collectionId}/${encodeURIComponent(oldFolderName)}/${encodeURIComponent(newFolderName)}`,
                    {method: 'POST'}
                );

                if (!response.ok) {
                    throw new Error('Error renaming folder');
                }

                showMessageModal(`Ordner erfolgreich in "${newFolderName}" umbenannt`);
                if (currentSelectedCollectionData.id) {
                    fetchSessionsAndRender(currentSelectedCollectionData.id);
                }
            } catch (error) {
                console.error('Error renaming folder:', error);
                showMessageModal('Fehler beim Umbenennen des Ordners');
            }
        }
        modal.style.display = 'none';
    };

    cancelButton.onclick = () => {
        modal.style.display = 'none';
    };
}

// Modify folder dropdown menu to attach delete and rename events
function createFolderDropdownMenu(folderName) {
    const dropdown = document.createElement('div');
    dropdown.classList.add('dropdown');
    dropdown.innerHTML = `
    <div class="dropdown-div">
      <div class="menuitem rename-folder">
        <img class="img" src="/static/img/left-sidebar/dropdown-marker.svg" alt="Rename" />
        <div class="text-wrapper">Ordner umbenennen</div>
      </div>
      <div class="menuitem delete-folder">
        <img class="img" src="/static/img/left-sidebar/dropdown-bin.svg" alt="Delete" />
        <div class="div">Ordner löschen</div>
      </div>
    </div>
  `;
    dropdown.style.display = 'none';

    // Attach delete event listener
    const deleteMenuItem = dropdown.querySelector('.delete-folder');
    deleteMenuItem.addEventListener('click', function (event) {
        event.stopPropagation();
        dropdown.style.display = 'none';
        deleteFolder(currentSelectedCollectionData.id, folderName);
    });

    // Attach rename event listener
    const renameMenuItem = dropdown.querySelector('.rename-folder');
    renameMenuItem.addEventListener('click', function (event) {
        event.stopPropagation();
        dropdown.style.display = 'none';
        renameFolder(currentSelectedCollectionData.id, folderName);
    });

    return dropdown;
}

function createFolderItem(folderName, folderSessions) {
    const folderContainer = document.createElement('div');
    folderContainer.classList.add('folder-container');
    folderContainer.style.marginBottom = '30px';

    const folderHeader = document.createElement('div');
    folderHeader.classList.add('session-item');
    folderContainer.appendChild(folderHeader);
    folderHeader.addEventListener('dragover', onFolderDragOver);
    folderHeader.addEventListener('drop', e => onFolderDrop(e, folderName));
    const folderIcon = document.createElement('img');
    folderIcon.src = "/static/img/left-sidebar/folder.svg";
    folderIcon.classList.add('folder-icon');
    folderIcon.style.width = '16px';
    folderIcon.style.height = '16px';
    folderIcon.style.marginRight = '8px';
    folderIcon.style.flexShrink = '0';
    folderHeader.appendChild(folderIcon);

    // folder title
    const folderTitle = document.createElement('div');
    folderTitle.classList.add('text-wrapper-2');
    folderTitle.textContent = folderName;
    folderHeader.appendChild(folderTitle);

    // close/open arrow
    let isOpen = false;
    const toggleArrow = document.createElement('img');
    toggleArrow.src = "/static/img/left-sidebar/chevron-up.svg";
    toggleArrow.classList.add('folder-toggle-arrow');
    toggleArrow.style.width = '24px';
    toggleArrow.style.height = '24px';
    toggleArrow.style.marginRight = '8px';
    toggleArrow.style.flexShrink = '0';
    toggleArrow.cursor = 'pointer';
    folderHeader.appendChild(toggleArrow);
    const threeDotButton = createThreeDotButton();
    folderHeader.appendChild(threeDotButton);
    const dropdownMenu = createFolderDropdownMenu(folderName);
    folderHeader.appendChild(dropdownMenu);

    threeDotButton.addEventListener('click', function (event) {
        event.stopPropagation();
        closeAllDropdowns();

        if (dropdownMenu.parentElement !== document.body) {
            folderHeader.removeChild(dropdownMenu);
            document.body.appendChild(dropdownMenu);
        }

        if (dropdownMenu.style.display === 'block') {
            dropdownMenu.style.display = 'none';
        } else {
            dropdownMenu.style.display = 'block';
            const buttonRect = threeDotButton.getBoundingClientRect();
            dropdownMenu.style.position = 'absolute';
            dropdownMenu.style.top = buttonRect.top + 'px';
            dropdownMenu.style.left = (buttonRect.right + 8) + 'px';
            dropdownMenu.style.zIndex = 9999;
        }
    });

    const folderSessionsContainer = document.createElement('div');
    folderSessionsContainer.classList.add('folder-sessions');
    folderContainer.appendChild(folderSessionsContainer);
    folderSessionsContainer.style.display = 'none';

    // 4) open/close arrow click event
    toggleArrow.addEventListener('click', (e) => {
        e.stopPropagation();
        isOpen = !isOpen;
        if (isOpen) {
            folderSessionsContainer.style.display = 'block';
            toggleArrow.src = "/static/img/left-sidebar/chevron-up.svg";
        } else {
            folderSessionsContainer.style.display = 'none';
            toggleArrow.src = "/static/img/left-sidebar/chevron-down.svg";
        }
    });

    // 5) list of sessions
    folderSessions.forEach(session => {
        const sessionItem = createSessionItem(session, true);
        folderSessionsContainer.appendChild(sessionItem);
    });

    // 6) mouse enter/leave events
    threeDotButton.style.display = "block";
    toggleArrow.style.display = "block";

    folderHeader.classList.add('folder-header');
    folderHeader.dataset.folder = folderName;
    folderHeader.addEventListener('click', (event) => {
        event.stopPropagation();
        selectFolder(folderName, folderHeader);
    });


    return folderContainer;
}

function createSessionItem(session, isFolderSession = false) {
    const sessionItem = document.createElement('div');
    sessionItem.classList.add('session-item');
    sessionItem.classList.add('real-item');
    sessionItem.dataset.sessionId = session.id;

    if (!session.is_favorite) {
        sessionItem.draggable = true;
        sessionItem.addEventListener('dragstart', e => onSessionDragStart(e, session));
        sessionItem.addEventListener('dragover', onSessionItemDragOver);
        sessionItem.addEventListener('drop', e => onSessionItemDrop(e, session.id));
    }
    let icon;
    if (session.is_favorite) {
        icon = document.createElementNS("http://www.w3.org/2000/svg", "svg");
        icon.classList.add("favorites-star-icon");
        icon.setAttribute("width", "20");
        icon.setAttribute("height", "20");
        icon.setAttribute("viewBox", "0 0 24 24");
        icon.setAttribute("fill", "currentColor");
        const path = document.createElementNS("http://www.w3.org/2000/svg", "path");
        path.setAttribute("d", "M5.825 22L7.45 14.975L2 10.25L9.2 9.625L12 3L14.8 9.625L22 10.25L16.55 14.975L18.175 22L12 18.275L5.825 22Z");
        icon.appendChild(path);
    } else {
        icon = document.createElementNS("http://www.w3.org/2000/svg", "svg");
        icon.classList.add("chat-bubble");
        icon.setAttribute("width", "16");
        icon.setAttribute("height", "16");
        icon.setAttribute("viewBox", "0 0 20 20");
        icon.setAttribute("fill", "none");
        const g = document.createElementNS("http://www.w3.org/2000/svg", "g");
        g.setAttribute("clip-path", "url(#clip0_105_959)");
        const path = document.createElementNS("http://www.w3.org/2000/svg", "path");
        path.setAttribute("d", "M2.5 16.6667L3.58333 13.4167C1.64667 10.5525 2.395 6.85668 5.33333 4.77168C8.27167 2.68751 12.4917 2.85835 15.2042 5.17168C17.9167 7.48585 18.2833 11.2267 16.0617 13.9225C13.84 16.6183 9.71583 17.435 6.41667 15.8333L2.5 16.6667Z");
        path.setAttribute("stroke", "white");
        path.setAttribute("stroke-width", "1.2");
        path.setAttribute("stroke-linecap", "round");
        path.setAttribute("stroke-linejoin", "round");
        g.appendChild(path);
        icon.appendChild(g);
    }
    sessionItem.appendChild(icon);
    const textDiv = document.createElement('div');
    textDiv.classList.add('text-wrapper-2');
    textDiv.textContent = session.title || "New Session";
    sessionItem.appendChild(textDiv);
    const threeDotButton = createThreeDotButton();
    sessionItem.appendChild(threeDotButton);
    const dropdownMenu = createDropdownMenu(session);
    sessionItem.appendChild(dropdownMenu);

    threeDotButton.addEventListener('click', function (event) {
        event.stopPropagation();
        closeAllDropdowns();
        if (dropdownMenu.parentElement !== document.body) {
            sessionItem.removeChild(dropdownMenu);
            document.body.appendChild(dropdownMenu);
        }

        if (dropdownMenu.style.display === 'block') {
            dropdownMenu.style.display = 'none';
        } else {
            dropdownMenu.style.display = 'block';
            const buttonRect = threeDotButton.getBoundingClientRect();
            dropdownMenu.style.position = 'absolute';
            dropdownMenu.style.top = buttonRect.top + 'px';
            dropdownMenu.style.left = (buttonRect.right + 8) + 'px';
            dropdownMenu.style.zIndex = 9999;
        }
    });

    textDiv.addEventListener('click', () => {
        selectSession(session.id);
    });

    const renameMenuItem = dropdownMenu.querySelector('.menuitem:nth-child(1)');
    renameMenuItem.addEventListener('click', function (event) {
        event.stopPropagation();
        dropdownMenu.style.display = 'none';
        openRenameSessionModal(session);
    });
    const favoriteMenuItem = dropdownMenu.querySelector('.menuitem:nth-child(2)');
    if (session.is_favorite) {
        dropdownMenu.querySelector('.menuitem:nth-child(2) .text-wrapper').textContent = 'Aus Favoriten entfernen';
        favoriteMenuItem.addEventListener('click', function (event) {
            event.stopPropagation();
            dropdownMenu.style.display = 'none';
            updateFavoriteStatus(session.id, false);
        });
    } else {
        dropdownMenu.querySelector('.menuitem:nth-child(2) .text-wrapper').textContent = 'Favorit';
        favoriteMenuItem.addEventListener('click', function (event) {
            event.stopPropagation();
            dropdownMenu.style.display = 'none';
            updateFavoriteStatus(session.id, true);
        });
    }
    const downloadMenuItem = dropdownMenu.querySelector('.menuitem:nth-child(3)');
    dropdownMenu.querySelector('.menuitem:nth-child(3) .text-wrapper').textContent = 'Als PDF herunterladen';
    downloadMenuItem.addEventListener('click', function (event) {
        event.stopPropagation();
        dropdownMenu.style.display = 'none';
        downloadChatHistory();
    });

    const showFilesMenuItem = dropdownMenu.querySelector('.menuitem:nth-child(4)');
    showFilesMenuItem.addEventListener('click', function (event) {
        event.stopPropagation();
        dropdownMenu.style.display = 'none';
        openUploadedFilesModal(session);
    });

    const deleteMenuItem = dropdownMenu.querySelector('.menuitem:nth-child(5)');
    dropdownMenu.querySelector('.menuitem:nth-child(5) .div').textContent = 'Verlaufseintrag löschen';
    deleteMenuItem.addEventListener('click', function (event) {
        event.stopPropagation();
        dropdownMenu.style.display = 'none';
        openYesNoModal(
            "Sitzung löschen",
            "Bist du sicher, dass du diese Sitzung löschen möchtest?",
            function () {
                deleteSession(session.id);
            }
        );
    });

    threeDotButton.style.display = "block";

    return sessionItem;
}

function onSessionDragStart(event, session) {
    event.dataTransfer.setData("text/plain", session.id);
}

function onFolderDragOver(event) {
    event.preventDefault();
}

async function onFolderDrop(event, folderName) {
    event.preventDefault();
    const sessionId = event.dataTransfer.getData("text/plain");
    if (!sessionId) return;
    const collectionId = currentSelectedCollectionData.id;
    try {
        const url = `/api/query/move_session_to_folder?folder_name=${encodeURIComponent(folderName)}&session_id=${encodeURIComponent(sessionId)}&collection_id=${encodeURIComponent(collectionId)}`;
        const r = await fetchWithAuth(url, {method: 'POST'});
        if (!r.ok) throw new Error('Failed to move session');
        fetchSessionsAndRender(collectionId);
    } catch (err) {
        console.error('Error in onFolderDrop:', err);
        showMessageModal('Fehler beim Verschieben der Sitzung');
    }
}


async function fetchAndDisplayRelevantDocs(queryId) {
    try {
        const response = await fetchWithAuth(`/api/query/get_relevant_docs_by_query_id/${queryId}`);
        if (!response.ok) {
            throw new Error('Fehler beim Abrufen der relevanten Dokumente');
        }
        const relevantDocs = await response.json();

        populateRelevantDocsSidebar(relevantDocs);
    } catch (error) {
        console.error('Fehler beim Abrufen der relevanten Dokumente:', error);
        showMessageModal('Fehler beim Abrufen der Dokumente.');
    }
}

function populateRelevantDocsSidebar(docs) {
    const sidebar = document.getElementById('relevant-documents');

    const container = document.getElementById('relevant-docs-wrapper');
    container.innerHTML = `<h3 class='relevant-document-text'>Relevante Dokumente</h3>`;

    if (!docs.retrieved_docs) {
        container.innerHTML = `<p class='relevant-document-text' style="font-family: 'Audi Type-ExtendedBold', Helvetica;font-size: 18px;color: #fff;">Keine relevanten Dokumente gefunden.</p>`;
        return;
    }

    const ul = document.createElement('ul');
    let previousFileName = "";

    docs.retrieved_docs.forEach(doc => {
        let documentInfo;
        let score;

        if (Array.isArray(doc)) {
            documentInfo = doc[0];
            score = doc[1];
        } else {
            documentInfo = doc;
            score = null;
        }


        const li = document.createElement('li');

        let source = documentInfo.metadata.source;
        let fileName;

        if (source && (source.startsWith("http://") || source.startsWith("https://"))) {
            fileName = source;
        } else if (source) {
            fileName = source.split('/').pop();
        } else {
            fileName = previousFileName;
        }

        previousFileName = fileName;
        let page = documentInfo.metadata.page ? documentInfo.metadata.page : (documentInfo.metadata.page_num ? documentInfo.metadata.page_num : "0");

        li.innerHTML = `
    ${documentInfo.metadata.title ? `
        <p class='relevant-document-text' style="font-style: normal;line-height: 21px;font-weight: 400;font-family: 'Audi Type-ExtendedBold', Helvetica;font-size: 14px;color: #fff;">
            <strong>Titel:</strong> ${documentInfo.metadata.title}
        </p>` : ''
        }
            <p class='relevant-document-text' style="font-style: normal;line-height: 21px;font-weight: 400;font-family: 'Audi Type-ExtendedBold', Helvetica;font-size: 14px;color: #fff;"><strong>Quelldatei:</strong> ${fileName}</p>
            <p class='relevant-document-text' style="font-style: normal;line-height: 21px;font-family: 'Audi Type-ExtendedBold', Helvetica;font-size: 14px;color: #fff;"><strong>Inhalt:</strong> ${documentInfo.page_content}</p>
            <p class='relevant-document-text' style="font-style: normal;line-height: 21px;font-family: 'Audi Type-ExtendedBold', Helvetica;font-size: 14px;color: #fff;"><strong>Seitenzahl:</strong> ${page}</p>
            <hr>
        `;
        ul.appendChild(li);
    });

    container.appendChild(ul);

    sidebar.style.display = 'block';
}

async function handleStarIcon(queryId) {
    try {
        const collectionId = currentSelectedCollectionData?.id;
        if (!queryId || !collectionId) {
            console.error("handleStarIcon: Query ID or Collection ID is missing.");
            return;
        }

        const response = await fetchWithAuth(`/api/query/${queryId}/toggle_favorite/${collectionId}`, {
            method: 'PUT',
            headers: {'Content-Type': 'application/json'},
        });

        if (!response.ok) {
            throw new Error(`Favorites couldn't be updated. Status: ${response.status}`);
        }

        const result = await response.json();
        const isFavorite = result.is_favorite;

        const starIcon = document.getElementById(`star-${queryId}`);
        if (starIcon) {
            starIcon.src = isFavorite
                ? "/static/img/chat/star-favorited.svg"
                : "/static/img/chat/star.svg";
        }

        queries = queries.map(query => {
            if (query.id === queryId) {
                return {...query, is_favorite: isFavorite};
            }
            return query;
        });


    } catch (error) {
        console.error("Error while updating favorite status:", error);
    }
}

function displayQueries(sessionId, queries, inlinePdfFiles = "") {
    const queriesContainer = document.getElementById("chat-window");
    if (!queriesContainer) return;

    // Clear previous messages
    queriesContainer.innerHTML = "";

    /* ---------- OPENING PLACEHOLDER ---------- */
    if (queries.length === 0) {
        queriesContainer.innerHTML = ``;
        return;
    }

    /* ---------- RENDER EACH QUERY ---------- */
    queries.forEach((query, index) => {
        /* ---------- USER BUBBLE ---------- */
        const userMessageContainer = document.createElement("div");
        userMessageContainer.classList.add("user-message-container");

        const queryItem = document.createElement("div");
        queryItem.classList.add("user-message-bubble");
        queryItem.id = `query-${query.id}`;
        queryItem.innerHTML = `
      <p class="query-text-style">${query.content}</p>
    `;
        userMessageContainer.appendChild(queryItem);

        /* ---------- AI BUBBLE ---------- */
        const aiMessage = document.createElement("div");
        aiMessage.classList.add("ai-message-bubble");
        aiMessage.id = `ai-message-${query.id}`;

        const date = new Date(query.created_at);
        const day = ("0" + date.getDate()).slice(-2);
        const month = ("0" + (date.getMonth() + 1)).slice(-2);
        const year = date.getFullYear();
        const hours = ("0" + date.getHours()).slice(-2);
        const minutes = ("0" + date.getMinutes()).slice(-2);

        // bold tags (**text**) -> <strong>
        const formattedAnswer = query.answer
            ? query.answer.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
            : "No answer yet";

        aiMessage.innerHTML = `
      <div class="chat-wrapper">
        <!-- Star Icon -->
        <div>
          <svg class="svg-icon-query-answer" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 22" fill="currentColor">
            <path d="M18.1104 10.7774C19.9436 12.6106 22.9991 12.9162 22.9991 12.9162C22.9991 12.9162 19.7944 13.4023 18.1104 14.7494C16.4265 16.0965 16.2772 19.9437 16.2772 19.9437C16.2772 19.9437 15.9716 16.5827 14.1383 14.7494C12.3051 12.9162 9.55518 12.9162 9.55518 12.9162C9.55518 12.9162 12.1828 12.4884 14.1383 10.7774C16.0938 9.06632 16.2772 5.88867 16.2772 5.88867C16.2772 5.88867 16.2732 8.94 18.1104 10.7774Z"/>
            <path d="M8.72166 3.76317C9.72158 4.79936 11.3882 4.97206 11.3882 4.97206C11.3882 4.97206 9.64017 5.24685 8.72166 6.00825C7.80316 6.76965 7.72171 8.94412 7.72171 8.94412C7.72171 8.94412 7.55502 7.04444 6.55506 6.00825C5.55511 4.97206 4.05518 4.97206 4.05518 4.97206C4.05518 4.97206 5.48844 4.73028 6.55506 3.76317C7.62168 2.79606 7.72171 1 7.72171 1C7.72171 1 7.71951 2.72466 8.72166 3.76317Z"/>
            <path d="M5.66649 15.9849C6.66641 17.021 8.33303 17.1937 8.33303 17.1937C8.33303 17.1937 6.585 17.4685 5.66649 18.2299C4.74798 18.9913 4.66654 21.1658 4.66654 21.1658C4.66654 21.1658 4.49984 19.2661 3.49989 18.2299C2.49993 17.1937 1 17.1937 1 17.1937C1 17.1937 2.43327 16.952 3.49989 15.9849C4.5665 15.0177 4.66654 13.2217 4.66654 13.2217C4.66654 13.2217 4.66433 14.9463 5.66649 15.9849Z"/>
          </svg>
        </div>

        <!-- Answer + Icons -->
        <div>
          <!-- ANSWER CONTENT -->
          <div class="ai-answer-text query-text-style">${formattedAnswer}</div>
          <br>
          <span class="change" style="display: none;"></span>

          <!-- DATE -->
          <p class="query-text-style date-text">${day}.${month}.${year} um ${hours}:${minutes} Uhr</p>
          <br>

          <!-- ICONS -->
          <div class="chat-items-icons">
            <img
              class="speak-icon clickable-icon"
              src="/static/img/chat/voice.svg"
              style="width: 20px; height: 20px; cursor: pointer;"
              onclick="handleSpeakIcon(event)"
              title="Sprachausgabe"
            />

            <img
              class="copy-icon clickable-icon"
              src="/static/img/chat/copy-icon.svg"
              style="width: 20px; height: 20px; margin-left: 20px; cursor: pointer;"
              onclick="handleCopyIcon(event)"
              title="Kopie"
            />

            <img
              class="adjust-icon clickable-icon"
              src="/static/img/chat/adjust-response.svg"
              style="width: 20px; height: 20px; margin-left: 20px; cursor: pointer;"
              onclick="handleAdjustIcon(event, '${query.id}')"
            />

            <div class="rel-doc-div">
              <img
                class="relevant-doc-button clickable-icon"
                src="/static/img/chat/search.svg"
                style="width: 20px; height: 20px; margin-left: 20px; cursor: pointer;"
                onclick="fetchAndDisplayRelevantDocs('${query.id}')"
                title="Relevante Dokumente"
              />
              <span class="source-number">
                ${query.len_of_retrieved_docs || 0} sources
              </span>
            </div>
          </div>
        </div>
      </div>
    `;

        /* ---------- OPTIONAL INLINE PDF LIST ---------- */
        if (index === queries.length - 1 && inlinePdfFiles) {
            const pdfsHTML = `<p class="query-text-style"><i>Pdfs: </i>${inlinePdfFiles}</p>`;
            const tempDiv = document.createElement("div");
            tempDiv.innerHTML = aiMessage.innerHTML;
            const changeElement = tempDiv.querySelector(".change");
            if (changeElement) {
                changeElement.insertAdjacentHTML("afterend", pdfsHTML);
                changeElement.style.display = "block";
            }
            aiMessage.innerHTML = tempDiv.innerHTML;
        }

        /* ---------- APPEND TO CHAT WINDOW ---------- */
        queriesContainer.appendChild(userMessageContainer);
        queriesContainer.appendChild(aiMessage);
    });

    attachStarIconListeners();
    scrollToBottom("chat-window");
}


function handleAdjustIcon(event, queryId) {
    event.stopPropagation();
    closeAllDropdowns();
    let existingDropdown = document.getElementById("adjust-dropdown");
    if (existingDropdown) {
        existingDropdown.remove();
        return;
    }
    const dropdown = document.createElement("div");
    dropdown.id = "adjust-dropdown";
    dropdown.classList.add("adjust-dropdown");
    dropdown.innerHTML = `
    <div class="adjust-dropdown-item" data-command="shorter_answer">Kürzere Antwort</div>
    <div class="adjust-dropdown-item" data-command="more_detail">Längere Antwort</div>
    <div class="adjust-dropdown-item" data-command="less_detail">Weniger Details</div>
    <div class="adjust-dropdown-item" data-command="more_detail">Mehr Details</div>
  `;
    dropdown.querySelectorAll(".adjust-dropdown-item").forEach(item => {
        item.addEventListener("click", (e) => {
            const adjustCommand = e.target.getAttribute("data-command");
            sendQueryForAdjustStreaming(queryId, adjustCommand);
            dropdown.remove();
        });
    });
    document.body.appendChild(dropdown);
    positionAdjustDropdown(event.target, dropdown);
}

function positionAdjustDropdown(button, dropdown) {
    dropdown.style.display = "block";
    const rect = button.getBoundingClientRect();
    dropdown.style.position = "absolute";
    dropdown.style.left = `${rect.left + window.scrollX}px`;
    const dropdownHeight = dropdown.offsetHeight;
    dropdown.style.top = `${rect.top + window.scrollY - dropdownHeight - 5}px`;
    dropdown.style.zIndex = "1000";
}


async function sendQueryForAdjustStreaming(queryId, adjustCommand) {
    closeAllDropdowns();
    const containerId = `ai-message-${queryId}`;
    const payload = {
        session_id: selectedSessionData.id,
        query_id: queryId,
        adjust_type: adjustCommand,
        stream: true
    };

    const adjustHTML = `
    <div class="chat-wrapper">
      <div>
        <svg class="svg-icon-query-answer" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 22" fill="currentColor">
          <path d="M18.1104 10.7774C19.9436 12.6106 22.9991 12.9162 22.9991 12.9162C22.9991 12.9162 19.7944 13.4023 18.1104 14.7494C16.4265 16.0965 16.2772 19.9437 16.2772 19.9437C16.2772 19.9437 15.9716 16.5827 14.1383 14.7494C12.3051 12.9162 9.55518 12.9162 9.55518 12.9162C9.55518 12.9162 12.1828 12.4884 14.1383 10.7774C16.0938 9.06632 16.2772 5.88867 16.2772 5.88867C16.2772 5.88867 16.2732 8.94 18.1104 10.7774Z"/>
          <path d="M8.72166 3.76317C9.72158 4.79936 11.3882 4.97206 11.3882 4.97206C11.3882 4.97206 9.64017 5.24685 8.72166 6.00825C7.80316 6.76965 7.72171 8.94412 7.72171 8.94412C7.72171 8.94412 7.55502 7.04444 6.55506 6.00825C5.55511 4.97206 4.05518 4.97206 4.05518 4.97206C4.05518 4.97206 5.48844 4.73028 6.55506 3.76317C7.62168 2.79606 7.72171 1 7.72171 1C7.72171 1 7.71951 2.72466 8.72166 3.76317Z"/>
          <path d="M5.66649 15.9849C6.66641 17.021 8.33303 17.1937 8.33303 17.1937C8.33303 17.1937 6.585 17.4685 5.66649 18.2299C4.74798 18.9913 4.66654 21.1658 4.66654 21.1658C4.66654 21.1658 4.49984 19.2661 3.49989 18.2299C2.49993 17.1937 1 17.1937 1 17.1937C1 17.1937 2.43327 16.952 3.49989 15.9849C4.5665 15.0177 4.66654 13.2217 4.66654 13.2217C4.66654 13.2217 4.66433 14.9463 5.66649 15.9849Z"/>
        </svg>
      </div>
      <div>
        <p class="query-text-style" id="${containerId}-text"></p>
        <br>
        <span class="change" style="display:none;"></span>
        <p class="query-text-style" id="${containerId}-timestamp" style="display:none;"></p>
        <div class="loading-container" id="${containerId}-loading">
          <div class="loading-bar"></div>
          <div class="loading-bar"></div>
          <div class="loading-bar"></div>
          <br>
          <p class="loading-text">Antwort wird generiert ...</p>
        </div>
      </div>
    </div>
  `;
    const aiMessage = document.getElementById(containerId);
    if (aiMessage) {
        aiMessage.innerHTML = adjustHTML;
    }

    try {
        const response = await fetchWithAuth(`/api/query/collection/${currentSelectedCollectionData.id}/query_update`, {
            method: 'PUT',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify(payload),
        });
        if (!response.ok) {
            throw new Error(`Request failed with status: ${response.status}`);
        }
        if (!response.body) {
            throw new Error('No readable stream from server');
        }
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let partialChunk = '';
        const aiTextElement = document.getElementById(`${containerId}-text`);
        const timeEl = document.getElementById(`${containerId}-timestamp`);
        let firstChunkReceived = false;

        while (true) {
            const {done, value} = await reader.read();
            if (done) break;
            partialChunk += decoder.decode(value, {stream: true});
            try {
                while (true) {
                    const sepIndex = partialChunk.indexOf('}{');
                    if (sepIndex === -1) break;
                    const jsonStr = partialChunk.slice(0, sepIndex + 1);
                    partialChunk = partialChunk.slice(sepIndex + 1);
                    const parsed = JSON.parse(jsonStr);
                    if (!firstChunkReceived) {
                        const loadingContainer = document.getElementById(`${containerId}-loading`);
                        if (loadingContainer) loadingContainer.remove();
                        firstChunkReceived = true;
                    }
                    const chunkContent = parsed.content || '';
                    aiTextElement.innerHTML += chunkContent;
                    scrollToBottom(containerId);
                }
            } catch {
                // Wait for more data if JSON parsing fails.
            }
        }
        if (partialChunk.trim()) {
            try {
                const lastObj = JSON.parse(partialChunk);
                if (!firstChunkReceived) {
                    const loadingContainer = document.getElementById(`${containerId}-loading`);
                    if (loadingContainer) loadingContainer.remove();
                    firstChunkReceived = true;
                }
                const chunkContent = lastObj.content || '';
                aiTextElement.innerHTML += chunkContent;
            } catch {
            }
        }
        const now = new Date();
        const stamp = `${now.getDate()}.${now.getMonth() + 1}.${now.getFullYear()} um ${now.getHours()}:${String(now.getMinutes()).padStart(2, '0')} Uhr`;
        if (timeEl) {
            timeEl.textContent = stamp;
            timeEl.style.display = 'block';
        }
        await fetchSessionQueries(selectedSessionData.id);
        scrollToBottom(containerId);
    } catch (error) {
        console.error('[sendQueryForAdjustStreaming] Error:', error);
        if (aiMessage) {
            aiMessage.innerHTML = '<p class="query-text-style">Error occurred. Please try again later.</p>';
        }
    }
}

function handleSpeakIcon(event) {
    const icon = event.target;
    const messageContainer = icon.closest(".ai-message-bubble");

    if (!messageContainer) {
        console.warn("Message container not found.");
        return;
    }

    const textElement = messageContainer.querySelector(".query-text-style");
    if (!textElement) {
        console.warn("Text element not found.");
        return;
    }

    if (currentUtterance) {
        window.speechSynthesis.cancel();
        currentUtterance = null;
        icon.src = "/static/img/chat/voice.svg";
        return;
    }

    const textToRead = textElement.innerText || textElement.textContent;
    if ("speechSynthesis" in window) {
        const utterance = new SpeechSynthesisUtterance(textToRead);
        utterance.lang = "de-DE";
        utterance.rate = 0.75;

        icon.src = "/static/img/chat/stop-button.svg";
        currentUtterance = utterance;

        utterance.onend = () => {
            currentUtterance = null;
            icon.src = "/static/img/chat/voice.svg";
        };

        window.speechSynthesis.speak(utterance);
    } else {
        showMessageModal("Text-to-speech wird in Ihrem Browser nicht unterstützt.");
    }
}

function scrollToBottom(containerId) {
    const container = document.getElementById(containerId);
    if (!container) {
        console.error(`Container with ID "${containerId}" not found.`);
        return;
    }
    setTimeout(() => {
        container.scrollTop = container.scrollHeight;
    }, 100);
}


// ==============================
// UTILITY FUNCTIONS
// ==============================

async function hasSessionSystemPrompt() {
    // Check if the session has a system prompt to not show the chat goal mask

    const sessionId = selectedSessionData.id;
    const url = `/api/query/session/${sessionId}`;

    try {
        const response = await fetchWithAuth(url);

        if (!response.ok) {
            if (response.status === 404) {
                console.error('Session not found');
                return false;
            }
            throw new Error('Network response was not ok');
        }

        const data = await response.json();

        return !!data.custom_config?.system_prompt;
    } catch (error) {
        console.error('Error fetching session data:', error);
        return false;
    }
}

function attachLogoutHandler() {
    const logoutBtn = document.querySelector('.secondary-button-wrapper button.button');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }
}

// ==============================
// SESSION ACTIONS (RENAME, DELETE, DOWNLOAD)
// ==============================

async function renameSession(sessionId, newTitle) {
    try {

        const response = await fetchWithAuth(`/api/query/update_session/${sessionId}`, {
            method: 'PUT',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({title: newTitle}),
        });
        if (!response.ok) throw new Error('Error renaming session');
        if (currentSelectedCollectionData.id) {
            fetchSessionsAndRender(currentSelectedCollectionData.id);
        }
    } catch (error) {
        console.error('Error renaming session:', error);
        showMessageModal('Fehler beim Umbenennen der Sitzung');
    }
}

async function deleteSession(sessionId) {
    try {
        const response = await fetchWithAuth(`/api/query/delete_session/${sessionId}`, {
            method: 'DELETE',
        });
        if (!response.ok) throw new Error('Error deleting session');
        if (currentSelectedCollectionData.id) {
            fetchSessionsAndRender(currentSelectedCollectionData.id);
        }
    } catch (error) {
        console.error('Error deleting session:', error);
        showMessageModal('Fehler beim Löschen der Sitzung');
    }
}


async function downloadChatHistory() {
    // Use current session if available, otherwise show message
    const sessionToUse = selectedSessionData || (allSessionsData && allSessionsData.folders && allSessionsData.folders.recents && allSessionsData.folders.recents.length > 0 ? allSessionsData.folders.recents[0] : null);

    if (!sessionToUse || !sessionToUse.id) {
        showMessageModal('Bitte wählen Sie eine Sitzung aus oder starten Sie eine neue Sitzung.');
        return;
    }

    const sessionId = sessionToUse.id;
    const response = await fetchWithAuth(`/api/query/get_queries_by_session_id/${sessionId}`);
    const data = await response.json();

    if (!data || data.length === 0) {
        showMessageModal('Kein Chatverlauf zum Herunterladen verfügbar.');
        return;
    }

    const sessionTitle = data[0]?.session_title || 'Chat_History';

    const {jsPDF} = window.jspdf;
    const doc = new jsPDF({
        unit: 'pt',
        format: 'a4',
    });

    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();
    const leftMargin = 40;
    const rightMargin = 40;
    const topMargin = 50;
    const bottomMargin = 50;
    const usableWidth = pageWidth - leftMargin - rightMargin;
    let currentX = leftMargin;
    let currentY = topMargin;
    let pageCount = 1;

    function sliceCanvas(orjCanvas, sliceStartY, sliceHeight) {
        const sliceCanvas = document.createElement('canvas');
        sliceCanvas.width = orjCanvas.width;
        sliceCanvas.height = sliceHeight;

        const ctx = sliceCanvas.getContext('2d');
        ctx.drawImage(
            orjCanvas,
            0,
            sliceStartY,
            orjCanvas.width,
            sliceHeight,
            0,
            0,
            orjCanvas.width,
            sliceHeight
        );
        return sliceCanvas;
    }

    async function addCanvasToPDF(orjCanvas) {
        const canvasWidthPx = orjCanvas.width;
        const canvasHeightPx = orjCanvas.height;

        const scaleFactor = usableWidth / canvasWidthPx;
        const scaledHeightPt = canvasHeightPx * scaleFactor;

        let pageRemainingHeight = pageHeight - bottomMargin - currentY;
        if (scaledHeightPt <= pageRemainingHeight) {
            const canvasData = orjCanvas.toDataURL('image/png');
            doc.addImage(canvasData, 'PNG', currentX, currentY, usableWidth, scaledHeightPt);
            currentY += scaledHeightPt + 10;
            return;
        }

        let sliceStartY = 0;
        while (sliceStartY < canvasHeightPx) {
            pageRemainingHeight = pageHeight - bottomMargin - currentY;
            if (pageRemainingHeight < 50) {
                doc.addPage();
                pageCount++;
                currentY = topMargin;
                pageRemainingHeight = pageHeight - bottomMargin - currentY;
            }

            const sliceHeightPx = pageRemainingHeight / scaleFactor;

            const actualSliceHeight = Math.min(sliceHeightPx, canvasHeightPx - sliceStartY);

            const partialCanvas = sliceCanvas(orjCanvas, sliceStartY, actualSliceHeight);

            const partialCanvasData = partialCanvas.toDataURL('image/png');
            const partialScaledHeightPt = actualSliceHeight * scaleFactor;
            doc.addImage(partialCanvasData, 'PNG', currentX, currentY, usableWidth, partialScaledHeightPt);
            currentY += partialScaledHeightPt + 5;

            sliceStartY += actualSliceHeight;

        }
    }

    for (const query of data) {
        let date = new Date(query.created_at);
        let formattedDate = `${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}h on ${date.getDate()}-${date.getMonth() + 1}-${date.getFullYear()}`;

        if (currentY + 40 > pageHeight - bottomMargin) {
            doc.addPage();
            pageCount++;
            currentY = topMargin;
        }

        doc.setFontSize(12);
        doc.setTextColor(0);
        doc.text(`Question from ${formattedDate}:`, leftMargin, currentY);
        currentY += 16;

        const questionDiv = document.createElement('div');
        questionDiv.style.width = '600px';
        questionDiv.innerHTML = query.content || '';


        document.body.appendChild(questionDiv);
        const questionCanvas = await html2canvas(questionDiv, {
            scale: 1,
            logging: false
        });
        document.body.removeChild(questionDiv);

        await addCanvasToPDF(questionCanvas);

        currentY += 10;
        if (currentY + 60 > pageHeight - bottomMargin) {
            doc.addPage();
            pageCount++;
            currentY = topMargin;
        }

        doc.setFontSize(12);
        doc.setTextColor(0);
        doc.text(`Answer:`, leftMargin, currentY);
        currentY += 16;

        const answerDiv = document.createElement('div');
        answerDiv.style.width = '600px';
        answerDiv.innerHTML = query.answer || '';
        document.body.appendChild(answerDiv);
        const answerCanvas = await html2canvas(answerDiv, {
            scale: 1,
            logging: false
        });
        document.body.removeChild(answerDiv);

        await addCanvasToPDF(answerCanvas);

        currentY += 15;

        if (currentY + 10 > pageHeight - bottomMargin) {
            doc.addPage();
            pageCount++;
            currentY = topMargin;
        }
        doc.setDrawColor(180);
        doc.setLineWidth(0.5);
        doc.line(leftMargin, currentY, pageWidth - rightMargin, currentY);
        currentY += 20;

        doc.setFontSize(8);
        doc.setTextColor(120);
        doc.text(`Page ${pageCount}`, pageWidth / 2, pageHeight - 10, {align: 'center'});
    }

    const safeFileName = sessionTitle.replace(/[^a-z0-9_\-]/gi, '_');
    doc.save(`${safeFileName}_Chat_History.pdf`);
}


// ==============================
// PDF UPLOAD
// ==============================

function triggerFileSelection() {
    document.getElementById('pdfInput').click();
}


async function addUploadedPDFtoSession(sessionId, pdfnames) {

    try {
        const response = await fetchWithAuth(`/api/query/update_session/${sessionId}`, {
            method: 'PUT',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({uploaded_pdfs: pdfnames}),
        });
        if (!response.ok) throw new Error('Fehler beim Hinzufügen des PDF-Namen zur Sitzung');

        fetchSessionsAndRender(currentSelectedCollectionData.id);
        showMessageModal(`PDF-Datei erfolgreich zur Sitzung hinzugefügt: ${pdfnames}`);

    } catch (error) {
        console.error('Fehler beim Hinzufügen des PDF-Namen zur Sitzung:', error);
        showMessageModal('Fehler beim Hinzufügen des PDF-Namen zur Sitzung');
    }
}


async function uploadPDF() {

    const pdfInput = document.getElementById('pdfInput');
    const pdfUploadbutton = document.getElementById('uploadIconButton');
    const files = pdfInput.files;
    const sessionId = selectedSessionData.id;

    if (!sessionId) {
        showMessageModal('Bitte wählen Sie zuerst eine Sitzung aus.');
        return;
    }


    // Exit if no files selected
    if (!files || files.length === 0) {
        console.warn('No files selected.');
        return;
    }

    // Check if more than 3 files are selected
    if (files.length > 3) {
        showMessageModal('Bitte wählen Sie maximal 3 Dateien aus.');
        return;
    }

    try {
        // Get current session PDF names (string)
        const existingPDFNames = await getAlreadyUploadedPDFs(sessionId) || "";
        const existingPDFNamesArray = existingPDFNames.split(',').map(name => name.trim());


        // Filter out duplicates
        const filesToUpload = Array.from(files).filter(file => {
            if (existingPDFNamesArray.includes(file.name)) {

                showMessageModal(`Die Datei "${file.name}" wurde bereits hochgeladen.`);
                return false;
            }
            return true;
        });

        // Only proceed if we have files to upload
        if (filesToUpload.length === 0) {
            console.warn('No new files to upload.');
            showMessageModal('Keine neuen Dateien zum Hochladen.');
            return;
        }

        // Create FormData and append the new PDFs
        const formData = new FormData();
        filesToUpload.forEach(file => formData.append('files', file));


        // Send the POST request to extract and process PDF content
        const response = await fetch('/api/loader/get_content_from_file', {
            method: 'POST',
            body: formData
        });
        const responseBody = await response.json();

        if (response.status === 400) {
            console.error('Error while uploading file:', responseBody.message);
            showMessageModal('Mindestens eine PDF-Datei hat zu viele Seiten.');
            return;
        } else if (response.status !== 200) {
            console.error('Error while uploading PDFs:', responseBody.message);
            showMessageModal('Fehler beim Hochladen der Datei');
            return;
        }

        // If successful, display the PDF content and update the session
        pdfContent += 'Inhalt der Datei: ' + responseBody.message;
        showInlinePDFNames(existingPDFNames, filesToUpload);

    } catch (error) {
        console.error('Error:', error);
        showMessageModal('Fehler beim Hochladen der Datei');
    }
}


async function showInlinePDFNames(currentFileNames, newFiles) {
    const sessionId = selectedSessionData.id;


    const existingNames = currentFileNames
        ? currentFileNames.split(',').map(name => name.trim())
        : [];

    const newFileNames = Array.from(newFiles).map(file => file.name);
    const combinedNames = [...existingNames, ...newFileNames].join(', ');


    addUploadedPDFtoSession(sessionId, combinedNames);

    showMessageModal(`PDF-Datei erfolgreich zur Sitzung hinzugefügt.Hochgeladene PDFs: ${combinedNames}.`);

}

async function getAlreadyUploadedPDFs(sessionId) {

    try {
        const response = await fetchWithAuth(`/api/query/get_session/${sessionId}`);
        if (!response.ok) {
            throw new Error('Fehler beim Abrufen der bereits hochgeladenen PDF-Namen');
        }
        const data = await response.json();


        return data.uploaded_pdfs;
    } catch (error) {
        console.error('Fehler beim Abrufen der bereits hochgeladenen PDF-Namen:', error);
        showMessageModal('Fehler beim Abrufen der bereits hochgeladenen PDF-Namen.');
    }
}


async function createNewSession() {
    try {
        const collectionId = currentSelectedCollectionData.id;

        const sessionResponse = await fetchWithAuth(`/api/query/create_session/${collectionId}`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({session_goal: ""})
        });


        if (!sessionResponse.ok) {
            throw new Error(`Session creation failed: ${sessionResponse.status}`);
        }

        const sessionData = await sessionResponse.json();
        const sessionId = sessionData.id;

        if (selectedFolder) {
            await fetchWithAuth(`/api/query/move_session_to_folder?folder_name=${encodeURIComponent(selectedFolder)}&session_id=${encodeURIComponent(sessionId)}&collection_id=${encodeURIComponent(collectionId)}`, {
                method: 'POST'
            });
        }

        await fetchSessionsAndRender(collectionId);
        await selectSession(sessionId);
    } catch (error) {
        console.error('Error creating new session:', error);
        showMessageModal(`Fehler beim Erstellen der Sitzung: ${error.message}`);
    }
}


async function createSessionWithoutFolder() {
    const collectionId = currentSelectedCollectionData.id;
    const requestBody = {
        session_goal: "",
    };

    try {
        const sessionResponse = await fetchWithAuth(`/api/query/create_session/${collectionId}`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify(requestBody)
        });

        if (!sessionResponse.ok) {
            throw new Error(`Failed to create a new session. Status: ${sessionResponse.status}`);
        }

        const sessionData = await sessionResponse.json();
        const sessionId = sessionData.id;

        await fetchSessionsAndRender(collectionId);
        await selectSession(sessionId);
    } catch (error) {
        console.error("Error creating session without folder:", error);
        showMessageModal("Error creating new session: " + error.message);
    }
}


function openLogoutModal() {
    document.getElementById("logoutModal").style.display = "flex";
}

function closeLogoutModal() {
    document.getElementById("logoutModal").style.display = "none";
}

function confirmLogout() {
    localStorage.removeItem('access_token');
    window.location.href = "/logout";
}

function handleCopyIcon(event) {
    const aiMessage = event.target.closest(".ai-message-bubble");
    if (!aiMessage) return showMessageModal("KI-Nachricht nicht gefunden.");

    const answerContainer = aiMessage.querySelector(".ai-answer-text");
    if (!answerContainer) return showMessageModal("Kein Text zum Kopieren gefunden.");

    let output = "";
    const children = Array.from(answerContainer.childNodes);
    let listCounter = 1;

    children.forEach(child => {
        if (child.nodeType === Node.ELEMENT_NODE) {
            if (child.tagName === "OL") {
                const items = child.querySelectorAll("li");
                items.forEach(li => {
                    output += `${listCounter}. ${li.innerText.trim()}\n`;
                    listCounter++;
                });
            } else {
                const text = child.innerText?.trim();
                if (text) {
                    output += text + "\n";
                }
            }
        } else if (child.nodeType === Node.TEXT_NODE) {
            const text = child.textContent.trim();
            if (text) {
                output += text + "\n";
            }
        }
    });

    output = output.trim();
    if (!output) return showMessageModal("Die Antwort ist leer.");

    navigator.clipboard.writeText(output)
        .then(() => showMessageModal("Antwort wurde erfolgreich kopiert!"))
        .catch(() => showMessageModal("Kopieren fehlgeschlagen."));
}


function toggleDropdown() {
    const dropdown = document.getElementById("collectionDropdown");
    if (!dropdown) return;
    dropdown.style.display =
        (dropdown.style.display === "block") ? "none" : "block";
}


// ==============================
// EVENT LISTENERS
// ==============================

document.addEventListener("DOMContentLoaded", function () {
    const sidebar = document.querySelector(".sidebar");
    const toggleButton = document.querySelector(".left-side-bar-burger-icon");

    toggleButton.addEventListener("click", function () {
        sidebar.classList.toggle("sidebar-collapsed");

        if (sidebar.classList.contains("sidebar-collapsed")) {
            localStorage.setItem("sidebarState", "collapsed");
        } else {
            localStorage.setItem("sidebarState", "expanded");
        }
    });

    const savedState = localStorage.getItem("sidebarState");
    if (savedState === "collapsed") {
        sidebar.classList.add("sidebar-collapsed");
    }
});

document.addEventListener('DOMContentLoaded', () => {
    const newSessionButton = document.getElementById('addNewSessionButton');

    if (newSessionButton) {
        newSessionButton.addEventListener('click', () => {
            if (selectedFolder) {
                createNewSession();
            } else {
                createSessionWithoutFolder();
            }
        });
    }
});

document.addEventListener("click", function (event) {
    const sendIconWrapper = event.target.closest(".send-icon-wrapper");
    if (sendIconWrapper) {
        sendQueryStreaming();
    }
});

document.addEventListener("click", function (event) {
    if (event.target.closest(".copy-icon")) {
        handleCopyIcon(event);
    }
});

document.addEventListener("click", function (event) {
    const dropdown = document.getElementById("collectionDropdown");
    const selector = document.getElementById("collection-name-selector");

    if (dropdown && dropdown.style.display === "block" && !selector.contains(event.target)) {
        dropdown.style.display = "none";
    }
});

document.getElementById("collection-name-selector").addEventListener("click", function (event) {
    event.stopPropagation();
    toggleDropdown();
});


function attachStarIconListeners() {
    document.querySelectorAll(".star.clickable-icon").forEach(starIcon => {
        starIcon.removeEventListener("click", starIconClickHandler);
        starIcon.addEventListener("click", starIconClickHandler);
    });
}

function starIconClickHandler(event) {
    const queryId = event.target.dataset.queryId;
    if (queryId) {
        handleStarIcon(queryId);
    }
}


function togglePromptSelection(prompt) {
    const idx = selectedPrompts.findIndex(sp => sp.id === prompt.id);
    if (idx === -1) {
        // Clear templates when selecting a prompt
        selectedTemplates.length = 0;
        updateSelectedTemplateDisplay();
        selectedPrompts.push(prompt);
    } else {
        selectedPrompts.splice(idx, 1);
    }
}

function updateSelectedPromptDisplay() {
    const container = document.getElementById('selectedPromptsContainer');
    container.textContent = selectedPrompts.map(p => p.content).join('\n\n'); // each on its own line
}

function renderPromptTable(prompts) {
    const tbody = document.querySelector('#promptTable tbody');
    tbody.innerHTML = '';

    if (!prompts.length) {
        tbody.innerHTML = '<tr><td colspan="5">Keine Treffer</td></tr>';
        return;
    }

    prompts.forEach(p => {
        const isSelected = selectedPrompts.some(sp => sp.id === p.id);
        const tr = document.createElement('tr');
        tr.style.verticalAlign = 'top';
        tr.innerHTML = `
      <td>${p.title}</td>
      <td style="white-space:pre-wrap;max-width:300px;">${p.content}</td>
      <td><button class="prompt-select-btn" data-id="${p.id}">${isSelected ? 'Unselect' : 'Select'}</button></td>
    `;
        tbody.appendChild(tr);
    });

    tbody.querySelectorAll('.prompt-select-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const id = e.target.dataset.id;
            const prompt = prompts.find(pr => pr.id == id);
            togglePromptSelection(prompt);
            renderPromptTable(prompts);  // refresh row buttons
            updateSelectedPromptDisplay();
        });
    });
}

async function loadPrompts(searchTerm) {
    const userEmail = localStorage.getItem('user_email');
    const tbody = document.querySelector('#promptTable tbody');
    tbody.innerHTML = '<tr><td colspan="5">Lade …</td></tr>';

    let url;
    if (searchTerm) {
        console.log("Searching for prompts with term:", searchTerm);
        url = `/api/query/prompts/user/${encodeURIComponent(userEmail)}/search?search_term=${encodeURIComponent(searchTerm)}`;
        console.log("Loading prompts from URL:", url);
    } else {
        console.log("No search term provided, loading all prompts for user:", userEmail);
        url = `/api/query/prompts/user/${encodeURIComponent(userEmail)}`;
        console.log("Loading prompts from URL:", url);
    }


    try {
        const res = await fetchWithAuth(url);
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        const prompts = await res.json();
        renderPromptTable(prompts);
    } catch (err) {
        tbody.innerHTML = `<tr><td colspan="5" style="color:red;">Fehler: ${err.message}</td></tr>`;
    }
}



function resetSessionData() {
    selectedSessionData = {};
    selectedSessionId = null;
    queries = [];
    allSessionsData = {};
    pdfContent = "";
    inlinePdfFiles = null;

    const queriesContainer = document.getElementById('chat-window');
    if (queriesContainer) {
        queriesContainer.innerHTML = '';
    }
}



document.getElementById("collection-name-in-chat").addEventListener("click", function (event) {
    event.stopPropagation();
    showCollectionDropdown();
});

function showCollectionDropdown() {
    const dropdown = document.getElementById("collectionDropdown");
    const selector = document.getElementById("collection-name-selector");

    dropdown.innerHTML = '';

    collectionsData.forEach(collection => {
        const item = document.createElement('div');
        item.textContent = collection.name;
        item.classList.add('collection-dropdown-item');
        item.addEventListener('click', (e) => {
            e.stopPropagation();
            resetSessionData();
            selectCollection(null, collection.id);
            dropdown.style.display = 'none';
        });
        dropdown.appendChild(item);
    });

    dropdown.style.display = 'block';

    const rect = selector.getBoundingClientRect();
    const dropdownHeight = dropdown.offsetHeight;

    dropdown.style.left = `${rect.left + window.scrollX}px`;
    dropdown.style.top = `${rect.top + window.scrollY - dropdownHeight}px`;
    dropdown.style.width = `${rect.width}px`;
}


document.addEventListener('click', function () {
    document.getElementById("collectionDropdown").style.display = 'none';
});



document.addEventListener("DOMContentLoaded", function () {
    const modal = document.getElementById("promptModal");
    const openButton = document.getElementById("openPromptModal");
    const closeButtonIcon = document.getElementById("closePromptModal");
    const closeButton = document.getElementById("closePromptModalButton");
    const deepeningModal = document.getElementById("deepeningModuleModal");
    const openDeepeningButton = document.getElementById("openDeepeningModuleModal");
    const closeDeepeningButtonIcon = document.getElementById("closeDeepeningModuleModal");
    const closeDeepeningButton = document.getElementById("closeDeepeningModuleModalButton");
    const btnOpen = document.getElementById('openPromptServiceModal');
    const btnClose = document.getElementById('closePromptServiceModal');
    const promptServicemodal = document.getElementById('promptServiceModal');
    const inpSearch = document.getElementById('promptSearchInput');
    const btnSearch = document.getElementById('searchPromptsButton');


    if (btnOpen) btnOpen.addEventListener('click', () => {
        promptServicemodal.style.display = 'block';
        loadPrompts('');
    });
    if (btnClose) btnClose.addEventListener('click', () => {
        promptServicemodal.style.display = 'none';
    });
    window.addEventListener('click', evt => {
        if (evt.target === promptServicemodal) promptServicemodal.style.display = 'none';
    });

    /* manual search (button / Enter) */
    btnSearch.addEventListener('click', () => loadPrompts(inpSearch.value.trim()));
    inpSearch.addEventListener('keypress', ev => {
        if (ev.key === 'Enter') btnSearch.click();
    });

    /* live filter (on every keystroke, debounced 300 ms) */
    inpSearch.addEventListener('input', ev => {
        clearTimeout(searchDebounceTimer);
        searchDebounceTimer = setTimeout(() => loadPrompts(ev.target.value.trim()), 300);
    });

    // Template search event listeners
    const templateSearchButton = document.getElementById('searchTemplatesButton');
    const templateSearchInput = document.getElementById('templateSearchInput');
    const templateUseCaseSelect = document.getElementById('templateUseCaseSelect');

    if (templateSearchButton) {
        templateSearchButton.addEventListener('click', () => {
            const searchTerm = templateSearchInput ? templateSearchInput.value.trim() : '';
            const useCase = templateUseCaseSelect ? templateUseCaseSelect.value : '';
            loadTemplates(searchTerm, useCase);
        });
    }

    if (templateSearchInput) {
        templateSearchInput.addEventListener('keypress', ev => {
            if (ev.key === 'Enter') {
                const searchTerm = templateSearchInput.value.trim();
                const useCase = templateUseCaseSelect ? templateUseCaseSelect.value : '';
                loadTemplates(searchTerm, useCase);
            }
        });
    }

    if (templateUseCaseSelect) {
        templateUseCaseSelect.addEventListener('change', () => {
            const searchTerm = templateSearchInput ? templateSearchInput.value.trim() : '';
            const useCase = templateUseCaseSelect.value;
            loadTemplates(searchTerm, useCase);
        });
    }

    if (openButton) {
        openButton.addEventListener("click", function () {
            modal.style.display = "block";
        });
    }

    if (closeButtonIcon) {
        closeButtonIcon.addEventListener("click", function () {
            modal.style.display = "none";
        });
    }

    if (closeButton) {
        closeButton.addEventListener("click", function () {
            modal.style.display = "none";
        });
    }

    window.addEventListener("click", function (event) {
        if (event.target === modal) {
            modal.style.display = "none";
        }
    });


    document.querySelectorAll('.copy-button').forEach(button => {
        button.addEventListener('click', function () {
            const text = this.getAttribute('data-text');
            navigator.clipboard.writeText(text).then(() => {
                this.textContent = "Kopiert!";
                setTimeout(() => this.textContent = "Copy", 1500);
            });
        });
    });


    if (openDeepeningButton) {
        openDeepeningButton.addEventListener("click", function () {
            deepeningModal.style.display = "block";
        });
    }
    if (closeDeepeningButtonIcon) {
        closeDeepeningButtonIcon.addEventListener("click", function () {
            deepeningModal.style.display = "none";
        });
    }
    if (closeDeepeningButton) {
        closeDeepeningButton.addEventListener("click", function () {
            deepeningModal.style.display = "none";
        });
    }
    window.addEventListener("click", function (event) {
        if (event.target === deepeningModal) {
            deepeningModal.style.display = "none";
        }
    });

});

// Slash Command Handling
let slashCommandSuggestions = [];
let currentCommandIndex = -1;

function isSlashCommandsEnabled() {
    // Check if slash commands are enabled for the current collection
    if (currentSelectedCollectionData && currentSelectedCollectionData.custom_config) {
        // Default to true if not specified (backward compatibility)
        const enabled = currentSelectedCollectionData.custom_config.is_slash_commands_on !== false;
        console.log('Slash commands enabled for collection:', currentSelectedCollectionData.name, ':', enabled);
        return enabled;
    }
    console.log('No collection selected or no custom config, defaulting to enabled');
    return true; // Default to enabled if no collection is selected
}

async function loadSlashCommands() {
    try {
        const response = await fetchWithAuth('/slash-commands/list', {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const data = await response.json();
        slashCommandSuggestions = data.commands;
    } catch (error) {
        console.error('Failed to load slash commands:', error);
    }
}

function showSlashCommandSuggestions(input) {
    const suggestionsContainer = document.getElementById('slashCommandSuggestions');
    if (!suggestionsContainer) return;

    // Show all commands if only / is entered
    const query = input.length === 1 ? '' : input.slice(1).toLowerCase();
    const filteredCommands = slashCommandSuggestions.filter(cmd =>
        cmd.name.toLowerCase().includes(query)
    );

    if (filteredCommands.length === 0) {
        suggestionsContainer.style.display = 'none';
        return;
    }

    suggestionsContainer.innerHTML = filteredCommands.map(cmd => `
        <div class="slash-command-suggestion" data-command="${cmd.name}">
            <span class="command">/${cmd.name}</span>
            <span class="description">${cmd.description}</span>
        </div>
    `).join('');

    suggestionsContainer.style.display = 'block';
    currentCommandIndex = -1;
}

async function executeSlashCommand(command) {
    try {
        // Check if this is a websearch command
        if (command.startsWith('/websearch ')) {
            let query = command.substring('/websearch '.length).trim();
            if (!query) {
                showMessageModal('Please provide a search query for /websearch command');
                return;
            }

            // Sanitize input by removing < and > characters
            query = query.replace(/[<>]/g, '');

            // Add the command to the chat as a user message
            const queriesContainer = document.getElementById('chat-window');

            // User message
            const userMessageContainer = document.createElement('div');
            userMessageContainer.classList.add('user-message-container');
            const userBubble = document.createElement('div');
            userBubble.classList.add('user-message-bubble');
            userBubble.innerHTML = `<p class="query-text-style">${safeText(command)}</p>`;
            userMessageContainer.appendChild(userBubble);
            queriesContainer.appendChild(userMessageContainer);

            // Create AI message container
            const aiMessageId = `ai-message-${Date.now()}`;
            const aiMessage = document.createElement('div');
            aiMessage.classList.add('ai-message-bubble');
            aiMessage.id = aiMessageId;

            aiMessage.innerHTML = `
                <div class="chat-wrapper">
                    <div>
                        <svg class="svg-icon-query-answer" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 22" fill="currentColor">
                            <path d="M18.1104 10.7774C19.9436 12.6106 22.9991 12.9162 22.9991 12.9162C22.9991 12.9162 19.7944 13.4023 18.1104 14.7494C16.4265 16.0965 16.2772 19.9437 16.2772 19.9437C16.2772 19.9437 15.9716 16.5827 14.1383 14.7494C12.3051 12.9162 9.55518 12.9162 9.55518 12.9162C9.55518 12.9162 12.1828 12.4884 14.1383 10.7774C16.0938 9.06632 16.2772 5.88867 16.2772 5.88867C16.2772 5.88867 16.2732 8.94 18.1104 10.7774Z"/>
                            <path d="M8.72166 3.76317C9.72158 4.79936 11.3882 4.97206 11.3882 4.97206C11.3882 4.97206 9.64017 5.24685 8.72166 6.00825C7.80316 6.76965 7.72171 8.94412 7.72171 8.94412C7.72171 8.94412 7.55502 7.04444 6.55506 6.00825C5.55511 4.97206 4.05518 4.97206 4.05518 4.97206C4.05518 4.97206 5.48844 4.73028 6.55506 3.76317C7.62168 2.79606 7.72171 1 7.72171 1C7.72171 1 7.71951 2.72466 8.72166 3.76317Z"/>
                            <path d="M5.66649 15.9849C6.66641 17.021 8.33303 17.1937 8.33303 17.1937C8.33303 17.1937 6.585 17.4685 5.66649 18.2299C4.74798 18.9913 4.66654 21.1658 4.66654 21.1658C4.66654 21.1658 4.49984 19.2661 3.49989 18.2299C2.49993 17.1937 1 17.1937 1 17.1937C1 17.1937 2.43327 16.952 3.49989 15.9849C4.5665 15.0177 4.66654 13.2217 4.66654 13.2217C4.66654 13.2217 4.66433 14.9463 5.66649 15.9849Z"/>
                        </svg>
                    </div>
                    <div>
                        <p class="query-text-style" style="white-space: break-spaces;" id="${aiMessageId}-text"></p>
                        <br>
                        <span class="change" style="display: none;"></span>
                        <p class="query-text-style" id="${aiMessageId}-timestamp" style="display:none;"></p>
                        <div class="loading-container" id="${aiMessageId}-loading">
                            <div class="loading-bar"></div>
                            <div class="loading-bar"></div>
                            <div class="loading-bar"></div>
                            <br>
                            <p class="loading-text">generating web search analysis ...</p>
                        </div>
                        <div id="${aiMessageId}-sources" style="display:none; margin-top: 10px;">
                            <p>📚 Sources:</p>
                            <div id="${aiMessageId}-sources-list"></div>
                        </div>
                    </div>
                </div>
            `;
            queriesContainer.appendChild(aiMessage);
            scrollToBottom('chat-window');

            // Send the websearch query through the existing query infrastructure
            const collectionId = currentSelectedCollectionData.id;
            const payload = {
                content: `Please perform a web search for: ${query} and provide a comprehensive analysis based on the search results.`,
                inline_pdf_content: "",
                custom_prompt: "You are a helpful AI assistant that performs web searches and provides comprehensive analysis. Please search the web for the user's query and provide a detailed, well-structured response based on the search results. Include relevant information, context, and cite your sources."
            };

            if (selectedSessionData && selectedSessionData.id) {
                payload.session_id = selectedSessionData.id;
            }

            try {
                const response = await fetchWithAuth(`/api/query/collection/${collectionId}/query_stream`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(payload),
                });

                if (!response.ok) {
                    throw new Error(`Server responded with status ${response.status}`);
                }

                if (!response.body) {
                    throw new Error('No readable stream from server');
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let partialChunk = '';
                const aiTextElement = document.getElementById(`${aiMessageId}-text`);
                const sourcesContainer = document.getElementById(`${aiMessageId}-sources`);
                const sourcesList = document.getElementById(`${aiMessageId}-sources-list`);
                let firstChunkReceived = false;
                let webSources = [];

                while (true) {
                    const {done, value} = await reader.read();
                    if (done) break;

                    partialChunk += decoder.decode(value, {stream: true});

                    try {
                        while (true) {
                            const sepIndex = partialChunk.indexOf('}{');
                            if (sepIndex === -1) break;

                            const jsonStr = partialChunk.slice(0, sepIndex + 1);
                            partialChunk = partialChunk.slice(sepIndex + 1);

                            const parsed = JSON.parse(jsonStr);
                            if (parsed.query_id) lastQueryId = parsed.query_id;
                            if (selectedSessionId !== parsed.session_id) {
                                selectedSessionId = parsed.session_id;
                            }

                            // Remove spinner on first chunk:
                            if (!firstChunkReceived) {
                                const loadingContainer = document.getElementById(`${aiMessageId}-loading`);
                                if (loadingContainer) loadingContainer.remove();
                                firstChunkReceived = true;
                            }

                            const chunkContent = parsed.content || '';
                            const safeContent = safeText(chunkContent);
                            aiTextElement.innerHTML += safeContent;
                            scrollToBottom('chat-window');

                            // Extract web sources from retrieved_docs if available
                            if (parsed.retrieved_docs && parsed.is_web_searched) {
                                webSources = parsed.retrieved_docs.filter(doc =>
                                    doc.metadata && doc.metadata.source && doc.metadata.source !== "No URL available"
                                );
                            }
                        }
                    } catch {
                        // Keep waiting for next chunk if partial parse fails.
                    }
                }

                if (partialChunk.trim()) {
                    try {
                        const lastObj = JSON.parse(partialChunk);
                        if (lastObj.query_id) lastQueryId = lastObj.query_id;
                        if (!firstChunkReceived) {
                            const loadingContainer = document.getElementById(`${aiMessageId}-loading`);
                            if (loadingContainer) loadingContainer.remove();
                            firstChunkReceived = true;
                        }
                        const chunkContent = lastObj.content || '';
                        const safeContent = safeText(chunkContent);
                        aiTextElement.innerHTML += safeContent;

                        // Extract web sources from the last chunk as well
                        if (lastObj.retrieved_docs && lastObj.is_web_searched) {
                            webSources = lastObj.retrieved_docs.filter(doc =>
                                doc.metadata && doc.metadata.source && doc.metadata.source !== "No URL available"
                            );
                        }
                    } catch {
                        // ignore leftover parse errors
                    }
                }

                // Display sources if we have any
                if (webSources.length > 0) {
                    sourcesContainer.style.display = 'block';

                    // Remove duplicates based on URL
                    const uniqueSources = webSources.filter((doc, index, self) =>
                        index === self.findIndex(d => d.metadata?.source === doc.metadata?.source)
                    );

                    const sourcesText = uniqueSources.map((doc, index) => {
                        const title = doc.metadata?.title || `Source ${index + 1}`;
                        const url = doc.metadata?.source || '';
                        return `${index + 1}. ${title}\n   ${url}`;
                    }).join('\n\n');
                    sourcesList.textContent = sourcesText;
                }

                if (selectedSessionData.id !== null) {
                    closeSessionLoadingModal();
                    selectSession(selectedSessionId);
                    selectedSessionId = null;
                }
                await fetchSessionQueries(selectedSessionData.id);

                if (lastQueryId) {
                    const scrollTargetId = `query-${lastQueryId}`;
                    setTimeout(async () => {
                        const targetEl = await waitForElementById(scrollTargetId, 10, 50);
                        if (targetEl) {
                            targetEl.scrollIntoView({behavior: 'smooth', block: 'start'});
                        } else {
                            scrollToBottom('chat-window');
                        }
                    }, 100);
                }

            } catch (err) {
                console.error('[WebSearch] Error:', err);
                const aiTextElement = document.getElementById(`${aiMessageId}-text`);
                if (aiTextElement) {
                    aiTextElement.textContent = 'Error performing web search. Please try again.';
                }
                const loadingContainer = document.getElementById(`${aiMessageId}-loading`);
                if (loadingContainer) loadingContainer.remove();
            }

            return;
        }

        // Handle other slash commands through the existing API
        const response = await fetchWithAuth('/slash-commands/execute', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ command })
        });

        const result = await response.json();

        // Handle the command result
        if (result.success) {
            // Add the command to the chat as a user message
            const queriesContainer = document.getElementById('chat-window');

            // User message
            const userMessageContainer = document.createElement('div');
            userMessageContainer.classList.add('user-message-container');
            const userBubble = document.createElement('div');
            userBubble.classList.add('user-message-bubble');
            userBubble.innerHTML = `<p class="query-text-style">${command}</p>`;
            userMessageContainer.appendChild(userBubble);
            queriesContainer.appendChild(userMessageContainer);

            // AI response
            const aiMessage = document.createElement('div');
            aiMessage.classList.add('ai-message-bubble');
            aiMessage.innerHTML = `
                <div class="chat-wrapper">
                    <div>
                        <svg class="svg-icon-query-answer" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 22" fill="currentColor">
                            <path d="M18.1104 10.7774C19.9436 12.6106 22.9991 12.9162 22.9991 12.9162C22.9991 12.9162 19.7944 13.4023 18.1104 14.7494C16.4265 16.0965 16.2772 19.9437 16.2772 19.9437C16.2772 19.9437 15.9716 16.5827 14.1383 14.7494C12.3051 12.9162 9.55518 12.9162 9.55518 12.9162C9.55518 12.9162 12.1828 12.4884 14.1383 10.7774C16.0938 9.06632 16.2772 5.88867 16.2772 5.88867C16.2772 5.88867 16.2732 8.94 18.1104 10.7774Z"/>
                            <path d="M8.72166 3.76317C9.72158 4.79936 11.3882 4.97206 11.3882 4.97206C11.3882 4.97206 9.64017 5.24685 8.72166 6.00825C7.80316 6.76965 7.72171 8.94412 7.72171 8.94412C7.72171 8.94412 7.55502 7.04444 6.55506 6.00825C5.55511 4.97206 4.05518 4.97206 4.05518 4.97206C4.05518 4.97206 5.48844 4.73028 6.55506 3.76317C7.62168 2.79606 7.72171 1 7.72171 1C7.72171 1 7.71951 2.72466 8.72166 3.76317Z"/>
                            <path d="M5.66649 15.9849C6.66641 17.021 8.33303 17.1937 8.33303 17.1937C8.33303 17.1937 6.585 17.4685 5.66649 18.2299C4.74798 18.9913 4.66654 21.1658 4.66654 21.1658C4.66654 21.1658 4.49984 19.2661 3.49989 18.2299C2.49993 17.1937 1 17.1937 1 17.1937C1 17.1937 2.43327 16.952 3.49989 15.9849C4.5665 15.0177 4.66654 13.2217 4.66654 13.2217C4.66654 13.2217 4.66433 14.9463 5.66649 15.9849Z"/>
                        </svg>
                    </div>
                    <div>
                        <p class="query-text-style" style="white-space: break-spaces;">${result.result.message}</p>
                    </div>
                </div>
            `;
            queriesContainer.appendChild(aiMessage);
            scrollToBottom('chat-window');

            // Handle specific commands
            if (command === '/clear') {
                // Clear the chat window
                queriesContainer.innerHTML = '';
            } else if (result.result.action === 'download_chat') {
                // Handle download command
                downloadChatHistory();
            }
        } else {
            showMessageModal('Command execution failed');
        }
    } catch (error) {
        console.error('Failed to execute command:', error);
        showMessageModal('Failed to execute command: ' + error.message);
    }
}

// Modify the existing input handling
document.getElementById('questionInput').addEventListener('input', function(e) {
    const input = e.target.value;
    if (input.startsWith('/') && isSlashCommandsEnabled()) {
        showSlashCommandSuggestions(input);
    } else {
        document.getElementById('slashCommandSuggestions').style.display = 'none';
    }
});

document.getElementById('questionInput').addEventListener('keydown', function (e) {
    const suggestionsContainer = document.getElementById('slashCommandSuggestions');
    const suggestions = suggestionsContainer.getElementsByClassName('slash-command-suggestion');

    // --- Enter Key Press Logic ---
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault(); // Stop the default browser action
        const inputValue = this.value.trim();

        // If the input starts with '/', handle as a slash command (only if enabled)
        if (inputValue.startsWith('/')) {
            if (isSlashCommandsEnabled()) {
                // If a suggestion is highlighted, use that one.
                if (suggestionsContainer.style.display === 'block' && currentCommandIndex >= 0 && suggestions[currentCommandIndex]) {
                     const selectedCommand = suggestions[currentCommandIndex].dataset.command;
                     executeSlashCommand(`/${selectedCommand}`);
                } else {
                    // Otherwise, use what's typed in the box.
                    executeSlashCommand(inputValue);
                }
            } else {
                // Show message that slash commands are disabled
                showMessageModal('Slash commands are disabled for this collection.');
            }
        }
        // Otherwise, handle as a regular query to the AI.
        else if (inputValue) {
            sendQueryStreaming();
        }

        // Cleanup the input area after any "Enter" action.
        this.value = '';
        suggestionsContainer.style.display = 'none';
        currentCommandIndex = -1;
        return;
    }

    // --- Suggestion Box Navigation Logic (Arrow keys, Escape) ---
    if (suggestionsContainer.style.display === 'block' && isSlashCommandsEnabled()) {
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                currentCommandIndex = Math.min(currentCommandIndex + 1, suggestions.length - 1);
                updateSuggestionSelection();
                break;
            case 'ArrowUp':
                e.preventDefault();
                currentCommandIndex = Math.max(currentCommandIndex - 1, 0);
                updateSuggestionSelection();
                break;
            case 'Escape':
                e.preventDefault();
                suggestionsContainer.style.display = 'none';
                break;
        }
    }
});

function updateSuggestionSelection() {
    const suggestionsContainer = document.getElementById('slashCommandSuggestions');
    const suggestions = suggestionsContainer.getElementsByClassName('slash-command-suggestion');

    if (currentCommandIndex >= 0 && currentCommandIndex < suggestions.length) {
        for (let i = 0; i < suggestions.length; i++) {
            suggestions[i].classList.remove('selected');
        }
        suggestions[currentCommandIndex].classList.add('selected');
    }
}

// Template management variables
let selectedTemplates = [];
let templates = [];

// Tab switching function
function switchTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });

    // Show selected tab content
    document.getElementById(tabName + 'Content').classList.add('active');

    // Add active class to selected tab button
    document.getElementById(tabName + 'Tab').classList.add('active');

    // Load data for the selected tab
    if (tabName === 'prompts') {
        loadPrompts('');
    } else if (tabName === 'templates') {
        loadTemplates();
    }
}

// Template functions
async function loadTemplates(searchTerm = '', useCase = '') {
    const tbody = document.querySelector('#templateTable tbody');
    if (tbody) {
        tbody.innerHTML = '<tr><td colspan="4">Loading...</td></tr>';
    }

    let url = '/api/query/templates';
    const params = new URLSearchParams();
    if (searchTerm) params.append('search_term', searchTerm);
    if (useCase) params.append('use_case', useCase);
    if (params.toString()) url += '?' + params.toString();

    try {
        const res = await fetchWithAuth(url);
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        const data = await res.json();
        templates = data.data || [];
        renderTemplateTable(templates);
    } catch (err) {
        console.error('Error loading templates:', err);
        if (tbody) {
            tbody.innerHTML = `<tr><td colspan="4" style="color:red;">Error: ${err.message}</td></tr>`;
        }
    }
}

function renderTemplateTable(templates) {
    const tbody = document.querySelector('#templateTable tbody');
    if (!tbody) return;

    tbody.innerHTML = '';

    if (!templates.length) {
        tbody.innerHTML = '<tr><td colspan="4">No templates found</td></tr>';
        return;
    }

    templates.forEach(template => {
        const isSelected = selectedTemplates.some(st => st.id === template.id);
        const tr = document.createElement('tr');
        tr.style.verticalAlign = 'top';

        const placeholdersText = template.placeholders && template.placeholders.length > 0
            ? template.placeholders.map(p => `{${p.placeholder_type}}`).join(', ')
            : 'No placeholders';

        const canSelect = template.rendered_content || template.placeholders.length === 0;
        const selectButtonText = isSelected ? 'Unselect' : (canSelect ? 'Select' : 'Render First');
        const selectButtonDisabled = !canSelect && !isSelected;

        tr.innerHTML = `
            <td>${template.title}</td>
            <td style="white-space:pre-wrap;max-width:300px;">${template.template_content}</td>
            <td>${placeholdersText}</td>
            <td>
                <button class="template-select-btn" data-id="${template.id}" ${selectButtonDisabled ? 'disabled' : ''} style="${selectButtonDisabled ? 'opacity: 0.5; cursor: not-allowed;' : ''}">${selectButtonText}</button>
                <button class="template-render-btn" data-id="${template.id}">Render</button>
            </td>
        `;
        tbody.appendChild(tr);
    });

    // Add event listeners
    tbody.querySelectorAll('.template-select-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const id = e.target.dataset.id;
            const template = templates.find(t => t.id === id);
            toggleTemplateSelection(template);
            renderTemplateTable(templates);
            updateSelectedTemplateDisplay();
        });
    });

    tbody.querySelectorAll('.template-render-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const id = e.target.dataset.id;
            const template = templates.find(t => t.id === id);
            openTemplateRenderModal(template);
        });
    });
}

function toggleTemplateSelection(template) {
    const idx = selectedTemplates.findIndex(st => st.id === template.id);
    if (idx === -1) {
        // Check if this template has been rendered before
        if (!template.rendered_content) {
            alert('Please render this template first before selecting it.');
            return;
        }

        // Clear prompts when selecting a template
        selectedPrompts.length = 0;
        updateSelectedPromptDisplay();

        // Use the rendered content
        selectedTemplates.push({
            ...template,
            template_content: template.rendered_content
        });
    } else {
        selectedTemplates.splice(idx, 1);
    }
}

function updateSelectedTemplateDisplay() {
    const container = document.getElementById('selectedTemplatesContainer');
    if (container) {
        const displayText = selectedTemplates.map(t => {
            // Use rendered content if available, otherwise use template content
            return t.rendered_content || t.template_content;
        }).join('\n\n');
        container.textContent = displayText;
    }
}

function useRenderedTemplate(templateId, renderedContent) {
    // If not already selected, add to selectedTemplates
    let templateIndex = selectedTemplates.findIndex(t => t.id === templateId);
    if (templateIndex === -1) {
        // Find the template in the templates list
        const template = templates.find(t => t.id === templateId);
        if (template) {
            // Clear prompts when selecting a template
            selectedPrompts.length = 0;
            updateSelectedPromptDisplay();
            selectedTemplates.push({
                ...template,
                rendered_content: renderedContent,
                template_content: renderedContent
            });
        }
    } else {
        selectedTemplates[templateIndex].rendered_content = renderedContent;
        selectedTemplates[templateIndex].template_content = renderedContent;
    }
    renderTemplateTable(templates);
    updateSelectedTemplateDisplay();
    document.getElementById('templateRenderModal').style.display = 'none';
}

function openTemplateRenderModal(template) {
    const modal = document.getElementById('templateRenderModal');
    if (!modal) return;

    // Clear previous content
    const content = modal.querySelector('.template-render-content');
    content.innerHTML = '';

    // Add template info
    content.innerHTML = `
        <h3>${template.title}</h3>
        <p><strong>Description:</strong> ${template.description || 'No description'}</p>
        <p><strong>Template:</strong> ${template.template_content}</p>
        <hr>
        <h4>Fill in placeholders:</h4>
    `;

    // Add placeholder inputs
    const placeholdersContainer = document.createElement('div');
    placeholdersContainer.id = 'placeholdersContainer';

    if (template.placeholders && template.placeholders.length > 0) {
        template.placeholders.forEach(placeholder => {
            const placeholderDiv = document.createElement('div');
            placeholderDiv.style.marginBottom = '10px';

            const label = document.createElement('label');
            label.textContent = `${placeholder.placeholder_type}${placeholder.is_required ? ' *' : ''}:`;
            label.style.display = 'block';
            label.style.fontWeight = 'bold';

            const input = document.createElement('input');
            input.type = 'text';
            input.id = `placeholder_${placeholder.placeholder_type}`;
            input.placeholder = placeholder.description;
            input.value = placeholder.default_value || '';
            input.style.width = '100%';
            input.style.padding = '8px';
            input.style.marginTop = '4px';
            input.style.border = '1px solid #ccc';
            input.style.borderRadius = '4px';

            if (placeholder.is_required) {
                input.required = true;
            }

            placeholderDiv.appendChild(label);
            placeholderDiv.appendChild(input);
            placeholdersContainer.appendChild(placeholderDiv);
        });
    } else {
        placeholdersContainer.innerHTML = '<p>This template has no placeholders.</p>';
    }

    content.appendChild(placeholdersContainer);

    // Add render button
    const renderButton = document.createElement('button');
    renderButton.textContent = 'Render Template';
    renderButton.className = 'button-primary';
    renderButton.style.marginTop = '15px';
    renderButton.onclick = () => renderTemplate(template);

    content.appendChild(renderButton);

    // Show modal
    modal.style.display = 'block';
}

function renderTemplate(template) {
    const placeholdersContainer = document.getElementById('placeholdersContainer');
    if (!placeholdersContainer) return;

    // Collect placeholder values
    const placeholderValues = {};
    let hasRequiredValues = true;

    if (template.placeholders && template.placeholders.length > 0) {
        template.placeholders.forEach(placeholder => {
            const input = document.getElementById(`placeholder_${placeholder.placeholder_type}`);
            if (input) {
                const value = input.value.trim();
                if (value) {
                    placeholderValues[placeholder.placeholder_type] = value;
                } else if (placeholder.is_required) {
                    hasRequiredValues = false;
                    input.style.borderColor = 'red';
                }
            }
        });
    }

    if (!hasRequiredValues) {
        alert('Please fill in all required placeholders.');
        return;
    }

    // Render template locally by replacing placeholders (case-insensitive)
    let renderedContent = template.template_content;
    if (template.placeholders && template.placeholders.length > 0) {
        template.placeholders.forEach(placeholder => {
            // Replace all case variants of the placeholder using case-insensitive regex
            const pattern = new RegExp(`\\{${placeholder.placeholder_type}\\}`, 'gi');
            const value = placeholderValues[placeholder.placeholder_type] || placeholder.default_value || '';
            renderedContent = renderedContent.replace(pattern, value);
        });
    }

    // Update the template in the global templates array
    const idx = templates.findIndex(t => t.id === template.id);
    if (idx !== -1) {
        templates[idx].rendered_content = renderedContent;
    }

    // Display the rendered template
    const content = document.querySelector('.template-render-content');
    content.innerHTML = `
        <h3>Rendered Template</h3>
        <div style="background:#f7f7f7;padding:1rem;border-radius:6px;white-space:pre-wrap;">
            ${renderedContent}
        </div>
        <button onclick="copyToClipboard('${renderedContent.replace(/'/g, "\\'")}')" style="margin-top:1rem;">
            Copy to Clipboard
        </button>
        <button onclick="useRenderedTemplate('${template.id}', '${renderedContent.replace(/'/g, "\\'")}')" style="margin-top:1rem; margin-left:1rem; background-color:#007bff; color:white; border:none; padding:8px 16px; border-radius:4px; cursor:pointer;">
            Use in Query
        </button>
    `;
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        alert('Copied to clipboard!');
    }).catch(err => {
        console.error('Failed to copy: ', err);
        alert('Failed to copy to clipboard');
    });
}
