/* --- COLLECTION PART --- */

ul {
    list-style-type: none;
    padding-left: 0;
    margin-left: 0;
}


p {
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
}

.rel-doc-div {
    display: inline-flex;
    flex-direction: row;
}

.source-number {
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
    line-height: 20px;
    margin-left: 5px;
}

/* Container for the loading bars */
.loading-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
}

/* Individual loading bars */
.loading-bar {
    width: 100%;
    height: 14px;
    border-radius: 40px;
    background-color: #f2f2f2; /* solid color for the loading bar */
    position: relative;
    overflow: hidden;
}

/* Adding the gradient animation to loading bars */
.loading-bar:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #FCFCFC, #E0E0E0, #FCFCFC);
    animation: loading 1.5s infinite;
}

/* Different delays for each loading bar */
.loading-bar:nth-child(1) {
    width: 874px;
    animation-delay: 0s;
}

.loading-bar:nth-child(2) {
    width: 617px;
    animation-delay: 0.2s;
    margin-top: 5px;
}

.loading-bar:nth-child(3) {
    width: 798px;
    animation-delay: 0.4s;
    margin-top: 5px;
}

/* Keyframes animation for the loading bars */
@keyframes loading {
    0% {
        left: -100%;
    }
    50% {
        left: 100%;
    }
    100% {
        left: -100%;
    }
}

/* Loading text below the bars */
.loading-text {
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
    color: #777;
    margin-top: 5px;
}


.modal-button {
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
}


.folder-sessions {
    padding-left: 2px;
}

.adjust-dropdown {
    background-color: #fff;
    border-radius: 8px;
    padding: 8px;
    z-index: 9999;
    border: 1px solid rgba(0, 0, 0, 0.15);
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15),
    0px 10px 30px rgba(0, 0, 0, 0.25),
    0px 20px 50px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(8px);
    width: max-content;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #888 #222;
}

.adjust-dropdown-item {
    padding: 8px;
    cursor: pointer;
    position: relative;
    display: block;
    width: 95%;
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
}

.adjust-dropdown-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
}


.input-section {
    position: relative;

    min-width: 589px;
    width: 672px;
    max-width: 740px;
    height: 60px;
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 22.5px; /* 160.714% */
    color: black;
    font-feature-settings: 'liga' off, 'clig' off;
    outline: none;
    cursor: text;
    font-feature-settings: 'liga' off, 'clig' off;
    border: hidden;
    margin: 0px 15px 1px 5px;
    resize: none;

}

.label-chat-goal, .verhandlungsziel {
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
}

.modal-input::placeholder {
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
}

.input-section:empty:before {
    content: attr(placeholder);
    color: #aaa;
}

.input-section::placeholder {
    line-height: 64px;
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
}

.logout-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: none;
    z-index: 9999;
}


.logout-modal .div {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 442px;
    margin: 100px auto;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    position: relative;
    width: 350px;
    height: 280px;
    top: 300px;
}


.logout-icon-wrapper {
    background-color: #000;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}


.logout-icon-wrapper img {
    width: 24px;
    height: 50px;
}


.heading-wrapper .heading {
    font-weight: 600;
    color: #000;
    margin-left: 115px;
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
}

.logout-text-wrapper .logout-text {
    color: #333;
    text-align: center;
    margin-top: 8px;
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
}

.logout-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding: 20px 0 10px;
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
}

.logout-cancel-wrapper {
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
}

.logout-cancel {
    color: #000;
    font-size: var(--font-size, 14px);
    font-family: var(--font-family, Helvetica);
}

.logout-yes-wrapper {
    background-color: #000;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
}

.logout-yes {
    color: #fff;
    font-size: var(--font-size, 14px);
    font-family: var(--font-family, Helvetica);
}

.logout-cancel-wrapper:hover {
    background-color: #f2f2f2;
}

.logout-yes-wrapper:hover {
    background-color: #333;
}


.COLLECTIONS {
    width: 100vw;
    height: 100vh;
    background-color: #000000;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.COLLECTIONS .cone,
.COLLECTIONS .img,
.COLLECTIONS .cone-wrapper,
.COLLECTIONS .cone-2 {
    position: absolute;
    object-fit: contain;
}

.COLLECTIONS .cone {
    width: 100vw;
    height: 90vh;
    top: 15%;
    left: -13%;
    z-index: 1;
}

.COLLECTIONS .img {
    width: 95vw;
    height: 80vh;
    right: 20%;
    bottom: 5%;
    z-index: 2;
}

.COLLECTIONS .cone-wrapper {
    width: 47vw;
    height: 47vh;
    top: -18%;
    right: -18%;
}

.COLLECTIONS .cone-2 {
    width: 80vw;
    height: 80vh;
    bottom: -80%;
    left: -60%;
}

.COLLECTIONS .setting-item {
    position: absolute;
    top: 3%;
    right: 3%;
    display: flex;
    align-items: center;
    gap: 0.8vw;
    padding: 0.5vh;
    z-index: 999;
}

.COLLECTIONS .help-circle {
    width: 1.4vw;
    min-width: 20px;
    height: auto;
}

.COLLECTIONS .help-documentation {
    font-family: var(--font-family, Helvetica);
    color: #fff;
    font-size: var(--font-size, 14px);
    white-space: nowrap;
    cursor: help;
}

.COLLECTIONS .collection-items {
    display: grid;
    grid-template-columns: repeat(3, 312px);
    gap: 2rem;
    max-height: 80vh;
    overflow-y: auto;
    overflow-x: hidden;
    margin: 0 auto;
    padding: 2vh;
    z-index: 500;
}

.COLLECTIONS .div {
    width: 260px;
    height: 314px;
    overflow-y: auto;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%), rgba(255, 255, 255, 0.60);
    backdrop-filter: blur(2px);
    border-radius: 40px;
    border: 2px solid #FFF;
    padding: 2.5vh;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.COLLECTIONS .div:hover {
    transform: translateY(-5px);
}

.COLLECTIONS .text-wrapper {
    font-family: var(--font-family, Helvetica);
    color: #000;
    text-align: center;
    width: 100%;
    margin: 2vh 0;
    font-size: var(--font-size, 14px);
}

.COLLECTIONS .templates,
.COLLECTIONS .certainly-i-d-be {
    margin: 0;
    text-align: center;
    white-space: normal;
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    line-height: 1.4;
}

.COLLECTIONS .templates {
    font-family: var(--font-family, Helvetica);
    font-weight: 700;
    font-size: 24px;
    line-height: 27px;
    margin-top: 28%;
}


.COLLECTIONS .certainly-i-d-be {
    font-family: var(--font-family, Helvetica);
    font-weight: 400;
    font-size: var(--font-size, 14px);
    line-height: 24px;
    padding: 0 1rem;
}

.collection-dropdown {
    position: absolute;
    background-color: #fff;
    border-radius: 8px;
    padding: 8px;
    z-index: 9999;
    border: 1px solid rgba(0, 0, 0, 0.15);
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15),
    0px 10px 30px rgba(0, 0, 0, 0.25),
    0px 20px 50px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(8px);

    max-height: 175px;
    overflow-y: auto;
}


.collection-dropdown-item {
    font-family: var(--font-family, Helvetica);
    font-weight: 400;
    color: black;
    font-size: var(--font-size, 14px);
    line-height: 24px;
    white-space: nowrap;
    padding: 10px;
    cursor: pointer;
    width: 90%;
}


@media (max-width: 768px) {
    .COLLECTIONS {
        overflow-y: auto;
        padding: 20px 0;
    }

    .COLLECTIONS .cone,
    .COLLECTIONS .cone-wrapper {
        display: none;
    }

    .COLLECTIONS .img {
        position: relative;
        width: 80%;
        height: auto;
        left: 0;
        top: 0;
        margin-bottom: 20px;
        right: auto;
        bottom: auto;
    }

    .COLLECTIONS .collection-items {
        grid-template-columns: 1fr;
        width: 95%;
    }

    .COLLECTIONS .div {
        border-radius: 40px;
        border: 2px solid #FFF;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%), rgba(255, 255, 255, 0.60);
        backdrop-filter: blur(2px);
    }

    .COLLECTIONS .setting-item {
        position: fixed;
        top: 10px;
        right: 10px;
        background: rgba(255, 255, 255, 0.1);
        padding: 8px;
        border-radius: 20px;
    }
}

@media (min-width: 1920px) {
    .COLLECTIONS {
        max-width: 100%;
        max-height: 100vh;
    }
}


/* --- MAIN LAYOUT CONTAINER: holds sidebar, center chat-interface, and relevant-documents --- */
.layout-container {
    display: flex;
    width: 100vw; /* full width */
    height: 100vh; /* full height */
    overflow: hidden;
    background-color: white;
}

/* --- LEFT SIDEBAR --- */

.sidebar {
    width: 220px;
    min-width: 180px;
    max-width: 220px;
    flex-shrink: 0;
    background-color: var(--sidebar-background-color);
    display: flex;
    flex-direction: column;
    padding: 8px 10px;
    color: #fff;
    transition: width 0.3s ease-in-out;
    overflow: hidden;
    height: 100vh;
}

.sidebar-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    gap: 20px;
}

.text-wrapper {
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
    color: black;
}

.sidebar.sidebar-collapsed {
    width: 50px;
    min-width: 50px;
    max-width: 50px;
    padding: 8px 5px;
    align-items: center;
    gap: 8px;
}

.sidebar.sidebar-collapsed .sessions-favorite,
.sidebar.sidebar-collapsed .sessions-recent {
    display: none;
}

.sidebar.sidebar-collapsed .add-new-session-icon-wrapper {
    width: 16px;
    height: 24px;
    justify-content: center;
    margin-top: 24px;
}


.sidebar.sidebar-collapsed .button-text,
.sidebar.sidebar-collapsed .text-wrapper {
    display: none;
}

.sidebar.sidebar-collapsed .session-item {
    width: 100%;
    justify-content: center;
}

.sidebar.sidebar-collapsed .add-new-session-icon {
    width: 20px;
    height: 20px;
}


.sidebar.sidebar-collapsed .button-text {
    display: none;
}


.left-side-bar-burger-icon-wrapper {
    color: var(--chat-left-bar-text-color, #fff);
    font-size: var(--font-size, 14px);
    width: 14px;
    height: 14px;
    cursor: pointer;
    margin-top: 16px;
    margin-left: 11px;
}

.sidebar {
    transition: all 0.3s ease-in-out;
}

/* Primary button in sidebar */
.sidebar .add-new-session-icon-wrapper {
    display: flex;
    padding: var(--15, 6px) var(--3, 12px);
    justify-content: center;
    align-items: center;
    gap: var(--15, 6px);
    align-self: stretch;
    border-radius: var(--rounded-md, 6px);
    border: 2px solid #FFF;
    cursor: pointer;
    transition: background 0.2s ease-in-out;

    /* shadow-sm */
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
}

.add-new-session-icon-wrapper:hover {
    background-color: rgba(0, 0, 0, 0.05);
}


.sidebar .button-text {
    font-family: var(--font-family, Helvetica);
    font-weight: 700;
    color: var(--chat-left-bar-text-color, #fff);
    font-size: var(--font-size, 14px);
    line-height: 24px;
    white-space: nowrap;
    margin-right: 8px;
}

/* Example text styling for sidebar items */
.sidebar .text-wrapper {
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
    color: black;
}

.session-item {
    margin-right: -8px;
    display: flex;
    width: 208px;
    align-items: center;
    gap: 8px;
    position: relative;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: normal;
}

.real-item {
    margin-top: 20px;
    height: 32px;
}

.sessions-favorite {
    display: inline-flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 4px;
    position: relative;
    margin-right: 20px;
}

.no-queries-text {
    color: #808080;
    font-size: 16px;
    font-family: var(--font-family, Helvetica, sans-serif);
    width: 700px;
    margin: 8vh auto;
    line-height: 1.6;
}

.opening-message {
    list-style-type: disc !important;
    padding-left: 20px;
    margin-top: 8px;
    margin-bottom: 16px;
}


.conversation-starters-item-wrapper:hover::after {
    content: attr(data-fulltext);
    position: absolute;
    left: 50%;
    bottom: 110%;
    transform: translateX(-50%);
    width: auto;
    max-width: 280px;
    background-color: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 8px 12px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    font-size: 14px;
    font-family: var(--font-family, Helvetica);
    text-align: center;
    z-index: 999;
    pointer-events: none;
    white-space: pre-wrap;
}

.conversation-starters-item-wrapper:hover::before {
    content: "";
    position: absolute;
    left: 50%;
    bottom: 100%;
    transform: translateX(-50%);
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid rgba(0, 0, 0, 0.8);
    font-family: var(--font-family, Helvetica);
    z-index: 1000;
}

.sessions-recent {
    display: inline-flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 4px;
    position: relative;
    flex: 1;
    flex-grow: 1;
    margin-right: 20px;
}

.session-item.selected {
    background-color: var(--left-side-bar-selected-color, #205d8c);
    border-radius: var(--general-border-radius);
}

.folder-header.selected-folder {
    background-color: var(--left-side-bar-selected-color, #205d8c);
    border-radius: var(--general-border-radius);
}


.add-new-session-icon {
    position: relative;
    width: 20px;
    height: 20px;
    color: var(--chat-left-bar-text-color);
}

.text-wrapper-2 {
    position: relative;
    flex: 1;
    font-family: var(--font-family, Helvetica);
    font-weight: 400;
    color: var(--chat-left-bar-text-color);
    font-size: var(--font-size, 14px);
    letter-spacing: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    max-width: 100%;
    cursor: pointer;
}


.text-wrapper-3 {
    position: relative;
    font-family: var(--font-family, Helvetica);
    font-weight: 700;
    color: var(--chat-left-bar-text-color);
    font-size: var(--font-size, 14px);
    letter-spacing: 0;
    line-height: 27px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    flex: 1;
}

.chat-bubble {
    position: relative;
    width: 16px;
    height: 16px;
    margin-left: 4px;
}

.chat-bubble svg {
    width: 20px;
    height: 20px;
    background-color: var(--chat-left-bar-background-color);
    stroke: var(--chat-left-bar-text-color);
    stroke-width: 1.2;
}

.chat-bubble::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 20px;
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid var(--chat-left-bar-background-color);
}

/* --- CENTER CHAT INTERFACE --- */

.logout-button {
    display: flex;
    margin-left: auto;
    border-radius: var(--rounded, 4px);
    border: 8px solid var(--gray-300, #D1D5DB);
    background: var(--gray-300, #D1D5DB);
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
    cursor: pointer;
}

.logout-button-wrapper {
    display: flex;
    padding: var(--1, 0px) var(--2, 8px);
    align-items: center;
}

.user-name {
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
    margin-right: -1px;
    line-height: 24px;
    white-space: nowrap;
}

.verticalline {
    display: inline-block;
    height: 32px;
    margin: 12px 8px 12px 8px;
}

.svg-icon-query-answer {
    position: relative;
    flex: 0 0 auto;
    color: var(--main-color-of-company, #E31536);
    width: 50px;
    height: 50px;
    margin-right: 26px;
    margin-top: 0px;
}

.chat-wrapper {
    flex-direction: row;
    display: flex;
}

.chat-interface {
    display: flex;
    flex-direction: column;
    flex: 1; /* fill leftover horizontal space */
    background-color: #fff; /* or your preference */
}

/* Header with logout top-right (requirement #2) */
.header {
    position: relative;
    display: flex;
    align-items: center;
    height: 64px;
    padding: 0 24px;
    background-color: transparent;
}

/* Logout absolutely in top-right */
.header .secondary-button-wrapper {
    position: absolute;
    top: 10px;
    right: 10px;
}

/* Chat window: scrollable (requirement #1) */
.chat-window {
    flex: 1; /* fill vertical space */
    overflow-y: auto; /* makes chat content scrollable */
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    padding: 24px 4px;
    background-color: #ffffff;
}

/* Example styling for user/AI messages */
.user-message-wrapper {
    display: flex;
    width: 894px;
    justify-content: flex-end;
    gap: 12px;
    padding: 4px;
}


.user-message-bubble {
    background-color: #f2f2f2;
    border-radius: 12px 12px 0px 12px;
    padding: 2px 15px 2px 15px;
    min-width: 2vw;
    max-width: 50vw;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.ai-message-bubble {
    background-color: #ffffff;
    border-radius: 12px;
    padding: 16px;
    width: 55vw;
    display: flex;
    flex-direction: column;
    gap: 16px;
}


.code-block {
    background-color: #ffffff;
    border-radius: 6px;
    padding: 16px;
    width: 768px;
    overflow: hidden;
}

.query-text-style {
    color: #000;
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 171.429% */
}

.query-text-style-2 {
    color: #000;
    font-family: var(--font-family, Helvetica);
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 171.429% */
}


.query-text-style-2 ol,
.query-text-style-2 ul {
    list-style-position: inside;
    margin: 0 0 1em 0;
    padding: 0;
}

.query-text-style-2 ol {
    list-style-type: decimal;
}

.query-text-style-2 ul {
    list-style-type: disc;
}

.query-text-style-2 li {
    padding: 0;
    margin-left: 5px;
}


.closeRelevantDocumentsButton {
    background-color: var(--sidebar-background-color);
    border-style: none;
}

.doc-icon {
    display: flex;
    height: 36px;
    padding: 10px;
    align-items: center;
    gap: 10px;
    border-radius: 4px;
    background: #F5F5F5;
}

/* (3) conversation-starters & (4) input field pinned at top */
.chat-input-part {
    position: sticky;
    top: 0;
    z-index: 999;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding-bottom: 8px;
}


.conversation-starters {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: center;
    width: 100%;
    margin: 0 auto;
}

.conversation-starters-title {
    font-size: 15px;
    font-weight: 700;
    font-family: var(--font-family, Helvetica);
    color: #000;
    text-align: center;
    width: 100%;
    margin-bottom: 8px;
}

.conversation-starters-item-wrapper {
    position: relative;
    display: inline-block;
    width: auto;
}

.conversation-starters-item {
    font-family: var(--font-family, Helvetica);
    font-size: 14px;
    padding: 8px 12px;
    border: 1px solid #c0c0c0;
    border-radius: 25px;
    cursor: pointer;
    background-color: #fafafa;
}

.conversation-starters-item:hover {
    background-color: #f2f2f2;
}


/* input-field styling */
.input-field {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 56px;
    margin: 0 auto; /* center horizontally */
    padding: 16px 24px;
    background-color: #ffffff;
    border: 1px solid #272727;
    border-radius: 6px;
    box-shadow: 0px 4px 11px 0px rgba(194, 194, 194, 0.25);
    position: relative;
}

.user-message-container {
    justify-items: flex-end;
}


.user-message-container,
.ai-message-bubble,
.input-field,
.conversation-starters {
    width: 920px;
    max-width: 1500px;
    min-width: 200px;
    margin: 0 auto;
}


.frame-5 {
    display: inline-flex;
    align-items: center;
    gap: 16px;
    margin-right: -13px; /* from your code */
}

.button-text-wrapper {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-4px) var(--spacing-8px);
    background-color: var(--tailwind-white, #fff);
    border: 1px solid var(--tailwind-gray-300, #ccc);
    border-radius: var(--radius-rounded, 6px);
    box-shadow: var(--box-shadow);
}

.collection-name-in-chat {
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
    color: var(--tailwind-gray-900, #000);
    line-height: 16px;
    max-width: 110px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: normal;
    cursor: pointer;
}

.conversation-starters {
    width: 1000px !important;
}


/* Send icon container */
.send-icon-wrapper {
    display: flex;
    width: 42px;
    height: 42px;
    padding: 2px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 6px;
    background: var(--main-color-of-company, #E31536);
    cursor: pointer;
    margin-left: 14px;
    margin-right: 14px;
    min-width: 25px;
    min-height: 25px;
    margin: 5px 5px 5px 5px;

}


/* Example for the microphone icon if you have one */
.microphone {
    position: relative;
    width: 20px;
    height: 20px;
    margin-left: -33px;
}

/* Right sidebar (relevant-documents) (requirement #6) */
.relevant-documents {
    flex-shrink: 0;
    min-width: 240px; /* ensures it doesn't become too narrow */
    max-width: 18vw; /* or a fixed px if you prefer */
    background-color: var(--sidebar-background-color);
    overflow-y: auto; /* let it scroll if there's lots of content */
    border-left: 1px solid #ccc; /* optional divider line */
    padding: 8px; /* optional spacing */
}

.relevant-document-text {
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
    color: #fff;
}

/* Example for the "element-icons-close-wrapper" in the right bar */
.element-icons-close-wrapper {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
}

.element-icons-close-wrapper .img-2 {
    cursor: pointer;
    padding: var(--1, 14px) var(--2, 8px);
}

/* Additional random classes from your existing code (unchanged) */
.favorites-star-icon {
    color: var(--chat-left-bar-text-color, #fff);
    width: 16px;
    height: 16px;
    margin-left: 4px;
}

/* Just references for the burger icon, etc. */


/* For your "three-dot" menu if used */
.three-dot {
    position: relative;
    width: 4px;
    height: 16px;
    cursor: pointer;
    margin-right: 8px;
}

.three-dot .three-dot-ellipse,
.three-dot .three-dot-div,
.three-dot .three-dot-ellipse-2 {
    position: absolute;
    width: 4px;
    height: 4px;
    left: 0;
    background-color: #ffffff;
    border-radius: 1.78px;
}

.three-dot .three-dot-ellipse {
    top: 0;
}

.three-dot .three-dot-div {
    top: 6px;
}

.three-dot .three-dot-ellipse-2 {
    top: 12px;
}


.dropdown {
    position: absolute;
    top: 0;
    left: 100%;
    margin-left: 8px;
    z-index: 9999;
    background: #fff;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
}

.session-scroll {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: none;
}


.dropdown .div {
    font-family: var(--font-family, Helvetica);
    font-weight: 400;
    color: #ff3b30;
    font-size: var(--font-size, 14px);
    line-height: 24px;
    white-space: nowrap;
}

.menuitem {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    width: 85%;
    background-color: white;
    border-radius: 6px;
    transition: background 0.2s ease-in-out;
    cursor: pointer;
}

.menuitem:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.menuitem.delete {
    color: red;
    font-weight: bold;
}

.menuitem img {
    width: 20px;
    height: 20px;
}

/* Paperclip & Send Icon color overrides if needed */
#paperclip {
    stroke: var(--main-color-of-company, #E31536);
}

#SendIcon {
    stroke: var(--chat-left-bar-text-color, #000);
}

/* Modal Container  */
.modal {
    display: none;
    width: 512px;
    max-width: 512px;
    padding: 24px;
    flex-direction: column;
    align-items: flex-start;
    gap: 24px;
    border-radius: 8px;
    background: var(--white, #FFF);
    z-index: 9999;
    border: 1px solid rgba(0, 0, 0, 0.15);
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15),
    0px 10px 30px rgba(0, 0, 0, 0.25),
    0px 20px 50px rgba(0, 0, 0, 0.3);
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

}

.modal .modal-content {
    width: 100%;
}

.modal-message {
    color: var(--gray-500, #6B7280);
    text-align: center;
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* %142.857 */
}

.modal-title {
    display: flex;
    padding: 0px 153.45px 0px 153.44px;
    flex-direction: column;
    align-items: center;
    align-self: stretch;
    color: var(--gray-900, #111827);
    text-align: center;
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
}

.modal-actions {
    display: flex;
    gap: 12px;
    width: 100%;
}

.modal-icon {
    display: flex;
    padding: 0px 153.45px 0px 153.44px;
    flex-direction: column;
    align-items: center;
    align-self: stretch;
}

.modal-icon svg {
    border-radius: 9999px;
    background: #000;
}

.modal-button {
    font-family: var(--font-family);
    font-size: var(--font-size);
    cursor: pointer;
    border: none;
    border-radius: 6px;
}

.modalAcceptButton {
    display: flex;
    padding: 10px 30px 10px 30px;
    justify-content: center;
    align-items: flex-start;
    flex: 1 0 0;
    align-self: stretch;
    border-radius: 6px;
    background: #000;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    color: #FFF;
    font-family: var(--font-family);
    font-size: var(--font-size);
    cursor: pointer;
}

.modalCancelButton {
    display: flex;
    padding: 10px 30px 10px 30px;
    justify-content: center;
    align-items: flex-start;
    flex: 1 0 0;
    align-self: stretch;
    border-radius: 6px;
    background: var(--white, #FFF);
    box-shadow: inset 0px 0px 0px 0px #FFF, inset 0px 0px 0px 1px #D1D5DB, 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    font-family: var(--font-family);
    font-size: var(--font-size);
    cursor: pointer;
}

.modalSkipButton {
    display: flex;
    padding: 10px 30px 10px 30px;
    justify-content: center;
    align-items: flex-start;
    align-self: stretch;
    border-radius: 6px;
    background: var(--gray-300, #FFF);
    box-shadow: inset 0px 0px 0px 0px #FFF, inset 0px 0px 0px 1px #D1D5DB, 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    font-family: var(--font-family);
    font-size: var(--font-size);
    cursor: pointer;
}

/* Modal Input */
.modal-input {
    width: 100%;
    padding: 8px;
    font-size: var(--font-size, 14px);
    font-family: var(--font-family, Helvetica);
    margin-top: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

/* Modal Buton*/
.modal-actions {
    display: flex;
    gap: 12px;
    width: 100%;
}

.modal-message {
    color: var(--gray-500, #6B7280);
    text-align: center;
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */
}

.close-button {
    align-self: flex-end;
    background: none;
    border: none;
    font-size: var(--font-size, 14px);
    cursor: pointer;
}


/* Prompt Modal Container  */
.prompt-modal {
    display: none;
    width: 512px;
    max-width: 512px;
    max-height: 512px;
    height: 512px;
    padding: 24px;
    flex-direction: column;
    align-items: flex-start;
    gap: 24px;
    border-radius: 8px;
    background: var(--white, #FFF);
    z-index: 9999;
    border: 1px solid rgba(0, 0, 0, 0.15);
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15),
    0px 10px 30px rgba(0, 0, 0, 0.25),
    0px 20px 50px rgba(0, 0, 0, 0.3);
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    overflow: hidden;

}

.prompt-modal .prompt-modal-content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    padding-right: 8px;
    font-family: var(--font-family, Helvetica);
}


.prompt-modal-title {
    display: flex;
    padding: 0px 153.45px 0px 153.44px;
    flex-direction: column;
    align-items: center;
    align-self: stretch;
    color: var(--gray-900, #111827);
    text-align: center;
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
}

.prompt-modal-actions {
    display: flex;
    gap: 12px;
    width: 100%;
}

.prompt-modal-icon {
    display: flex;
    padding: 0px 153.45px 0px 153.44px;
    flex-direction: column;
    align-items: center;
    align-self: stretch;
}

.prompt-modal-icon svg {
    border-radius: 9999px;
    background: #000;
}

.prompt-modal-button {
    cursor: pointer;
    border: none;
    border-radius: 6px;
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
}

.prompt-modalAcceptButton {
    display: flex;
    padding: 10px 30px 10px 30px;
    justify-content: center;
    align-items: flex-start;
    flex: 1 0 0;
    align-self: stretch;
    border-radius: 6px;
    background: #000;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    color: #FFF;
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
}

.prompt-modalCancelButton {
    display: flex;
    padding: 10px 30px 10px 30px;
    justify-content: center;
    align-items: flex-start;
    flex: 1 0 0;
    align-self: stretch;
    border-radius: 6px;
    background: var(--white, #FFF);
    box-shadow: inset 0px 0px 0px 0px #FFF, inset 0px 0px 0px 1px #D1D5DB, 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
}

.prompt-modalSkipButton {
    display: flex;
    padding: 10px 30px 10px 30px;
    justify-content: center;
    align-items: flex-start;
    align-self: stretch;
    border-radius: 6px;
    background: var(--gray-300, #FFF);
    box-shadow: inset 0px 0px 0px 0px #FFF, inset 0px 0px 0px 1px #D1D5DB, 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
}

/* Modal Input */
.prompt-modal-input {
    width: 100%;
    padding: 8px;
    font-size: var(--font-size, 14px);
    font-family: var(--font-family, Helvetica);
    margin-top: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

/* Modal Buton*/
.prompt-modal-actions {
    display: flex;
    gap: 12px;
    width: 100%;
}


.prompt-close-button {
    align-self: flex-end;
    background: none;
    border: none;
    font-size: var(--font-size, 14px);
    cursor: pointer;
}

#openPromptModal {
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
    border-radius: 4px;
    cursor: pointer;
}

#openDeepeningModuleModal {
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
    border-radius: 4px;
    cursor: pointer;
}

#openPromptServiceModal {
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
    border-radius: 4px;
    cursor: pointer;
}


.prompt-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 12px;
}


.prompt-actions button:hover {
    background-color: var(--prompt-buttons-hover-color, black);
    color: white;
}


#prompt-list {
    list-style: none;
    padding: 0;
}

#prompt-list li {
    margin-bottom: 14px;
    position: relative;
    padding-right: 70px;
    line-height: 1.6;
}

.prompt-modal-content li{
    margin-bottom: 14px;
    position: relative;
    padding-right: 70px;
    line-height: 1.6;
}

#deepening-module-list li {
    position: relative;
    margin-bottom: 14px;
    padding-right: 70px;
    line-height: 1.6;
}


.copy-button {
    position: absolute;
    right: 0;
    top: 4px;
    background-color: #e0e0e0;
    border: none;
    padding: 4px 10px;
    font-size: 12px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s ease-in-out;
}

.copy-button:hover {
    background-color: #c0c0c0;
}


/* (5) RESPONSIVE: ensure columns stack on small screens (requirement #6) */
@media (max-width: 768px) {
    .layout-container {
        flex-direction: column; /* stack .sidebar, .chat-interface, .relevant-documents */
        height: auto; /* let them grow vertically */
    }

    /* Make each "sidebar" and "relevant-documents" full-width in mobile */
    .sidebar,
    .relevant-documents {
        width: 100%;
        max-width: 100%;
    }


    .chat-items-icons {
        display: inline-flex;
        align-items: flex-start;
        gap: 8px;
        position: relative;
        flex: 0 0 auto;
    }

    .speak-icon {
        position: relative;
        width: 36px;
        height: 36px;
    }

    .copy-response .adjust-response {
        position: relative;
        flex: 0 0 auto;
    }

    .star-wrapper {
        display: inline-flex;
        align-items: center;
        gap: 10px;
        padding: 10px;
        position: relative;
        flex: 0 0 auto;
    }

    .star {
        position: relative;
        width: 16px;
        height: 16px;
    }


    .clickable-icon {
        cursor: pointer;
        border: none;
        background: none;
        outline: none;
    }


    .folder-container {
        border: 1px solid #444;
        border-radius: 4px;
        margin-bottom: 30px;
    }

    .folder-header {
        display: flex;
        align-items: center;
        padding: 4px 8px;
        background-color: #1f1f1f;
        color: #fff;
        cursor: pointer;
        position: relative;
    }

    .folder-icon {
        width: 16px;
        height: 16px;
        margin-right: 8px;
    }

    .folder-title {
        flex-grow: 1;
    }


    .folder-toggle-arrow {
        width: 24px;
        height: 24px;
        margin-right: 8px;
        cursor: pointer;
        flex-shrink: 0;
        margin-left: 8px;
        cursor: pointer;
    }

    placeholder {
        color: black;
    }

    /* Mouse toggle arrow ve three-dot hide */
    .add-folder-icon,
    .folder-toggle-arrow,
    .three-dot {
        display: block;
    }

    /* Show Mouse hover */
    .session-item:hover .folder-toggle-arrow,
    .session-item:hover .three-dot,
    .session-item:hover .add-folder-icon {
        display: block;
    }


    .label-chat-goal {
        font-family: var(--font-family, Helvetica);
        font-size: var(--font-size, 14px);
        color: #000;
        line-height: 24px;
        margin-top: 20px;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: scale(0.9);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .message-fade-in {
        animation: fadeIn 0.3s ease-out;
    }

    .message-slide-in {
        animation: slideIn 0.5s ease-out;
    }


    .error-message {
        background-color: #ffdddd;
        border-left: 5px solid #ff4d4d;
        padding: 10px;
    }

    .retry-button {
        background-color: #ff4d4d;
        color: white;
        border: none;
        padding: 5px 10px;
        cursor: pointer;
        margin-top: 5px;
    }

    /* Spinner */
    .modal-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(0, 0, 0, 0.2);
        border-top: 4px solid #000;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 16px auto;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }

}

/* Slash command suggestions styling */
.slash-command-suggestions {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.slash-command-suggestion {
    padding: 8px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s;
}

.slash-command-suggestion:hover,
.slash-command-suggestion.selected {
    background-color: #f5f5f5;
}

.slash-command-suggestion .command {
    color: #2563eb;
    font-weight: 500;
    font-family: monospace;
}

.slash-command-suggestion .description {
    color: #666;
    font-size: 0.9em;
}

.slash-command-suggestions::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 20px;
    width: 10px;
    height: 10px;
    background: white;
    border-right: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
    transform: rotate(45deg);
}


.legal-footer {
    position: fixed;
    bottom: 12px;
    right: 20px;
    z-index: 9999;
    /*background-color: rgba(255, 255, 255, 0.85);*/
    padding: 6px 12px;
    /*border-radius: 8px;*/
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
    /*box-shadow: 0 0 5px rgba(0,0,0,0.1);*/
}

.legal-footer a {
    color: #555;
    text-decoration: none;
    margin: 0 5px;
}

.legal-footer a:hover {
    text-decoration: underline;
}


#collectionDropdown {
    display: none;
    position: absolute;
    background: #fff;
    border: 1px solid #ccc;
    z-index: 9999;
    min-width: 150px;
    max-height: 200px;
    overflow-y: auto;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
    border: 1px solid var(--tailwind-gray-300, #ccc);
    border-radius: var(--radius-rounded, 6px);
}


.collection-dropdown-item {
    padding: 6px 12px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);

}

.collection-dropdown-item:hover {
    background-color: #f2f2f2;
}

/* Tab Styles */
.tab-button {
    padding: 8px 16px;
    border: none;
    background: #f0f0f0;
    cursor: pointer;
    border-radius: 4px 4px 0 0;
    font-family: var(--font-family, Helvetica);
    font-size: var(--font-size, 14px);
}

.tab-button.active {
    background: #007bff;
    color: white;
}

.tab-button:hover:not(.active) {
    background: #e0e0e0;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}
