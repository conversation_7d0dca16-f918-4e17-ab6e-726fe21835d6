user                            root;
worker_processes                auto;

error_log                       /var/log/nginx/error.log warn;

events {
    worker_connections          1024;
}

http {
    include                     /etc/nginx/mime.types;
    default_type                application/octet-stream;
    sendfile                    off;
    access_log                  off;
    keepalive_timeout           3000;

    server {
        listen                  8006;
        root                    /usr/share/nginx/html;
        index                   index-${FE_STYLE}.html;
        server_name             localhost;
        client_max_body_size    50m;

        location / {
            try_files $uri $uri/ /index-${FE_STYLE}.html;
        }

        location /static/ {
            alias /usr/share/nginx/html/static/;
        }

        location /api/query/ {
            proxy_pass ${QUERY_BASE_URL}/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        location /slash-commands/ {
            proxy_pass ${QUERY_BASE_URL}/slash-commands/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        location /api/loader/ {
            proxy_pass ${LOADER_BASE_URL}/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

    }
}
