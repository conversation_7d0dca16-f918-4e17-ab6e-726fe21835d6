<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <link rel="stylesheet" href="/static/css/globals.css"/>
    <link rel="stylesheet" href="/static/css/styleguide-cevey.css"/>
    <link rel="stylesheet" href="/static/css/style.css"/>
    <title>_fbeta AI Interface</title>
</head>
<body>
<div class="COLLECTIONS" id="COLLECTIONS">
    <div class="cone">
        <img class="img" src="https://c.animaapp.com/7jiuBGHy/img/cone-01-2.png" loading="lazy" alt="decorative cone"/>
    </div>
    <div class="cone-wrapper">
        <img class="cone-2" src="https://c.animaapp.com/7jiuBGHy/img/cone-01-2-1.png" loading="lazy" alt="decorative background element"/>
    </div>
    <a href="https://fbeta.de/kontakt/" class="setting-item" target="_blank">
        <div class="setting-item">
            <img class="help-circle" src="https://c.animaapp.com/7jiuBGHy/img/help-circle.svg"/>
            <div class="help-documentation">Hilfe &amp; Dokumentation</div>
        </div>
    </a>
    <div class="collection-items"></div>
</div>

<!-- Wrap left-sidebar, center chat, and right sidebar in ONE container -->
<div class="layout-container" id="CHAT">

    <!-- LEFT SIDEBAR -->
    <div class="sidebar">
        <div class="sidebar-content">
            <div class="left-side-bar-burger-icon-wrapper">
                <svg class="left-side-bar-burger-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 16 12">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M0 0.75C0 0.551088 0.0790175 0.360322 0.21967 0.21967C0.360322 0.0790175 0.551088 0 0.75 0H15.25C15.4489 0 15.6397 0.0790175 15.7803 0.21967C15.921 0.360322 16 0.551088 16 0.75C16 0.948912 15.921 1.13968 15.7803 1.28033C15.6397 1.42098 15.4489 1.5 15.25 1.5H0.75C0.551088 1.5 0.360322 1.42098 0.21967 1.28033C0.0790175 1.13968 0 0.948912 0 0.75ZM0 6C0 5.80109 0.0790175 5.61032 0.21967 5.46967C0.360322 5.32902 0.551088 5.25 0.75 5.25H15.25C15.4489 5.25 15.6397 5.32902 15.7803 5.46967C15.921 5.61032 16 5.80109 16 6C16 6.19891 15.921 6.38968 15.7803 6.53033C15.6397 6.67098 15.4489 6.75 15.25 6.75H0.75C0.551088 6.75 0.360322 6.67098 0.21967 6.53033C0.0790175 6.38968 0 6.19891 0 6ZM0 11.25C0 11.0511 0.0790175 10.8603 0.21967 10.7197C0.360322 10.579 0.551088 10.5 0.75 10.5H15.25C15.4489 10.5 15.6397 10.579 15.7803 10.7197C15.921 10.8603 16 11.0511 16 11.25C16 11.4489 15.921 11.6397 15.7803 11.7803C15.6397 11.921 15.4489 12 15.25 12H0.75C0.551088 12 0.360322 11.921 0.21967 11.7803C0.0790175 11.6397 0 11.4489 0 11.25Z"
                          fill="white"/>
                </svg>

            </div>

            <div class="add-new-session-icon-wrapper" id="addNewSessionButton">
                <svg class="add-new-session-icon" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor">
                    <g id="add">
                        <path id="icon" d="M9.1665 10.8334H4.1665V9.16669H9.1665V4.16669H10.8332V9.16669H15.8332V10.8334H10.8332V15.8334H9.1665V10.8334Z"/>
                    </g>
                </svg>
                <div class="button-text">Neuer Chat</div>
            </div>
            <div class="sessions-favorite">
            </div>
            <div class="session-scroll">
                <div class="sessions-recent" id="sessions-recent">
                </div>
            </div>
        </div>
        <div id="prompt-actions" class="prompt-actions">
            <button id="openPromptServiceModal" style="margin-top:auto;width:100%;">Prompt-Service</button>
            <button id="openPromptModal" style="margin-top: auto; width: 100%;">Promptvorschläge</button>
            <button id="openDeepeningModuleModal" style="width: 100%; margin-bottom: 8px;">Module für Vertiefungen</button>
        </div>


    </div><!-- END .sidebar -->

    <!-- CENTER CHAT -->
    <div class="chat-interface">
        <header class="header">
            <div class="frame-2">
                <div class="logo-section">
                    <img src="/static/img/cevey-logo.png" alt="Cevey Logo" class="logo" style="width:180px;">
                </div>
                <div class="user-controls"></div>
            </div>
            <div class="secondary-button-wrapper">
                <div class="logout-button-wrapper">
                    <span id="userName" class="user-name"></span>
                    <div class="verticalline"></div>
                    <button class="logout-button" id="logout-button">Abmelden</button>
                </div>
            </div>
        </header>

        <div class="chat-window" id="chat-window">
            <!-- Chat messages go here -->
        </div>

        <!-- Input pinned at top with conversation-starters -->
        <div class="chat-input-part">
            <div class="conversation-starters" id="conversation-starters">
            </div>
            <div class="input-field" id="input-field">
                <div class="frame-5">
                    <div class="button-text-wrapper" id="collection-name-selector" title="Sammlungen">
                        <div class="collection-name-in-chat" id="collection-name-in-chat"></div>
                    </div>
                    <svg id="uploadIconButton" width="24" height="24" viewBox="0 0 19 18" fill="none"
                         xmlns="http://www.w3.org/2000/svg" onclick="triggerFileSelection()"
                         style="cursor: pointer;">
                        <g id="paperclip-01">
                            <path id="paperclip"
                                  d="M13.6329 6.0998L8.30803 11.4247C7.67485 12.0579 6.64824 12.0579 6.01506 11.4247C5.38054 10.7902 5.38207 9.76096 6.01846 9.12832L10.1478 5.0234L11.1848 3.9864C12.4465 2.72464 14.4922 2.72464 15.754 3.9864C17.0157 5.24815 17.0157 7.29386 15.754 8.55561L14.7326 9.57702L10.8614 13.4482C8.85899 15.534 5.82477 15.8514 3.70332 13.8148C1.60784 11.8032 1.96302 8.78723 4.08176 6.66848L7.93765 2.81201"
                                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </g>
                    </svg>
                    <input type="file" id="pdfInput" accept=".pdf,.docx,.xlsx,.csv" multiple style="display: none;" onchange="uploadPDF()"/>
                    <textarea id="questionInput" class="input-section" placeholder="Frage eingeben..."></textarea>

                </div>
                <div class="send-icon-wrapper">
                    <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-left:-2px;">
                        <g id="SendIcon">
                            <path id="Icon" d="M15.1668 1.83325L7.8335 9.16658M15.1668 1.83325L10.5002 15.1666L7.8335 9.16658M15.1668 1.83325L1.8335 6.49992L7.8335 9.16658"
                                  stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
                        </g>
                    </svg>
                </div>

            </div>
        </div>


        <div id="messageModal" style="display: none;" class="modal">
            <div class="modal-content">
                <p id="modalMessage" class="modal-message"></p>
                <br>
                <div class="modal-actions">
                    <button class="modal-button modalAcceptButton" onclick="closeMessageModal()">Okay</button>
                </div>
            </div>
        </div>

        <div id="YesNoModal" style="display: none;" class="modal">
            <div class="modal-content">
                <h3 id="YesNoModalTitle" class="modal-title"></h3>
                <br>
                <br>
                <p id="YesNoModalMessage" class="modal-message"></p>
                <br>
                <div class="modal-actions">
                    <button class="modal-button modalCancelButton" onclick="closeYesNoModal()">Nein</button>
                    <button class="modal-button modalAcceptButton" onclick="confirmYesNoModal()">Ja</button>
                </div>
            </div>
        </div>

        <div id="RenameSessionModal" class="modal" style="display: none;">
            <div class="modal-content">
                <h3 class="modal-title">Session umbenennen</h3>
                <br>
                <br>
                <input type="text" id="RenameSessionModalInput" placeholder="Neuen Namen eingeben" class="modal-input"/>
                <br>
                <br>
                <br>
                <div class="modal-actions">
                    <button id="RenameSessionModalRenameButton" class="modal-button modalAcceptButton">Umbenennen</button>
                    <button id="RenameSessionModalCancelButton" class="modal-button modalCancelButton">Abbrechen</button>
                </div>
            </div>
        </div>

        <div id="CreateFolderModal" class="modal" style="display: none;">
            <div class="modal-content">
                <br>
                <h3 class="modal-title">Neuen Ordner erstellen</h3>
                <br>
                <input type="text" id="CreateFolderModalInput" placeholder="Ordnernamen eingeben" class="modal-input"/>
                <br>
                <br>
                <div class="modal-actions">
                    <button id="CreateFolderModalCreateButton" class="modal-button modalAcceptButton">Erstellen</button>
                    <button onclick="closeCreateFolderModal()" class="modal-button modalCancelButton">Abbrechen</button>
                </div>
            </div>
        </div>

        <div id="chatGoalModal" class="modal" style="display: none;">
            <div class="modal-content">
                <button class="close-button" onclick="closeChatGoalModal()">✖</button>
                <br>
                <h3 class="modal-title">Zielsetzung für Gespräch festlegen</h3>
                <div class="chat-goal-form">
                    <p>
                        <label for="lieferantDropdown" class="label-chat-goal">Lieferanten:</label>
                        <select id="lieferantDropdown" class="modal-input">
                            <option value="new">Neuen Lieferanten erstellen</option>
                            <!-- Bestehende Lieferantenoptionen können hier hinzugefügt werden -->
                        </select>
                    </p>
                    <br>
                    <p>
                        <label for="lieferant" class="label-chat-goal">Lieferant:</label>
                        <input type="text" id="lieferant" placeholder="z.B. Firma XYZ" class="modal-input">
                    </p>
                    <br>
                    <p>
                        <label for="verhandlungsziel" class="verhandlungsziel">Verhandlungsziel:</label>
                        <input type="text" id="verhandlungsziel" placeholder="z.B. Preisreduktion um 10%" class="modal-input">
                    </p>
                    <br>
                    <div class="modal-actions">
                        <button onclick="createNewSession()" class="modal-button modalAcceptButton">Bestätigen</button>
                        <button onclick="closeChatGoalModal()" class="modal-button modalCancelButton">Abbrechen</button>
                    </div>
                </div>
            </div>
        </div>


        <div id="goalModal" class="modal" style="display: none;">
            <div class="modal-content">
                <button class="close-button" onclick="closeOnlyGoalModal()">✖</button>
                <br>
                <h3 class="modal-title">Zielsetzung für Gespräch festlegen</h3>
                <div class="chat-goal-form">
                    <p>
                        <label for="verhandlungsziel2" class="verhandlungsziel2">Verhandlungsziel:</label>
                        <input type="text" id="verhandlungsziel2" placeholder="z.B. Preisreduktion um 10%" class="modal-input">
                    </p>
                    <br>
                    <div class="modal-actions">
                        <button onclick="createNewSession()" class="modal-button modalAcceptButton">Bestätigen</button>
                        <button onclick="closeOnlyGoalModal()" class="modal-button modalCancelButton">Abbrechen</button>
                    </div>
                </div>
            </div>
        </div>


        <div id="chatGoalModalCreateFolder" class="modal" style="display: none;">
            <div class="modal-content">
                <button class="close-button" onclick="closeChatGoalModalCreateFolder()">✖</button>
                <br>
                <h3 class="modal-title">Zielsetzung für Ordner erstellen</h3>
                <div class="chat-goal-form">
                    <p>
                        <label for="lieferantCreateFolder" class="label-chat-goal">Lieferant:</label>
                        <input type="text" id="lieferantCreateFolder" placeholder="z.B. Firma XYZ" class="modal-input">
                    </p>
                    <br>
                    <p>
                        <label for="verhandlungszielCreateFolder" class="verhandlungsziel">Verhandlungsziel:</label>
                        <input type="text" id="verhandlungszielCreateFolder" placeholder="z.B. Preisreduktion um 10%" class="modal-input">
                    </p>
                    <br>
                    <div class="modal-actions">
                        <button onclick="saveChatGoalCreateFolder()" class="modal-button modalAcceptButton">Bestätigen</button>
                        <button onclick="closeChatGoalModalCreateFolder()" class="modal-button modalCancelButton">Abbrechen</button>
                    </div>
                </div>
            </div>
        </div>


        <!-- Session Loading Modal -->
        <div id="SessionLoadingModal" class="modal" style="display: none;">
            <div class="modal-content">
                <!-- Spinner -->
                <div class="modal-spinner"></div>

                <h3 class="modal-title">Session wird geladen...</h3>
                <br>
                <p class="modal-message">
                    Sehr gut! Wir erstellen eine neue Sitzung für dich mit diesem neuen Problem! <br>
                    Sobald unsere Sprachmodell-Antwort kommt, wird deine Sitzung erstellt.
                </p>

                <br>

                <div class="modal-actions">
                    <button onclick="closeSessionLoadingModal()" class="modal-button modalCancelButton">Abbrechen</button>
                </div>
            </div>
        </div>

        <div id="logoutModal" class="logout-modal" style="display: none;">
            <div class="div">
                <div class="logout-icon-wrapper">
                    <img src="/static/img/right-sidebar/Logout.svg" alt="Abmelde-Symbol">
                </div>
                <div class="logout-content">
                    <br>
                    <div class="heading-wrapper">
                        <span class="heading">Abmelden</span>
                    </div>
                    <br>
                    <div class="logout-text-wrapper">
                        <p class="logout-text">Sind Sie sicher, dass Sie sich abmelden möchten?</p>
                    </div>
                </div>
                <br>
                <br>
                <div class="logout-actions">
                    <button class="logout-cancel-wrapper" onclick="closeLogoutModal()">
                        <span class="logout-cancel">Abbrechen</span>
                    </button>
                    <button class="logout-yes-wrapper" onclick="confirmLogout()">
                        <span class="logout-yes">Ja, abmelden</span>
                    </button>
                </div>
            </div>
        </div>

        <div id="UploadedFilesModal" class="modal" style="display: none;">
            <div class="modal-content">
                <h3 class="modal-title">Hochgeladene PDFs</h3>
                <br>
                <div id="uploaded-files-list" class="modal-list"></div>
                <br>
                <div class="modal-actions">
                    <button onclick="closeUploadedFilesModal()" class="modal-button modalCancelButton">Schließen</button>
                </div>
            </div>
        </div>

        <div id="demoNoticeModal" class="modal" style="display: none;">
            <div class="modal-content">
                <p style="font-family: var(--font-family, Helvetica);font-size: var(--font-size, 14px);">
                    Bei diesem Produkt handelt es sich um eine Demo-Version mit ggf. noch eingeschränktem Funktionsumfang.<br><br>
                    Ihre Eingaben sowie die Dokumente werden zur Bereitstellung der Systemfunktionen gespeichert und mit Ablauf der Testphase gelöscht. <br><br>
                    Die Verarbeitung und Speicherung der Daten erfolgt ausschließlich auf europäischen Servern in der Microsoft Azure. <br>
                </p>
                <div class="modal-actions">
                    <button onclick="closeDemoNoticeModal()" class="modalCancelButton">Bestätigen</button>
                </div>
            </div>
        </div>

        <div id="promptModal" class="prompt-modal" style="display: none;">
            <span id="closePromptModal" class="close-button">&times;</span>
            <div class="prompt-modal-content">
                <h3 class="modal-title">Prompt-Vorschläge</h3>
                <p>Sie können diese Prompt-Vorschläge per Copy & Paste in Ihre Anfrage an den Coach einfügen.</p>

                <ul id="prompt-list">
                    <li>★ Zielformulierung: Präzisiere die Ziele MaxPlus, Max und Budget
                        <button class="copy-button" data-text="Zielformulierung: Präzisiere die Ziele MaxPlus, Max und Budget">Copy</button>
                    </li>
                    <li>★ Begründungen der Ziele: Formuliere aus der Business-Situation heraus eindeutige Begründungen der Ziele
                        <button class="copy-button" data-text="Begründungen der Ziele: Formuliere aus der Business-Situation heraus eindeutige Begründungen der Ziele">Copy
                        </button>
                    </li>
                    <li>★ Härtegrade: Erhöhe den Härtegrad der Verhandlung insgesamt um x Stufen
                        <button class="copy-button" data-text="Härtegrade: Erhöhe den Härtegrad der Verhandlung insgesamt um x Stufen">Copy</button>
                    </li>
                    <li>★ Härtegrade: Erniedrige den Härtegrad der Verhandlung um x Stufen
                        <button class="copy-button" data-text="Härtegrade: Erniedrige den Härtegrad der Verhandlung um x Stufen">Copy</button>
                    </li>
                    <li>★ Position der Stärke / Schwäche: Formuliere das Verhalten aus einer Position der Stärke / Schwäche
                        <button class="copy-button" data-text="Position der Stärke / Schwäche: Formuliere das Verhalten aus einer Position der Stärke / Schwäche">Copy</button>
                    </li>
                    <li>★ Redeanteil: Verkürze die Sätze – mache sehr präzise, kurze und eindeutige Statements mit Hinweis auf die Gesprächstechnik „Pause“. Markiere „Pause“
                        explizit, wo diese hilfreich ist.
                        <button class="copy-button"
                                data-text="Redeanteil: Verkürze die Sätze – mache sehr präzise, kurze und eindeutige Statements mit Hinweis auf die Gesprächstechnik „Pause“. Markiere „Pause“ explizit, wo diese hilfreich ist.">
                            Copy
                        </button>
                    </li>
                    <li>★ Situationsdefinition – „Titel“ - Rahmensetzung des Gesprächs: Mache die Situationsdefinition und den „Titel“ / die Rahmensetzung der Verhandlung noch
                        prägnanter.
                        <button class="copy-button"
                                data-text="Situationsdefinition – „Titel“ - Rahmensetzung des Gesprächs: Mache die Situationsdefinition und den „Titel“ / die Rahmensetzung der Verhandlung noch prägnanter.">
                            Copy
                        </button>
                    </li>
                    <li>★ Situationsdefinition – „Titel“ - Rahmensetzung des Gesprächs: Formuliere für mich 3 unterschiedliche Titel / Situationsdefinitionen zur Auswahl.
                        <button class="copy-button"
                                data-text="Situationsdefinition – „Titel“ - Rahmensetzung des Gesprächs: Formuliere für mich 3 unterschiedliche Titel / Situationsdefinitionen zur Auswahl.">
                            Copy
                        </button>
                    </li>
                    <li>★ Situationsdefinition – „Titel“ - Rahmensetzung des Gesprächs: Formuliere für mich 3 unterschiedliche Titel / Situationsdefinitionen mit unterschiedlichem
                        Härtegrad: Härtegrad 1, Härtegrad 2 und Härtegrad 3.
                        <button class="copy-button"
                                data-text="Situationsdefinition – „Titel“ - Rahmensetzung des Gesprächs: Formuliere für mich 3 unterschiedliche Titel / Situationsdefinitionen mit unterschiedlichem Härtegrad: Härtegrad 1, Härtegrad 2 und Härtegrad 3.">
                            Copy
                        </button>
                    </li>
                    <li>★ Realität: Zähle die wesentlichen sachlichen und objektiven Aspekte der Business-Situation auf, welche als Fundierung Deiner Argumentation dienen sollen.
                        <button class="copy-button"
                                data-text="Realität: Zähle die wesentlichen sachlichen und objektiven Aspekte der Business-Situation auf, welche als Fundierung Deiner Argumentation dienen sollen.">
                            Copy
                        </button>
                    </li>
                    <li>★ Wertefrage: Formuliere 3 Wertefragen, um die Motivlagen des Lieferanten für Kooperation zu erfragen – z.B. „welchen Stellenwert hat die Zusammenarbeit mit
                        unserer Firma für Euch…?“
                        <button class="copy-button"
                                data-text="Wertefrage: Formuliere 3 Wertefragen, um die Motivlagen des Lieferanten für Kooperation zu erfragen – z.B. „welchen Stellenwert hat die Zusammenarbeit mit unserer Firma für Euch…?“">
                            Copy
                        </button>
                    </li>
                    <li>★ Erfolgsgeschichte: Formuliere eine Erfolgsgeschichte / die gemeinsame Erfolgsgeschichte aus der Historie mit dem Lieferanten.
                        <button class="copy-button"
                                data-text="Erfolgsgeschichte: Formuliere eine Erfolgsgeschichte / die gemeinsame Erfolgsgeschichte aus der Historie mit dem Lieferanten.">Copy
                        </button>
                    </li>
                    <li>★ Objektive Kriterien: Erwähne objektive Kriterien wie Marktlage, Inflation, Preiserhöhungen in der Vergangenheit, Rohstoffentwicklung, Logistik-Kosten
                        etc…, die für die Preisfindung relevant sind. Mache den Hinweis auf die hochgeladenen objektiven Daten.
                        <button class="copy-button"
                                data-text="Objektive Kriterien: Erwähne objektive Kriterien wie Marktlage, Inflation, Preiserhöhungen in der Vergangenheit, Rohstoffentwicklung, Logistik-Kosten etc…, die für die Preisfindung relevant sind. Mache den Hinweis auf die hochgeladenen objektiven Daten.">
                            Copy
                        </button>
                    </li>
                    <li>★ Positives Zielszenario mit Nutzen: Formuliere ein positives Zielbild und Szenario der erfolgreichen Zusammenarbeit für das Folgejahr und weiter. Zeige den
                        Nutzen für den Lieferanten in einer klaren Aufzählung auf.
                        <button class="copy-button"
                                data-text="Positives Zielszenario mit Nutzen: Formuliere ein positives Zielbild und Szenario der erfolgreichen Zusammenarbeit für das Folgejahr und weiter. Zeige den Nutzen für den Lieferanten in einer klaren Aufzählung auf.">
                            Copy
                        </button>
                    </li>
                    <li>★ Forderung als Klartext: Formuliere unsere Forderung als Klartext - glasklar und unmissverständlich. Die Forderung bezieht sich auf Preis, Rabatt,
                        Werbebeiträge, Bonus-Komponenten, Rückvergütungen etc. Stelle den Nutzen für den Lieferanten noch einmal heraus.
                        <button class="copy-button"
                                data-text="Forderung als Klartext: Formuliere unsere Forderung als Klartext - glasklar und unmissverständlich. Die Forderung bezieht sich auf Preis, Rabatt, Werbebeiträge, Bonus-Komponenten, Rückvergütungen etc. Stelle den Nutzen für den Lieferanten noch einmal heraus.">
                            Copy
                        </button>
                    </li>
                    <li>★ Level III: Formuliere die Forderung als Level III = Zwingendes Argument in der Form Kontext – Positionierung – Nutzen für den Lieferanten und unsere Firma
                        im Sinne eines Win-Win.
                        <button class="copy-button"
                                data-text="Level III: Formuliere die Forderung als Level III = Zwingendes Argument in der Form Kontext – Positionierung – Nutzen für den Lieferanten und unsere Firma im Sinne eines Win-Win.">
                            Copy
                        </button>
                    </li>
                    <li>★ Einwände / Einwandbehandlung: Formuliere jeweils x Varianten der Einwandbehandlung.
                        <button class="copy-button" data-text="Einwände / Einwandbehandlung: Formuliere jeweils x Varianten der Einwandbehandlung.">Copy</button>
                    </li>
                    <li>★ Einwände / Einwandbehandlung: Erhöhe den Härtegrad in der Bearbeitung der Einwände.
                        <button class="copy-button" data-text="Einwände / Einwandbehandlung: Erhöhe den Härtegrad in der Bearbeitung der Einwände.">Copy</button>
                    </li>
                    <li>★ Einwände / Einwandbehandlung: Kombiniere Einwandbehandlung und Nutzen. Das bedeutet: Schließe den Nutzen für den Lieferanten an den Einwand an.
                        <button class="copy-button"
                                data-text="Einwände / Einwandbehandlung: Kombiniere Einwandbehandlung und Nutzen. Das bedeutet: Schließe den Nutzen für den Lieferanten an den Einwand an.">
                            Copy
                        </button>
                    </li>
                    <li>★ Finalisierung: Fasse das Ergebnis der Verhandlung abschließend zusammen.
                        <button class="copy-button" data-text="Finalisierung: Fasse das Ergebnis der Verhandlung abschließend zusammen.">Copy</button>
                    </li>
                    <li>★ Finalisierung: Akzentuiere noch einmal, dass die Verhandlung für uns zu Ende ist.
                        <button class="copy-button" data-text="Finalisierung: Akzentuiere noch einmal, dass die Verhandlung für uns zu Ende ist.">Copy</button>
                    </li>
                    <li>★ Abschluss, Dank, Motivation: Beende die Verhandlung mit einer positiven und motivierenden Aussage.
                        <button class="copy-button" data-text="Abschluss, Dank, Motivation: Beende die Verhandlung mit einer positiven und motivierenden Aussage.">Copy</button>
                    </li>
                    <li>★ Abschluss, Dank bei Abbruch: Formuliere 3 Varianten für einen konstruktiven Abbruch („walk away statement“) der Verhandlung. Es ist wichtig, die
                        Beziehungsebene nicht zu beschädigen.
                        <button class="copy-button"
                                data-text="Abschluss, Dank bei Abbruch: Formuliere 3 Varianten für einen konstruktiven Abbruch („walk away statement“) der Verhandlung. Es ist wichtig, die Beziehungsebene nicht zu beschädigen.">
                            Copy
                        </button>
                    </li>
                    <li>Welche weiteren Vertiefungen oder Weiterentwicklungen kannst du mir empfehlen?
                        <button class="copy-button" data-text="Welche weiteren Vertiefungen oder Weiterentwicklungen kannst du mir empfehlen?">Copy</button>
                    </li>
                </ul>

                <div class="modal-actions">
                    <button id="closePromptModalButton" class="modalCancelButton">Schließen</button>
                </div>
            </div>
        </div>

        <div id="deepeningModuleModal" class="prompt-modal" style="display: none;">
            <span id="closeDeepeningModuleModal" class="close-button">&times;</span>
            <div class="prompt-modal-content">
                <h3 class="modal-title">Module für Vertiefungen</h3>
                <ul id="deepening-module-list">
                    <li>1. <b>Erhöhung des Härtegrades:</b> Entwickle Vorschläge und Formulierungen zur Steigerung des Härtegrades der Forderungen, einschließlich konkreter
                        Taktiken für eine stärkere Durchsetzung
                        <button class="copy-button"
                                data-text="Erhöhung des Härtegrades: Entwickle Vorschläge und Formulierungen zur Steigerung des Härtegrades der Forderungen, einschließlich konkreter Taktiken für eine stärkere Durchsetzung">
                            Copy
                        </button>
                    </li>
                    <li>2. <b>Bessere Nutzung der Realität:</b> Erarbeite Methoden, um die Faktenbasis effektiver zur Untermauerung der Verhandlungsposition zu nutzen,
                        einschließlich der Darstellung von gemeinsamer Historie, aktueller Situation, Marktdaten, Kostenanalysen und Leistungsbenchmarks
                        <button class="copy-button"
                                data-text="Bessere Nutzung der Realität: Erarbeite Methoden, um die Faktenbasis effektiver zur Untermauerung der Verhandlungsposition zu nutzen, einschließlich der Darstellung von gemeinsamer Historie, aktueller Situation, Marktdaten, Kostenanalysen und Leistungsbenchmarks">
                            Copy
                        </button>
                    </li>
                    <li>3. <b>Entwicklung einer Sog- und Druckstrategie:</b> Formuliere eine klare Strategie, die Sog und Druck auf den Verhandlungspartner ausübt, um dessen
                        Zustimmungsbereitschaft zu erhöhen
                        <button class="copy-button"
                                data-text="Entwicklung einer Sog- und Druckstrategie: Formuliere eine klare Strategie, die Sog und Druck auf den Verhandlungspartner ausübt, um dessen Zustimmungsbereitschaft zu erhöhen">
                            Copy
                        </button>
                    </li>
                    <li>4. <b>Varianten der Einwandbehandlung:</b> Stelle ein Repertoire an Antworten für typische Einwände des Lieferanten zusammen, basierend auf einer
                        systematischen Einwandbehandlung in drei Schritten: Bestätigung, Argument, Pause/Frage
                        <button class="copy-button"
                                data-text="Varianten der Einwandbehandlung: Stelle ein Repertoire an Antworten für typische Einwände des Lieferanten zusammen, basierend auf einer systematischen Einwandbehandlung in drei Schritten: Bestätigung, Argument, Pause/Frage">
                            Copy
                        </button>
                    </li>
                    <li>5. <b>Systematische Argumente gegen Preisforderungen:</b> Formuliere fundierte Argumente gegen Preisforderungen des Lieferanten, inklusive der Vorbereitung
                        von Gegenargumenten
                        <button class="copy-button"
                                data-text="Systematische Argumente gegen Preisforderungen: Formuliere fundierte Argumente gegen Preisforderungen des Lieferanten, inklusive der Vorbereitung von Gegenargumenten">
                            Copy
                        </button>
                    </li>
                    <li>6. <b>Verfeinerung der Verhaltenstechniken:</b> Verbessere die Verhaltenstechniken des Benutzers in den Bereichen Freundlichkeit, Klarheit, Bestimmtheit und
                        Eskalation.
                        <button class="copy-button"
                                data-text="Verfeinerung der Verhaltenstechniken: Verbessere die Verhaltenstechniken des Benutzers in den Bereichen Freundlichkeit, Klarheit, Bestimmtheit und Eskalation.">
                            Copy
                        </button>
                    </li>
                    <li>7. <b>Antizipation möglicher Angriffe:</b> Plane Vorgehensweisen und Reaktionen auf mögliche Angriffe oder Killerphrasen des Lieferanten
                        <button class="copy-button"
                                data-text="Antizipation möglicher Angriffe: Plane Vorgehensweisen und Reaktionen auf mögliche Angriffe oder Killerphrasen des Lieferanten">Copy
                        </button>
                    </li>
                    <li>8. <b>Vorbereitung aus einer starken oder schwachen Position:</b> Entwickle Strategien, die sowohl aus einer starken als auch aus einer schwachen Position
                        heraus wirksam sind, einschließlich der Anpassung der Verhandlungstaktik an die jeweilige Machtkonstellation
                        <button class="copy-button"
                                data-text="Vorbereitung aus einer starken oder schwachen Position: Entwickle Strategien, die sowohl aus einer starken als auch aus einer schwachen Position heraus wirksam sind, einschließlich der Anpassung der Verhandlungstaktik an die jeweilige Machtkonstellation">
                            Copy
                        </button>
                    </li>
                </ul>
                <div class="modal-actions">
                    <button id="closeDeepeningModuleModalButton" class="modalCancelButton">Schließen</button>
                </div>
            </div>
        </div>

        <div id="promptServiceModal" class="prompt-modal" style="display:none;">
            <div class="prompt-modal-content">
                <span id="closePromptServiceModal" class="close-button" style="float:right;cursor:pointer;font-size:24px;">&times;</span>
                <h2>Prompt‑Service</h2>

                <!-- search / filter bar -->
                <div style="margin-bottom:1rem;display:flex;gap:.5rem;">
                    <input id="promptSearchInput" type="text" placeholder="Suchbegriff eingeben …" style="flex:1;padding:.45rem;"/>
                    <button id="searchPromptsButton" class="button-primary">Suchen</button>
                </div>

                <!-- result table -->
                <div style="overflow:auto;max-height:60vh;">
                    <table id="promptTable" style="width:100%;border-collapse:collapse;">
                        <thead>
                        <tr>
                            <th style="text-align:left;">Title</th>
                            <th style="text-align:left;">Content</th>
                            <th style="text-align:left;">Action</th>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>

                <!-- currently selected prompts – each on its own line -->
                <h3 style="margin-top:1.5rem;">Gewählte Prompts</h3>
                <pre id="selectedPromptsContainer" class="selected-prompts"
                     style="background:#f7f7f7;padding:.75rem;border-radius:6px;white-space:pre-wrap;max-height:25vh;overflow:auto;"></pre>
            </div>
        </div>


    </div>

    <!-- RIGHT SIDEBAR (Relevant Documents) -->
    <div class="relevant-documents" id="relevant-documents">
        <div class="element-icons-close-wrapper">
            <button class="closeRelevantDocumentsButton" onclick="closeRelevantDocumentsSideBar()"><img class="img-2" src="/static/img/right-sidebar/close-icon.svg"/></button>
        </div>
        <div class="relevant-docs-wrapper" id="relevant-docs-wrapper">
        </div>
    </div><!-- END .relevant-documents -->

</div><!-- END .layout-container -->


<div class="legal-footer">
  <a href="/footer/datenschutzerklaerung.html" target="_blank">Datenschutzerklärung</a>
  <span>|</span>
  <a href="/footer/impressum.html" target="_blank">Impressum</a>
  <span>|</span>
  <a href="/footer/nutzungsbedingungen.html" target="_blank">Nutzungsbedingungen</a>
</div>



<!-- Various modals remain the same -->

<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/dompurify@2.4.0/dist/purify.min.js"></script>

<script src="/static/js/main.js"></script>
</body>
</html>
