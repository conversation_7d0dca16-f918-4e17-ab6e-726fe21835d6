---
volumes:
  pgdata_loader:
  pgdata_indexer:
  pgdata_query:
  rabbitmqdata:

networks:
  backend:
  frontend:

services:
  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq
    restart: always
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RA<PERSON><PERSON>MQ_DEFAULT_USER: guest
      <PERSON><PERSON><PERSON><PERSON><PERSON>_DEFAULT_PASS: guest
      <PERSON><PERSON><PERSON><PERSON><PERSON>_LOGS: debug
    volumes:
      - rabbitmqdata:/var/lib/rabbitmq
    networks:
      - backend

  redis:
    image: redis:alpine
    container_name: redis
    command: redis-server --loglevel warning
    restart: always
    ports:
      - "${REDIS_PORT:-6379}:${REDIS_PORT:-6379}"
    volumes:
      - ./data/redis:/data
    networks:
      - backend

  postgres_indexer:
    image: pgvector/pgvector:pg16
    container_name: postgres_indexer
    restart: always
    ports:
      - "5433:5432"
    env_file:
      - IndexerService/.env
    volumes:
      - pgdata_indexer:/var/lib/postgresql/data
    networks:
      - backend

  postgres_loader:
    image: postgres:16
    container_name: postgres-loader
    restart: always
    ports:
      - "5434:5432"
    env_file:
      - LoaderService/.env
    volumes:
      - pgdata_loader:/var/lib/postgresql/data
    networks:
      - backend

  postgres_query:
    image: postgres:16
    container_name: postgres-query
    restart: always
    ports:
      - "5435:5432"
    env_file:
      - QueryService/.env
    volumes:
      - pgdata_query:/var/lib/postgresql/data
    networks:
      - backend

  indexer-worker:
    build:
      context: .
      dockerfile: IndexerWorker/Dockerfile
    container_name: indexer-worker
    env_file:
      - IndexerWorker/.env
    depends_on:
      - rabbitmq
      - postgres_indexer
    networks:
      - backend

  loader-worker:
    build:
      context: .
      dockerfile: LoaderWorker/Dockerfile
    container_name: loader-worker
    env_file:
      - LoaderWorker/.env
    depends_on:
      - rabbitmq
      - postgres_loader
    networks:
      - backend
    volumes:
      - ./data/pdfs:/data

  indexer:
    build:
      context: .
      dockerfile: IndexerService/Dockerfile
    container_name: indexer-service
    env_file:
      - IndexerService/.env
    ports:
      - "8001:8001"
    depends_on:
      - rabbitmq
      - postgres_indexer
      - redis
    networks:
      - backend
      - frontend
    volumes:
      - ./data/pdfs:/data
      - ./IndexerService:/app

  loader:
    build:
      context: .
      dockerfile: LoaderService/Dockerfile
    container_name: loader-service
    env_file:
      - LoaderService/.env
    ports:
      - "8002:8002"
    depends_on:
      - rabbitmq
      - postgres_loader
      - redis
    networks:
      - backend
      - frontend
    volumes:
      - ./data/loader/tmp:/indexer_app/data/tmp
      - ./data/pdfs:/data
      - ./LoaderService:/app
      - ./LoaderService/pyproject.toml:/app/pyproject.toml

  query:
    build:
      context: .
      dockerfile: QueryService/Dockerfile
    container_name: query-service
    env_file:
      - QueryService/.env
    ports:
      - "8003:8003"
    depends_on:
      - rabbitmq
      - postgres_query
      - redis
    networks:
      - backend
      - frontend
    volumes:
      - ./QueryService:/app

  mcp-query:
    build:
      context: .
      dockerfile: MCPQuery/Dockerfile
    container_name: mcp-query
    env_file:
      - MCPQuery/.env
    ports:
      - "8004:8004"
    depends_on:
      - query
    networks:
      - backend
      - frontend

  admin-ui:
    build:
      context: .
      dockerfile: AdminUI/Dockerfile
    container_name: admin-ui
    env_file:
      - AdminUI/.env
    depends_on:
      - loader
    ports:
      - "8501:8501"
    networks:
      - frontend
      - backend
    volumes:
      - ./AdminUI:/indexer_app
      - ./data:/data

  clientui:
    build:
      context: .
      dockerfile: ClientUI/Dockerfile
    container_name: clientui
    ports:
      - "8006:8006"
    env_file:
      - ClientUI/.env
    depends_on:
      - query
      - loader
      - indexer
      - postgres_query
      - postgres_loader
      - postgres_indexer
      - redis
    volumes:
      - ./ClientUI:/usr/share/nginx/html
    networks:
      - backend
      - frontend

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.13.4
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - ES_JAVA_OPTS=-Xms1g -Xmx1g
    ports:
      - "9200:9200"
    networks:
      - backend
