{"id": "query-mcp", "version": "1.0.0", "auth": {"type": "bearer"}, "capabilities": [{"name": "vector_search", "description": "Run a semantic search and return top-k chunks", "args_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "query": {"type": "string"}, "top_k": {"type": "integer", "default": 5}, "search_method": {"type": "string", "enum": ["similarity", "mmr", "keyword", "hybrid"]}, "session_id": {"type": "string"}, "inline_pdf_content": {"type": "string"}, "folder_name": {"type": "string"}, "markdown_support_on": {"type": "boolean"}}, "required": ["collection_id", "query"]}}, {"name": "vector_search_stream", "description": "Same search but streaming chunks", "args_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "query": {"type": "string"}, "top_k": {"type": "integer", "default": 5}, "stream": {"type": "boolean", "default": true}, "search_method": {"type": "string", "enum": ["similarity", "mmr", "keyword", "hybrid"]}, "session_id": {"type": "string"}, "inline_pdf_content": {"type": "string"}, "folder_name": {"type": "string"}, "markdown_support_on": {"type": "boolean"}}, "required": ["collection_id", "query"]}}, {"name": "vector_search_update", "description": "Update an existing query", "args_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "query_id": {"type": "string"}, "adjust_type": {"type": "string", "enum": ["more_detail", "less_detail", "shorter_answer", "longer_answer"]}, "search_method": {"type": "string", "enum": ["similarity", "mmr", "keyword", "hybrid"]}, "session_id": {"type": "string"}, "inline_pdf_content": {"type": "string"}, "stream": {"type": "boolean"}}, "required": ["collection_id", "query_id"]}}, {"name": "get_retrieved_docs", "description": "Return previously retrieved documents for a query", "args_schema": {"type": "object", "properties": {"collection_id": {"type": "string"}, "query": {"type": "string"}, "top_k": {"type": "integer", "default": 5}, "search_method": {"type": "string", "enum": ["similarity", "mmr", "keyword", "hybrid"]}, "session_id": {"type": "string"}, "inline_pdf_content": {"type": "string"}, "folder_name": {"type": "string"}, "markdown_support_on": {"type": "boolean"}}, "required": ["collection_id", "query"]}}]}