#!/usr/bin/env python3
"""
MCP‑COMPLIANT Gateway (side‑car + API gateway)
––––––––––––––––––––––––––––––––––––––––––––––
* Exposes a single **/mcp** endpoint that speaks the *Model Context Protocol* (JSON‑RPC 2.0).
* Wraps four Query‑Service REST operations as MCP **tools**.
* Keeps a transparent catch‑all proxy for every other path so existing UI routes keep working.
* Preserves the original special‑case redirect for Azure AD callback (redeemed code → 307 → 302).
* Adds:
  – JSON‑RPC dispatcher with `initialize`, `tools/list`, `tools/call`.
  – Optional session header handling (`Mcp‑Session‑Id`).
  – Strict `Origin` validation.
  – Up‑to‑date `/manifest` serving protocolVersion, tools, etc.
"""

from __future__ import annotations

import asyncio
import json
import logging
import os
import time
import uuid
from pathlib import Path
from typing import Any, Dict, List, Tuple
from urllib.parse import urlparse

import aiohttp
from fastapi import FastAPI, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse
from fastapi.responses import Response as FAResponse
from fastapi.responses import StreamingResponse
from prometheus_client import (
    CONTENT_TYPE_LATEST,
    CollectorRegistry,
    Counter,
    Histogram,
    generate_latest,
    multiprocess,
)
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.middleware.gzip import GZipMiddleware
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

# ─────────────────── Configuration ────────────────────────────────────────────────
QUERY_SERVICE_URL = os.getenv("QUERY_SERVICE_URL", "http://query:8003")
CLIENT_UI_URL = os.getenv("CLIENT_UI_URL", "http://localhost:8005")
PORT = int(os.getenv("PORT", "8004"))
TIMEOUT = int(os.getenv("TIMEOUT", "30"))
MAX_RETRIES = int(os.getenv("MAX_RETRIES", "3"))
MAX_PAYLOAD = int(os.getenv("MAX_REQUEST_SIZE", "10485760"))
RATE_LIMIT = int(os.getenv("RATE_LIMIT", "1000"))
ENABLE_METRICS = os.getenv("ENABLE_METRICS", "false").lower() == "true"
ALLOWED_ORIGINS = {urlparse(CLIENT_UI_URL).netloc}
PROTOCOL_VERSION = "2025-03-26"  # MCP spec version we speak

logging.basicConfig(
    level=os.getenv("LOG_LEVEL", "INFO"),
    format="%(asctime)s | %(levelname)s | %(name)s | %(message)s",
)
log = logging.getLogger("mcp-gateway")

# ─────────────────── Manifest & tool registry ─────────────────────────────────────
MANIFEST_PATH = Path(os.getenv("MANIFEST_PATH", "manifest.json"))
if MANIFEST_PATH.exists():
    _raw_manifest = json.loads(MANIFEST_PATH.read_text())
else:
    raise RuntimeError("manifest.json missing – cannot start gateway")

TOOLS: Dict[str, Dict[str, Any]] = {cap["name"]: cap for cap in _raw_manifest["capabilities"]}

# ----------------------------------------------------------------------------
# Helper – CAP → internal forwarding mapping
# ----------------------------------------------------------------------------


def _b(body: dict, src: dict, key_map: dict):
    """Build JSON payload translating keys defined in *key_map*."""
    for k, v in key_map.items():
        if k in src:
            body[v] = src[k]
    # copy untouched keys
    for k in src:
        if k not in key_map:
            body[k] = src[k]
    return body


CAP_TABLE: Dict[str, Tuple[str, str, Any]] = {
    "vector_search": (
        "POST",
        "/collection/{cid}/query",
        lambda a: _b({}, a, {"query": "content"}),
    ),
    "vector_search_stream": (
        "POST",
        "/collection/{cid}/query_stream",
        lambda a: _b({"stream": True}, a, {"query": "content"}),
    ),
    "vector_search_update": (
        "PUT",
        "/collection/{cid}/query_update",
        lambda a: _b({}, a, {"query": "custom_prompt"}),
    ),
    "get_retrieved_docs": (
        "POST",
        "/collection/{cid}/query/retrieved_docs",
        lambda a: _b({}, a, {"query": "content"}),
    ),
}

# ─────────────────── FastAPI app ─────────────────────────────────────────────────
app = FastAPI(
    title="MCP Gateway – Query",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # UI already sets Origin header – we validate separately
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.add_middleware(GZipMiddleware, minimum_size=1024)

# ─────────────────── Middleware: security, logging, rate‑limit ────────────────────


class SecurityHeaders(BaseHTTPMiddleware):
    """Middleware to add security headers to the response."""

    async def dispatch(self, request, call_next):
        """Add security headers to the response."""
        response = await call_next(request)
        response.headers.update(
            {
                "X-Content-Type-Options": "nosniff",
                "X-Frame-Options": "DENY",
                "X-XSS-Protection": "1; mode=block",
                "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
                "Content-Security-Policy": "default-src 'self'",
            }
        )
        return response


class OriginCheck(BaseHTTPMiddleware):
    """Middleware to check the request's Origin header against allowed origins."""

    async def dispatch(self, request, call_next):
        """Check the request's Origin header against allowed origins."""
        origin = request.headers.get("origin")
        if origin and urlparse(origin).netloc not in ALLOWED_ORIGINS:
            raise HTTPException(status.HTTP_403_FORBIDDEN, "Forbidden origin")
        return await call_next(request)


class RequestLog(BaseHTTPMiddleware):
    """Middleware to log requests and responses with a unique request ID."""

    async def dispatch(self, request, call_next):
        """Log the request method and path, and add a unique request ID to the response headers."""
        rid = request.headers.get("X-Request-ID", uuid.uuid4().hex)
        log.info(f"[{rid}] ▶ {request.method} {request.url.path}")
        response = await call_next(request)
        log.info(f"[{rid}] ◀ {response.status_code}")
        response.headers["X-Request-ID"] = rid
        return response


class RateLimiter(BaseHTTPMiddleware):
    """Middleware to limit requests per IP address."""

    def __init__(self, app, limit: int):
        """Initialize the rate limiter with a request limit."""
        super().__init__(app)
        self.limit = limit
        self.history: Dict[str, List[float]] = {}

    async def dispatch(self, request, call_next):
        """Check rate limit for the request based on IP address."""
        ip = request.client.host
        now = time.time()
        window = [t for t in self.history.get(ip, []) if now - t < 60]
        if len(window) >= self.limit:
            raise HTTPException(status.HTTP_429_TOO_MANY_REQUESTS, "Rate limit exceeded")
        window.append(now)
        self.history[ip] = window
        return await call_next(request)


app.add_middleware(SecurityHeaders)
app.add_middleware(OriginCheck)
app.add_middleware(RequestLog)
app.add_middleware(RateLimiter, limit=RATE_LIMIT)

# ─────────────────── Metrics ──────────────────────────────────────────────────────
registry = CollectorRegistry()
if ENABLE_METRICS and os.getenv("PROMETHEUS_MULTIPROC_DIR"):
    multiprocess.MultiProcessCollector(registry)
REQ = Counter("gw_req_total", "Total requests", ["m", "e", "s"], registry=registry)
LAT = Histogram("gw_latency_seconds", "Latency", ["m", "e"], registry=registry)


class Metrics(BaseHTTPMiddleware):
    """Middleware to collect request metrics."""

    async def dispatch(self, request, call_next):
        """Collect metrics for each request."""
        start = time.time()
        response = await call_next(request)
        if ENABLE_METRICS:
            REQ.labels(request.method, request.url.path, response.status_code).inc()
            LAT.labels(request.method, request.url.path).observe(time.time() - start)
        return response


app.add_middleware(Metrics)

# ─────────────────── Helper – forward to Query‑Service ────────────────────────────


@retry(
    stop=stop_after_attempt(MAX_RETRIES),
    wait=wait_exponential(1, 2, 8),
    retry=retry_if_exception_type((asyncio.TimeoutError, aiohttp.ClientError)),
)
async def forward(method: str, path: str, headers: Dict[str, str], body: bytes):
    """Forward request to Query‑Service with retries and rate limiting."""
    log.debug("⇢ %s %s (%d B)", method, path, len(body))
    if len(body) > MAX_PAYLOAD:
        raise HTTPException(status.HTTP_413_REQUEST_ENTITY_TOO_LARGE, "Payload too large")

    async with aiohttp.ClientSession() as sess:
        async with sess.request(
            method,
            f"{QUERY_SERVICE_URL}{path}",
            headers=headers,
            data=body,
            timeout=TIMEOUT,
            allow_redirects=False,
        ) as resp:
            # streaming?
            if resp.headers.get("content-type", "").startswith("text/event-stream"):
                return StreamingResponse(
                    resp.content.iter_any(),
                    media_type="text/event-stream",
                    headers=dict(resp.headers),
                )

            raw = await resp.read()
            log.debug("⇠ %s %s (%d B)", method, path, resp.status)

            # special Azure‑AD callback handling
            if path.startswith("/callback") and resp.status in {
                status.HTTP_301_MOVED_PERMANENTLY,
                status.HTTP_302_FOUND,
                status.HTTP_303_SEE_OTHER,
                status.HTTP_307_TEMPORARY_REDIRECT,
                status.HTTP_308_PERMANENT_REDIRECT,
            }:
                loc = resp.headers.get("location")
                if resp.status == status.HTTP_307_TEMPORARY_REDIRECT and loc and loc.startswith(CLIENT_UI_URL):
                    return RedirectResponse(url=loc, status_code=status.HTTP_302_FOUND)

            return FAResponse(raw, status_code=resp.status, media_type=resp.headers.get("content-type"))


# ─────────────────── JSON‑RPC dispatcher ──────────────────────────────────────────


class JsonRpcError(Exception):
    """Custom exception for JSON‑RPC errors."""

    def __init__(self, code: int, message: str, data: Any | None = None):
        """Initialize a JSON‑RPC error with code, message, and optional data."""
        super().__init__(message)
        self.code, self.message, self.data = code, message, data


class Rpc:
    """Helper class for JSON‑RPC responses."""

    @staticmethod
    def success(id_: Any, result: Any):
        """Create a successful JSON‑RPC response."""
        return {"jsonrpc": "2.0", "id": id_, "result": result}

    @staticmethod
    def error(id_: Any | None, err: JsonRpcError):
        """Create a JSON‑RPC error response."""
        resp: Dict[str, Any] = {
            "jsonrpc": "2.0",
            "id": id_,
            "error": {"code": err.code, "message": err.message},
        }
        if err.data is not None:
            resp["error"]["data"] = err.data
        return resp


# ─────────────────── /mcp endpoint ────────────────────────────────────────────────


@app.post("/mcp")
async def mcp_endpoint(request: Request):
    """Main MCP endpoint – handles JSON‑RPC 2.0 messages."""
    msg = await request.json()
    if not isinstance(msg, dict) or msg.get("jsonrpc") != "2.0":
        raise HTTPException(status.HTTP_400_BAD_REQUEST, "Invalid JSON‑RPC 2.0 message")

    method = msg.get("method")
    id_ = msg.get("id")
    params = msg.get("params", {})

    try:
        if method == "initialize":
            # handshake – respond with capabilities & protocol version
            result = {
                "protocolVersion": PROTOCOL_VERSION,
                "serverInfo": {"name": "Query‑Gateway", "version": "2.0.0"},
                "capabilities": {"tools": {"listChanged": False}},
            }
            return Rpc.success(id_, result)

        if method == "tools/list":
            return Rpc.success(id_, list(TOOLS.values()))

        if method == "tools/call":
            if not isinstance(params, dict):
                raise JsonRpcError(-32602, "Invalid params – expected object")
            tool_name = params.get("name")
            tool_args = params.get("args", {})
            if tool_name not in CAP_TABLE:
                raise JsonRpcError(-32601, f"Tool '{tool_name}' not found")
            if "collection_id" not in tool_args:
                raise JsonRpcError(-32602, "'collection_id' missing in args")

            method_, tpl, build = CAP_TABLE[tool_name]
            body = json.dumps(build(tool_args)).encode()
            path = tpl.format(cid=tool_args["collection_id"])

            # pass through user headers except Host and Content‑Length
            hdrs = {k.decode(): v.decode() for k, v in request.headers.raw if k.lower() != b"host" and k.lower() != b"content-length"}
            hdrs.setdefault("content-type", "application/json")

            # propagate session header if present (optional)
            sess_id = request.headers.get("Mcp-Session-Id")
            if sess_id:
                hdrs["Mcp-Session-Id"] = sess_id

            result = await forward(method_, path, hdrs, body)

            # FastAPI StreamingResponse is already a Response – return directly
            return result

        # unknown method
        raise JsonRpcError(-32601, "Method not found")

    except JsonRpcError as exc:
        return Rpc.error(id_, exc)
    except HTTPException as exc:
        return Rpc.error(id_, JsonRpcError(exc.status_code, exc.detail))
    except Exception as exc:  # noqa: BLE001
        log.exception("Unhandled error in /mcp")
        return Rpc.error(id_, JsonRpcError(-32603, "Internal error"))


# ─────────────────── Legacy /invoke (will be removed in v3) ──────────────────────


@app.post("/invoke", deprecated=True)
async def invoke_legacy(request: Request):
    """Legacy wrapper kept for backward compatibility (MCP > /mcp)."""
    payload = await request.json()
    cap_id, args = payload.get("id"), payload.get("args", {})
    if cap_id not in CAP_TABLE:
        raise HTTPException(status.HTTP_404_NOT_FOUND, "Capability not found")
    if "collection_id" not in args:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, "collection_id missing")

    method, tpl, build = CAP_TABLE[cap_id]
    body = json.dumps(build(args)).encode()
    path = tpl.format(cid=args["collection_id"])

    hdrs = {k.decode(): v.decode() for k, v in request.headers.raw if k.lower() not in {b"host", b"content-length"}}
    hdrs.setdefault("content-type", "application/json")
    return await forward(method, path, hdrs, body)


# ─────────────────── Transparent proxy (non‑MCP paths) ───────────────────────────


@app.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"])
async def proxy(request: Request, path: str):
    """Catch‑all proxy for non‑MCP paths – forwards to Query‑Service."""
    body = await request.body()
    target_path = f"/{path}"
    if request.url.query:
        target_path += f"?{request.url.query}"
    hdrs = {k.decode(): v.decode() for k, v in request.headers.raw if k.lower() not in {b"host", b"content-length"}}
    return await forward(request.method, target_path, hdrs, body)


# ─────────────────── Health & metrics ─────────────────────────────────────────────


@app.get("/health", include_in_schema=False)
async def health():
    """Health check endpoint."""
    return {"status": "ok"}


@app.get("/metrics", include_in_schema=False)
async def metrics():
    """Prometheus metrics endpoint."""
    if not ENABLE_METRICS:
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="disabled")
    return FAResponse(generate_latest(registry), CONTENT_TYPE_LATEST)


# ─────────────────── Entrypoint ───────────────────────────────────────────────────

if __name__ == "__main__":
    import uvicorn

    uvicorn.run("gateway:app", host="0.0.0.0", port=PORT, reload=False)
