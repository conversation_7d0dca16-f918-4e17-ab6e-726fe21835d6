############################
# 1.  Builder stage        #
############################
FROM python:3.11-slim AS builder

WORKDIR /src

# System packages required only while building wheels
RUN apt-get update && apt-get install -y --no-install-recommends \
        build-essential gcc && \
    rm -rf /var/lib/apt/lists/*

# Pre‑build Python dependencies into wheels
COPY MCPQuery/requirements.txt requirements.txt

RUN --mount=type=cache,target=/root/.cache/pip \
    pip wheel -r requirements.txt --wheel-dir /wheels

############################
# 2. Runtime stage         #
############################
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Create Prometheus multiprocessing directory
RUN mkdir -p /tmp/prometheus_multiproc && chmod 777 /tmp/prometheus_multiproc

# Create non-root user
RUN adduser --disabled-password --gecos "" appuser

# Copy requirements first to leverage Docker cache
COPY MCPQuery/requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir -r /app/requirements.txt

# Copy the rest of the application
COPY MCPQuery/ /app/

# Change ownership to appuser
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Expose the port
EXPOSE 8004

# Health check endpoint
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s CMD \
  curl -f http://localhost:8004/health || exit 1

# Run the application
CMD ["uvicorn", "gateway:app", "--host", "0.0.0.0", "--port", "8004"]
